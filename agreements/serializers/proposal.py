from rest_framework import serializers
from accounts.models.organisation import Organization
from agreements.models.proposal_models import Proposal, TransporterDetails, Item
from accounts.models import User
from documents.models.document_models import OrganisationContacts


class ProposalFromSerializers(serializers.Serializer):
    organization_name = serializers.Char<PERSON><PERSON>(source="name")  # maps to Organization.name
    contact_number = serializers.CharField(source="creator_user.phone_number")
    email = serializers.EmailField(source="creator_user.email")


class ProposalForSerializers(serializers.Serializer):
    client_organization_name = serializers.Char<PERSON>ield(source="name")
    contact_number = serializers.Char<PERSON>ield(source="phone_number")
    email = serializers.EmailField()


class TransporterDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = TransporterDetails
        fields = "__all__"


class ItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = Item
        fields = "__all__"


class ProposalSerializer(serializers.ModelSerializer):
    proposal_from = ProposalFromSerializers()
    proposal_for = ProposalForSerializers()
    transporter_details = TransporterDetailsSerializer()
    items = ItemSerializer()

    class Meta:
        model = Proposal
        fields = ['id', 'proposal_id', 'proposal_title', 'proposal_from', 'proposal_for', 'vat_tax_and_currency', 'transporter_details', 'items', 'terms_and_conditions', 'essential_information', 'additional_information', 'footer_information', 'proposal_document_file', 'proposal_expires_at']

    def create(self, validated_data):
        request = self.context.get("request")

        # -----------------------
        # Proposal From
        # -----------------------
        print("\n\n")
        user_data = validated_data.pop("proposal_from")
        print("user_data: ", user_data)

        user_obj = User.objects.filter(email=user_data["creator_user"]["email"]).first()
        print("user_obj : ", user_obj)


        user_org_obj= Organization.objects.get(
            name=user_data.get("organization_name"),
            creator_user=user_obj
        )
        print("user_org_obj: ", user_org_obj)

        if not user_org_obj:
            raise serializers.ValidationError("Organization not found for this user.")

        # -----------------------
        # Proposal For
        # -----------------------
        org_data = validated_data.pop("proposal_for")
        print("org_data : ", org_data)

        client_org_contact_obj = OrganisationContacts.objects.filter(
            name=org_data.get("name"),
            email=org_data.get("email"),
            phone_number=org_data.get("phone_number")
        ).first()
        print("client_org_contact_obj: ", client_org_contact_obj)
        # -----------------------
        # Transporter & Items
        # -----------------------
        transporter = TransporterDetails.objects.create(**validated_data.pop("transporter_details"))
        print("transporter: ", transporter)
        print("\n\n")   

        item = Item.objects.create(**validated_data.pop("items"))

        # -----------------------
        # Final Proposal
        # -----------------------
        proposal = Proposal.objects.create(
            proposal_from=user_org_obj,
            proposal_for=client_org_contact_obj,
            transporter_details=transporter,
            items=item,
            **validated_data
        )
        return proposal
