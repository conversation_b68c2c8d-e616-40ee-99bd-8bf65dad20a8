from rest_framework import serializers
from accounts.models.organisation import Organization
from agreements.models.proposal_models import Proposal, TransporterDetails, Item
from accounts.models import User
from documents.models.document_models import OrganisationContacts


class ProposalFromSerializers(serializers.Serializer):
    organization_name = serializers.CharField(source="name")
    contact_number = serializers.CharField(source="creator_user.phone_number")
    email = serializers.EmailField(source="creator_user.email")


class ProposalForSerializers(serializers.Serializer):
    client_organization_name = serializers.CharField(source="name")
    contact_number = serializers.CharField(source="phone_number")
    email = serializers.EmailField()


class TransporterDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = TransporterDetails
        fields = "__all__"


class ItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = Item
        fields = "__all__"


class ProposalSerializer(serializers.ModelSerializer):
    proposal_from = ProposalFromSerializers(required=False)
    proposal_for = ProposalForSerializers(required=False)
    transporter_details = TransporterDetailsSerializer(required=False)
    items = ItemSerializer(required=False)
    proposal_status = serializers.Char<PERSON>ield(read_only=True)

    class Meta:
        model = Proposal
        fields = ['id', 'proposal_id', 'proposal_title', 'proposal_status', 'proposal_from', 'proposal_for', 'vat_tax_and_currency','transporter_details', 'items', 'terms_and_conditions', 'essential_information', 'additional_information', 'footer_information', 'proposal_logo_file', 'proposal_expires_at']

    def create(self, validated_data):
        request = self.context.get("request")
        user_data = request.data.get("proposal_from")
        user_obj = User.objects.filter(email=user_data["email"]).first()

        user_org_obj= Organization.objects.filter(
            name=user_data.get("name"),
            creator_user=user_obj
        ).first()
        if not user_org_obj:
            raise serializers.ValidationError("Organization not found for this user.")

        org_data = request.data.get("proposal_for")
        client_org_contact_obj = OrganisationContacts.objects.filter(
            name=org_data.get("name"),
            email=org_data.get("email"),
            phone_number=org_data.get("phone_number")
        ).first()
        transporter = TransporterDetails.objects.create(**request.data.get("transporter_details"))
        item = Item.objects.create(**request.data.get("items"))

        proposal = Proposal.objects.create(
            proposal_from=user_org_obj,
            proposal_for=client_org_contact_obj,
            transporter_details=transporter,
            items=item,
            **validated_data
        )
        return proposal


class ProposalDashboardSerializer(serializers.ModelSerializer):
    draft_count = serializers.SerializerMethodField()
    sent_count = serializers.SerializerMethodField()
    signed_count = serializers.SerializerMethodField()
    all_count = serializers.SerializerMethodField()
    proposal_data = ProposalSerializer(many=True)

    def get_draft_count(self, obj):
        return Proposal.objects.filter(proposal_status="draft").count()

    def get_sent_count(self, obj):
        return Proposal.objects.filter(proposal_status="sent").count()

    def get_signed_count(self, obj):
        return Proposal.objects.filter(proposal_status="signed").count()
    
    def get_all_count(self, obj):
        return Proposal.objects.all().count()
    
    class Meta:
        model = Proposal
        fields = ['draft_count', 'sent_count', 'signed_count', 'all_count', 'proposal_data']
