from datetime import timed<PERSON><PERSON>
from django.utils import timezone
import json
from rest_framework import serializers
from accounts.models.organisation import Organization
from agreements.models.proposal_models import Proposal, TransporterDetails, Item, ShippedDetails
from agreements.models.sales_room_models import SalesRoom
from documents.models.document_models import OrganisationContacts
from typing import Any, Dict, List, Optional, Union
from cryptography.fernet import Fernet
from django.conf import settings

cipher_suite = Fernet(settings.ENCRYPTION_KEY)

class ProposalFromSerializers(serializers.Serializer):
    organization_name = serializers.Char<PERSON>ield(source="get_decrypted_name", allow_null=True, required=False, default=None)
    contact_number = serializers.CharField(source="get_organization_owner.get_decrypted_phone_number", allow_null=True, required=False, default=None)
    email = serializers.Char<PERSON>ield(source="get_organization_owner.get_decrypted_email", allow_null=True, required=False, default=None)


class ProposalForSerializers(serializers.Serializer):
    client_organization_name = serializers.Char<PERSON>ield(source="name")
    contact_number = serializers.CharField(source="phone_number")
    email = serializers.EmailField()
    assigner_org_name = serializers.CharField()


class TransporterDetailsSerializer(serializers.ModelSerializer):
    transporter_id = serializers.CharField()

    class Meta:
        model = TransporterDetails
        fields = [
            "id",
            "transporter_id",
            "name",
            "mode_of_transport",
            "transport_doc_date",
            "vehicle_number",
            "vehicle_type",
        ]


class ItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = Item
        fields = "__all__"


class ShippingAddressSerializer(serializers.Serializer):
    """Serializer for shipping address data from frontend"""
    business_name = serializers.CharField(required=False, allow_blank=True)
    client_name = serializers.CharField(required=False, allow_blank=True)
    address = serializers.CharField(required=False, allow_blank=True)
    city = serializers.CharField(required=False, allow_blank=True)
    state = serializers.CharField(required=False, allow_blank=True)
    country = serializers.CharField(required=False, allow_blank=True)
    pincode = serializers.CharField(required=False, allow_blank=True)


class ShippedDetailsSerializer(serializers.ModelSerializer):
    """Serializer for ShippedDetails model"""
    from_address_full = serializers.ReadOnlyField()
    to_address_full = serializers.ReadOnlyField()
    
    class Meta:
        model = ShippedDetails
        fields = '__all__'
        read_only_fields = ['id', 'created_at', 'updated_at']


class SalesRoomNestedSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalesRoom
        fields = [
            "id",
            "deal_title",
            "deal_price",
            "deal_description",
            "deal_status",
            "start_date",
            "end_date",
            "address",
            "postal_code",
            "city",
            "country",
        ]


class ProposalSerializer(serializers.ModelSerializer):
    proposal_from = ProposalFromSerializers(read_only=True)
    proposal_for = ProposalForSerializers(read_only=True)
    transporter_details = TransporterDetailsSerializer(required=False, allow_null=True)
    items = ItemSerializer(many=True, required=False)
    proposal_status = serializers.SerializerMethodField(read_only=True)
    proposal_for_id = serializers.IntegerField(write_only=True, required=False)
    is_assigned_deal = serializers.BooleanField(required=False, default=False)
    salesroom_data = serializers.SerializerMethodField()
    shippings_details_data = serializers.SerializerMethodField()
    doc_reference_id = serializers.SerializerMethodField(read_only=True)
    is_proposal_signed = serializers.SerializerMethodField(read_only=True)
    
    # Shipping details fields
    shipped_to_data = ShippingAddressSerializer(required=False)
    shipped_from_data = ShippingAddressSerializer(required=False)
    shipped_details = ShippedDetailsSerializer(many=True, read_only=True)
    shipped_date = serializers.DateTimeField(required=False)
    shipping_notes = serializers.CharField(required=False, allow_blank=True)
    tracking_number = serializers.CharField(required=False, allow_blank=True)

    class Meta:
        model = Proposal
        fields = "__all__"
        extra_fields = [
            "shipped_to_data",
            "shipped_from_data",
            "shipped_date",
            "shipping_notes",
            "tracking_number",
        ]

    def get_proposal_status(self, obj: Proposal) -> Optional[str]:
        return obj.get_proposal_status_display() if obj.proposal_status else None

    def get_salesroom_data(self, obj: Proposal) -> Optional[Dict[str, Any]]:
        salesroom: Optional[SalesRoom] = SalesRoom.objects.filter(proposal=obj).first()
        return SalesRoomNestedSerializer(salesroom).data if salesroom else None
    
    def get_doc_reference_id(self, obj: Proposal) -> Optional[str]:
        """Return the reference_id of the linked user document, or None if not linked"""
        return obj.user_document.reference_id if obj.user_document else None
    
    def get_is_proposal_signed(self, obj: Proposal) -> Optional[bool]:
        """Return True if the linked user document is signed by any signer, False if not signed, None if no document linked"""
        if not obj.user_document:
            return None
        
        from documents.models import DocumentSigner
        has_signed_signer = DocumentSigner.objects.filter(
            document=obj.user_document,
            status="signed"
        ).exists()
        
        return has_signed_signer
    
    def get_shippings_details_data(self, obj: Proposal) -> Optional[List[Dict[str, Any]]]:
        """Return the shipping details data for the proposal"""
        shipping_data = obj.shippeddetails_set.all().order_by('shipped_date').values()
        return list(shipping_data)

    def _create_shipping_details(self, proposal: Proposal, validated_data: Dict[str, Any]) -> None:
        """Create shipping details if provided"""
        shipped_to_data = self._parse_json(self._get_request_data("shipped_to_data"), "shipped_to_data")
        shipped_from_data = self._parse_json(self._get_request_data("shipped_from_data"), "shipped_from_data")
        shipped_date = validated_data.pop('shipped_date', None)
        shipping_notes = validated_data.pop('shipping_notes', None)
        tracking_number = validated_data.pop('tracking_number', None)
        
        # Only create shipping details if both addresses are provided
        if shipped_to_data and shipped_from_data and shipped_date:
            # Map frontend payload to model fields
            shipping_data = {
                'proposal': proposal,
                'shipped_date': shipped_date,
                'shipping_notes': shipping_notes,
                'tracking_number': tracking_number,
                
                # From address (shipped_from)
                'from_address_line1': shipped_from_data.get('address', ''),
                'from_address_line2': '',  # Not provided in frontend payload
                'from_city': shipped_from_data.get('city', ''),
                'from_state': shipped_from_data.get('state', ''),
                'from_postal_code': shipped_from_data.get('pincode', ''),
                'from_country': shipped_from_data.get('country', ''),
                
                # To address (shipped_to)
                'to_address_line1': shipped_to_data.get('address', ''),
                'to_address_line2': '',  # Not provided in frontend payload
                'to_city': shipped_to_data.get('city', ''),
                'to_state': shipped_to_data.get('state', ''),
                'to_postal_code': shipped_to_data.get('pincode', ''),
                'to_country': shipped_to_data.get('country', ''),
            }
            
            ShippedDetails.objects.create(**shipping_data)
    
    def _update_shipping_details(self, proposal: Proposal, validated_data: Dict[str, Any]) -> None:
        """Update or create shipping details if provided"""
        shipped_to_data = validated_data.pop('shipped_to_data', None)
        shipped_from_data = validated_data.pop('shipped_from_data', None)
        shipped_date = validated_data.pop('shipped_date', None)
        shipping_notes = validated_data.pop('shipping_notes', None)
        tracking_number = validated_data.pop('tracking_number', None)
        
        # Only update/create shipping details if both addresses are provided
        if shipped_to_data and shipped_from_data and shipped_date:
            # Get existing shipping details or create new one
            shipping_detail, created = ShippedDetails.objects.get_or_create(
                proposal=proposal,
                defaults={
                    'shipped_date': shipped_date,
                    'shipping_notes': shipping_notes,
                    'tracking_number': tracking_number,
                    
                    # From address (shipped_from)
                    'from_address_line1': shipped_from_data.get('address', ''),
                    'from_address_line2': '',
                    'from_city': shipped_from_data.get('city', ''),
                    'from_state': shipped_from_data.get('state', ''),
                    'from_postal_code': shipped_from_data.get('pincode', ''),
                    'from_country': shipped_from_data.get('country', ''),
                    
                    # To address (shipped_to)
                    'to_address_line1': shipped_to_data.get('address', ''),
                    'to_address_line2': '',
                    'to_city': shipped_to_data.get('city', ''),
                    'to_state': shipped_to_data.get('state', ''),
                    'to_postal_code': shipped_to_data.get('pincode', ''),
                    'to_country': shipped_to_data.get('country', ''),
                }
            )
            
            # Update existing shipping details
            if not created:
                shipping_detail.shipped_date = shipped_date
                shipping_detail.shipping_notes = shipping_notes
                shipping_detail.tracking_number = tracking_number
                
                # From address
                shipping_detail.from_address_line1 = shipped_from_data.get('address', '')
                shipping_detail.from_city = shipped_from_data.get('city', '')
                shipping_detail.from_state = shipped_from_data.get('state', '')
                shipping_detail.from_postal_code = shipped_from_data.get('pincode', '')
                shipping_detail.from_country = shipped_from_data.get('country', '')
                
                # To address
                shipping_detail.to_address_line1 = shipped_to_data.get('address', '')
                shipping_detail.to_city = shipped_to_data.get('city', '')
                shipping_detail.to_state = shipped_to_data.get('state', '')
                shipping_detail.to_postal_code = shipped_to_data.get('pincode', '')
                shipping_detail.to_country = shipped_to_data.get('country', '')
                
                shipping_detail.save()

    def validate_proposal_expires_at(self, value):
        if value.date() < timezone.now().date():
            raise serializers.ValidationError(
                "proposal_expires_at cannot be a past date."
            )
        return value

    def _parse_json(
        self, data: Union[str, Dict[str, Any], List[Any]], field_name: str
    ) -> Union[Dict[str, Any], List[Any]]:
        if isinstance(data, str):
            try:
                return json.loads(data)
            except Exception:
                raise serializers.ValidationError({field_name: "Invalid JSON format"})
        return data

    def _get_request_data(self, field_name: str) -> Any:
        request = self.context.get("request")
        return request.data.get(field_name)

    def _generate_proposal_id(self) -> str:
        last_proposal: Optional[Proposal] = (
            Proposal.objects.exclude(proposal_id__isnull=True)
            .exclude(proposal_id="")
            .order_by("-proposal_id")
            .first()
        )
        last_num: int = 0
        if last_proposal and last_proposal.proposal_id:
            try:
                last_num = (
                    int(last_proposal.proposal_id[1:])
                    if last_proposal.proposal_id[1:].isdigit()
                    else 0
                )
            except Exception:
                last_num = 0
        new_id: str = f"A{last_num + 1:04d}"
        if Proposal.objects.filter(proposal_id=new_id).exists():
            raise serializers.ValidationError(
                {"proposal_id": f"Proposal ID {new_id} already exists."}
            )
        return new_id

    

    def _get_or_create_proposal_for(
        self, validated_data: Dict[str, Any]
    ) -> OrganisationContacts:
        proposal_for_id: Optional[int] = validated_data.pop("proposal_for_id", None)
        proposal_for_payload: Any = self._get_request_data("proposal_for")

        # Case 1: proposal_for_id provided
        if proposal_for_id:
            proposal_for_obj: Optional[OrganisationContacts] = (
                OrganisationContacts.objects.filter(id=proposal_for_id).first()
            )
            if not proposal_for_obj:
                raise serializers.ValidationError(
                    {
                        "proposal_for_id": "Organisation contact not found, please check or add it manually with proposal_for field."
                    }
                )
            return proposal_for_obj

        # Case 2: proposal_for payload provided
        if not proposal_for_payload:
            raise serializers.ValidationError(
                {
                    "proposal_for": "Either proposal_for_id or proposal_for payload is required."
                }
            )

        proposal_for_payload = self._parse_json(proposal_for_payload, "proposal_for")
        if not isinstance(proposal_for_payload, dict):
            raise serializers.ValidationError(
                {"proposal_for": "Must be a valid organisation contact object."}
            )

        # Always fetch the org from proposal_from_id
        proposal_from_id: Any = self._get_request_data("proposal_from_id")
        org: Optional[Organization] = Organization.objects.filter(
            reference_id=proposal_from_id
        ).first()
        if not org:
            raise serializers.ValidationError(
                {"proposal_from_id": "Organization not found."}
            )

        # Map frontend field names to model field names
        mapped_payload = {
            'organisation': org,
            'name': proposal_for_payload.get('name'),
            'email': proposal_for_payload.get('email') or proposal_for_payload.get('contact_email'),
            'phone_number': proposal_for_payload.get('phone_number') or proposal_for_payload.get('contact_number'),
            'assigner_org_name': proposal_for_payload.get('assigner_org_name'),
        }
        
        # Validate required fields
        if not mapped_payload.get('email'):
            raise serializers.ValidationError(
                {"proposal_for": "Email is required for organisation contact."}
            )
        
        if not mapped_payload.get('assigner_org_name'):
            raise serializers.ValidationError(
                {"proposal_for": "Assigner organization name is required."}
            )
        
        # Check if OrganisationContact already exists under this organization
        query = OrganisationContacts.objects.filter(
            organisation=org,
            email=mapped_payload.get("email"),
        )
        if query.exists():
            return query.first()

        # Otherwise, create new OrganisationContact
        proposal_for_obj: OrganisationContacts = OrganisationContacts.objects.create(
            **mapped_payload
        )
        return proposal_for_obj

    def create(self, validated_data: Dict[str, Any]) -> Proposal:
        request = self.context.get("request")

        action: Optional[str] = request.query_params.get("action") or request.data.get(
            "action"
        )

        validated_data["proposal_status"] = validated_data.get("proposal_status", "draft")

        # Proposal From (Organization)
        user_org_id: Any = self._get_request_data("proposal_from_id")
        if not user_org_id:
            raise serializers.ValidationError(
                {"proposal_from_id": "This field is required."}
            )
        user_org_obj: Optional[Organization] = Organization.objects.filter(
            reference_id=user_org_id
        ).first()
        if not user_org_obj:
            raise serializers.ValidationError(
                {"proposal_from_id": "Organization not found."}
            )

        # Proposal For (OrganisationContacts)
        proposal_for_obj: OrganisationContacts = self._get_or_create_proposal_for(
            validated_data
        )

        # Transporter
        transporter_data: Any = validated_data.pop(
            "transporter_details", None
        ) or self._get_request_data("transporter_details")
        transporter: Optional[TransporterDetails] = None
        if transporter_data:
            transporter_data = self._parse_json(transporter_data, "transporter_details")
            if isinstance(transporter_data, dict) and transporter_data:
                transporter = TransporterDetails.objects.create(**transporter_data)

        # Items
        items_data: Any = validated_data.pop("items", None) or self._get_request_data(
            "items"
        )
        if not items_data:
            raise serializers.ValidationError({"items": "This field is required."})
        items_data = self._parse_json(items_data, "items")
        if not isinstance(items_data, list):
            raise serializers.ValidationError(
                {"items": "Must be a list of item dictionaries."}
            )
        item_instances: List[Item] = [
            Item.objects.create(**item) for item in items_data
        ]

        # Proposal ID
        validated_data["proposal_id"] = self._generate_proposal_id()
        

        is_assigned_deal: bool = self._get_request_data("is_assigned_deal") or False
        print(validated_data)
        proposal: Proposal = Proposal.objects.create(
            proposal_from=user_org_obj,
            proposal_for=proposal_for_obj,
            transporter_details=transporter,
            **validated_data,
        )
        proposal.items.set(item_instances)

        # Create shipping details if provided
        self._create_shipping_details(proposal, validated_data)

        if is_assigned_deal == True or is_assigned_deal=='true':
            SalesRoom.objects.create(
                proposal=proposal,
                client=proposal.proposal_for,
                deal_title=f"Proposal {proposal.proposal_title}",
                deal_price=sum(item.item_amount for item in proposal.items.all()),
                deal_description=getattr(proposal, "description", ""),
                deal_status="appointment_scheduled",  # default
                start_date=proposal.created_at.date() if hasattr(proposal, "created_at") else None,
                end_date=(proposal.created_at + timedelta(days=30)).date() if hasattr(proposal, "created_at") else None,
            )
        return proposal

    def update(self, instance: Proposal, validated_data: Dict[str, Any]) -> Proposal:
        # Check if proposal is linked to a user document and validate signing status
        if instance.user_document:
            # Check if the linked user document exists and hasn't been signed
            if instance.user_document.status == "sent":
                # Check if any signer has signed the document
                from documents.models import DocumentSigner
                has_signed_signer = DocumentSigner.objects.filter(
                    document=instance.user_document,
                    status="signed"
                ).exists()
                
                if has_signed_signer:
                    raise serializers.ValidationError(
                        "Cannot update proposal. The linked document is in 'sent' status and has been signed by one or more   signers."
                    )
        
        # Proposal For (OrganisationContacts)
        if "proposal_for_id" in validated_data or self._get_request_data(
            "proposal_for"
        ):
            instance.proposal_for = self._get_or_create_proposal_for(validated_data)

        # Transporter
        transporter_data: Any = validated_data.pop("transporter_details", None)
        if transporter_data is not None:
            transporter_data = self._parse_json(transporter_data, "transporter_details")
            if isinstance(transporter_data, dict) and transporter_data:
                if instance.transporter_details:
                    for attr, value in transporter_data.items():
                        if value is not None:
                            setattr(instance.transporter_details, attr, value)
                    instance.transporter_details.save()
                else:
                    instance.transporter_details = TransporterDetails.objects.create(
                        **transporter_data
                    )
            else:
                instance.transporter_details = None

        # Items
        items_data: Any = validated_data.pop("items", None)
        if items_data:
            items_data = self._parse_json(items_data, "items")
            if not isinstance(items_data, list):
                raise serializers.ValidationError(
                    {"items": "Must be a list of item dictionaries."}
                )
            item_instances: List[Item] = []
            for idx, item_dict in enumerate(items_data):
                if instance.items.count() > idx:
                    item_obj: Item = instance.items.all()[idx]
                    for attr, value in item_dict.items():
                        if value is not None:
                            setattr(item_obj, attr, value)
                    item_obj.save()
                    item_instances.append(item_obj)
                else:
                    item_instances.append(Item.objects.create(**item_dict))
            instance.items.set(item_instances)

        # Update shipping details if provided
        self._update_shipping_details(instance, validated_data)

        # Update remaining fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class ProposalDashboardSerializer(serializers.Serializer):
    draft_count = serializers.IntegerField()
    sent_count = serializers.IntegerField()
    signed_count = serializers.IntegerField()
    all_count = serializers.IntegerField()
    proposal_data = ProposalSerializer(many=True)
