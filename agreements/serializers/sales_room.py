from rest_framework import serializers
from agreements.models.proposal_models import Proposal
from agreements.models.sales_room_models import SalesRoom
from documents.models.document_models import OrganisationContacts, Organization
from typing import Dict, Any, Optional


class OrganizationSerializer(serializers.ModelSerializer):
    """Serializer to fetch full organization info"""

    class Meta:
        model = Organization
        fields = "__all__"
        ref_name = "AgreementsOrganizationSerializer"


class OrganisationContactsSerializer(serializers.ModelSerializer):
    """Serializer for client info with nested organization"""

    organisation = OrganizationSerializer(read_only=True)

    class Meta:
        model = OrganisationContacts
        fields = "__all__"
        ref_name = "AgreementsOrganisationContacts"


class SalesRoomSerializer(serializers.ModelSerializer):
    client_info = OrganisationContactsSerializer(source="client", read_only=True)
    proposal_id = serializers.UUIDField(
        write_only=True, required=False, allow_null=True)
    client_id = serializers.IntegerField(
        write_only=True, required=True)

    class Meta:
        model = SalesRoom
        extra_fields = ["proposal_id", "client_id"]
        exclude = ["client"]

    def validate_proposal_id(self, value):
        """
        Ensure that the proposal is not already assigned to another SalesRoom.
        """
        if value and SalesRoom.objects.filter(proposal_id=value).exists():
            raise serializers.ValidationError(
                "This proposal is already linked to another deal. Each deal must have a unique proposal."
            )
        return value

    def _get_client(self, client_id: str) -> OrganisationContacts:
        """
        Get or create OrganisationContacts for the client.
        If client exists with same name and organization, return it.
        Otherwise, create a new one.
        """
        # Try to find existing client by name and organization
        client_obj = OrganisationContacts.objects.filter(
            pk=client_id
        ).first()

        if client_obj:
            return client_obj

    def create(self, validated_data):
        # Extract client name and proposal_id
        client_id = validated_data.pop("client_id")
        proposal_id = validated_data.pop("proposal_id", None)

        # Get organization from request context
        request = self.context.get('request')
        if not request or not hasattr(request, 'user'):
            raise serializers.ValidationError("Request context is required")

        # Get user's organization
        user = request.user
        organization = user.organizations.filter(
            is_active=True, is_primary=True).first()
        if not organization:
            raise serializers.ValidationError(
                "User must belong to an active primary organization")

        # Get or create client
        client = self._get_client(client_id)
        validated_data["client"] = client

        # Handle proposal if provided
        if proposal_id:
            try:
                proposal = Proposal.objects.get(id=proposal_id)
                validated_data["proposal"] = proposal
            except Proposal.DoesNotExist:
                raise serializers.ValidationError(
                    {"proposal_id": "Invalid proposal_id. Proposal does not exist."}
                )

        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Extract client name if provided
        client_id = validated_data.pop("client_id", None)
        proposal_id = validated_data.pop("proposal_id", None)

        if client_id:
            # Get organization from request context
            request = self.context.get('request')
            if not request or not hasattr(request, 'user'):
                raise serializers.ValidationError("Request context is required")

            # Get user's organization
            user = request.user
            organization = user.organizations.filter(
                is_active=True, is_primary=True).first()
            if not organization:
                raise serializers.ValidationError(
                    "User must belong to an active primary organization")

            # Get or create client
            client = self._get_client(client_id)
            validated_data["client"] = client

        # Handle proposal if provided
        if proposal_id:
            try:
                proposal = Proposal.objects.get(id=proposal_id)
                validated_data["proposal"] = proposal
            except Proposal.DoesNotExist:
                raise serializers.ValidationError(
                    {"proposal_id": "Invalid proposal_id. Proposal does not exist."}
                )

        return super().update(instance, validated_data)
