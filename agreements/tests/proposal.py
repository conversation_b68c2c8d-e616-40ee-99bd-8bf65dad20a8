from rest_framework.test import APITestCase
from rest_framework import status
from accounts.models.organisation import Organization
from documents.models.document_models import OrganisationContacts
from agreements.models.proposal_models import Proposal
from django.utils import timezone
from datetime import timedelta
from accounts.models.users import User


class ProposalAPITestCase(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email="<EMAIL>", password="testpass123", phone_number="**********"
        )

        self.org = Organization.objects.create(
            name="Organization", creator_user=self.user
        )
        self.contact = OrganisationContacts.objects.create(
            organisation=self.org,
            name="Client Contact",
            email="<EMAIL>",
            phone_number="**********",
        )

        # Base payload
        self.base_payload = {
            "proposal_title": "ERP Implementation",
            "proposal_from_id": self.org.id,
            "proposal_for_id": self.contact.id,
            "vat_tax_and_currency": 2500,
            "items": [
                {
                    "item_name": "ERP Module",
                    "item_quantity": 2,
                    "item_rate": 1500,
                    "item_amount": 3000,
                    "item_description": "Core ERP module",
                }
            ],
            "proposal_expires_at": (timezone.now() + timedelta(days=30)).isoformat(),
            "is_assigned_deal": False,
        }

    def test_create_proposal_draft_success(self):
        """Ensure proposal is created with status Draft when action=save"""
        url = "/en/api/v1/agreements/proposals/create/?action=save"

        response = self.client.post(
            url, self.base_payload, format="json", HTTP_ACCEPT_LANGUAGE="en"
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("data", response.json())
        self.assertEqual(response.json()["data"]["proposal_status"], "Draft")

    def test_create_proposal_signed_success(self):
        """Ensure proposal is created with status Signed when action=save_and_sign"""
        url = "/en/api/v1/agreements/proposals/create/?action=save_and_sign"

        response = self.client.post(url, self.base_payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.json()["data"]["proposal_status"], "Signed")

    def test_create_proposal_missing_title(self):
        """Validation error when proposal_title is missing"""
        url = "/en/api/v1/agreements/proposals/create/?action=save"
        invalid_payload = self.base_payload.copy()
        invalid_payload.pop("proposal_title")

        response = self.client.post(url, invalid_payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("proposal_title", response.json()["data"])

    # ----------------- RETRIEVE / LIST -----------------
    def test_get_proposal_list(self):
        Proposal.objects.create(
            proposal_id="A0001",
            proposal_title="ERP Implementation",
            proposal_status="draft",
            proposal_from=self.org,
            proposal_for=self.contact,
            proposal_expires_at=timezone.now() + timedelta(days=30),
        )
        url = "/en/api/v1/agreements/proposals/list/"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue("proposals" in response.data["data"])

    def test_get_single_proposal(self):
        proposal = Proposal.objects.create(
            proposal_id="A0002",
            proposal_title="ERP Implementation",
            proposal_status="draft",
            proposal_from=self.org,
            proposal_for=self.contact,
            proposal_expires_at=timezone.now() + timedelta(days=30),
        )
        url = f"/en/api/v1/agreements/proposals/{proposal.id}/"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["data"]["proposal_title"], "ERP Implementation")

    # ----------------- UPDATE -----------------
    def test_update_proposal_title(self):
        proposal = Proposal.objects.create(
            proposal_id="A0003",
            proposal_title="Old Title",
            proposal_status="draft",
            proposal_from=self.org,
            proposal_for=self.contact,
            proposal_expires_at=timezone.now() + timedelta(days=30),
        )
        url = f"/en/api/v1/agreements/proposals/{proposal.id}/"
        response = self.client.put(url, {"proposal_title": "New Title"}, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        proposal.refresh_from_db()
        self.assertEqual(proposal.proposal_title, "New Title")

    # ----------------- WITHDRAW -----------------
    def test_withdraw_proposal(self):
        proposal = Proposal.objects.create(
            proposal_id="A0004",
            proposal_title="ERP Proposal",
            proposal_status="draft",
            proposal_from=self.org,
            proposal_for=self.contact,
            proposal_expires_at=timezone.now() + timedelta(days=30),
        )
        url = f"/en/api/v1/agreements/proposals/{proposal.id}/withdraw/"
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        proposal.refresh_from_db()
        self.assertTrue(proposal.is_withdrawn)

    # ----------------- DELETE -----------------
    def test_delete_proposal_without_withdraw(self):
        proposal = Proposal.objects.create(
            proposal_id="A0005",
            proposal_title="ERP Proposal",
            proposal_status="draft",
            proposal_from=self.org,
            proposal_for=self.contact,
            proposal_expires_at=timezone.now() + timedelta(days=30),
        )
        url = f"/en/api/v1/agreements/proposals/{proposal.id}/"
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data["status"], "error")

    def test_delete_proposal_after_withdraw(self):
        proposal = Proposal.objects.create(
            proposal_id="A0006",
            proposal_title="ERP Proposal",
            proposal_status="draft",
            proposal_from=self.org,
            proposal_for=self.contact,
            proposal_expires_at=timezone.now() + timedelta(days=30),
            is_withdrawn=True,
        )
        url = f"/en/api/v1/agreements/proposals/{proposal.id}/"
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        proposal.refresh_from_db()
        self.assertTrue(proposal.is_deleted)

    # ----------------- DASHBOARD -----------------
    def test_dashboard_counts(self):
        Proposal.objects.create(
            proposal_id="A0007",
            proposal_title="ERP Proposal",
            proposal_status="draft",
            proposal_from=self.org,
            proposal_for=self.contact,
            proposal_expires_at=timezone.now() + timedelta(days=30),
        )
        url = "/en/api/v1/agreements/proposal/dashboard/"
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("draft_count", response.data)
        self.assertIn("all_count", response.data)
