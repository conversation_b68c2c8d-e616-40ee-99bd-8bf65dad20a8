from rest_framework import status
from rest_framework.test import APITestCase
from django.utils import timezone
from datetime import timedelta

from agreements.models import Proposal, SalesRoom
from documents.models.document_models import OrganisationContacts
from accounts.models import Organization


class SalesRoomAPITestCase(APITestCase):
    def setUp(self):
        self.org = Organization.objects.create(name="Test Org")
        self.contact = OrganisationContacts.objects.create(
            organisation=self.org, email="<EMAIL>"
        )

        self.proposal = Proposal.objects.create(
            proposal_id="A1001",
            proposal_title="ERP Proposal",
            proposal_status="draft",
            proposal_from=self.org,
            proposal_for=self.contact,
            proposal_expires_at=timezone.now() + timedelta(days=30),
        )

        # Base payload for salesroom
        self.base_payload = {
            "proposal_id": str(self.proposal.id),
            "deal_title": "ERP Deal",
            "deal_price": "10000.00",
            "deal_description": "ERP Implementation Project",
            "deal_status": "appointment_scheduled",
            "start_date": (timezone.now().date() + timedelta(days=1)).isoformat(),
            "end_date": (timezone.now().date() + timedelta(days=10)).isoformat(),
            "address": "123 Test St",
            "postal_code": "12345",
            "city": "Test City",
            "country": "Testland",
            "client": str(self.contact.id),
        }

    # ----------------- CREATE -----------------
    def test_create_salesroom_success(self):
        url = "/en/api/v1/agreements/salesroom/create/"
        response = self.client.post(url, self.base_payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn("data", response.json())
        self.assertEqual(response.json()["data"]["deal_title"], "ERP Deal")

    def test_create_salesroom_missing_title(self):
        url = "/en/api/v1/agreements/salesroom/create/"
        payload = self.base_payload.copy()
        payload.pop("deal_title")
        response = self.client.post(url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("deal_title", response.json()["data"])

    # ----------------- LIST -----------------
    def test_list_salesrooms(self):
        SalesRoom.objects.create(
            proposal=self.proposal,
            client=self.contact,
            deal_title="ERP Deal",
            deal_price="25000.00",
            deal_description="Phase 1",
            deal_status="appointment_scheduled",
            start_date=timezone.now().date(),
            end_date=(timezone.now() + timedelta(days=60)).date(),
            city="Mumbai",
            country="India",
        )
        url = "/en/api/v1/agreements/salesroom/list/"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(len(response.json()["data"]) >= 1)

    # ----------------- RETRIEVE -----------------
    def test_retrieve_salesroom(self):
        salesroom = SalesRoom.objects.create(
            proposal=self.proposal,
            client=self.contact,
            deal_title="ERP Deal",
            deal_price="25000.00",
            deal_description="Phase 1",
            deal_status="appointment_scheduled",
            start_date=timezone.now().date(),
            end_date=(timezone.now() + timedelta(days=60)).date(),
            city="Mumbai",
            country="India",
        )
        url = f"/en/api/v1/agreements/salesroom/{salesroom.id}/"
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["data"]["deal_title"], "ERP Deal")

    # ----------------- UPDATE -----------------
    def test_update_salesroom(self):
        salesroom = SalesRoom.objects.create(
            proposal=self.proposal,
            client=self.contact,
            deal_title="Old Title",
            deal_price="10000.00",
            start_date=timezone.now().date(),
            end_date=(timezone.now() + timedelta(days=60)).date(),
            city="Delhi",
            country="India",
        )

        url = f"/en/api/v1/agreements/salesroom/update/{salesroom.id}/"

        payload = {
            **self.base_payload,
            "deal_title": "Updated ERP Deal",
        }
        payload.pop("proposal_id", None)

        response = self.client.put(url, payload, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["data"]["deal_title"], "Updated ERP Deal")

    # ----------------- CANCEL -----------------
    def test_cancel_salesroom(self):
        salesroom = SalesRoom.objects.create(
            proposal=self.proposal,
            client=self.contact,
            deal_title="ERP Deal",
            deal_price="10000.00",
            deal_description="ERP Implementation Project",
            deal_status="deal_signed",
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=10),
            address="123 Test St",
            postal_code="12345",
            city="Test City",
            country="Testland",
        )

        url = f"/en/api/v1/agreements/salesroom/{salesroom.id}/cancel/"
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        salesroom.refresh_from_db()
        self.assertEqual(salesroom.deal_status, "deal_signed")

    # ----------------- DELETE -----------------
    def test_delete_salesroom(self):
        salesroom = SalesRoom.objects.create(
            proposal=self.proposal,
            client=self.contact,
            deal_title="Delete Deal",
            deal_price="5000.00",
            start_date=timezone.now().date(),
            end_date=(timezone.now() + timedelta(days=30)).date(),
            city="Mumbai",
            country="India",
        )

        url = f"/en/api/v1/agreements/salesroom/{salesroom.id}/"

        response = self.client.delete(url, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertIn("data", response.json())
        self.assertEqual(response.json()["data"]["id"], str(salesroom.id))
        self.assertEqual(response.json()["status"], "success")

        salesroom.refresh_from_db()
        self.assertTrue(salesroom.is_deleted)
