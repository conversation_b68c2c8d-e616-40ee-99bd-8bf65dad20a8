from django.urls import path
from agreements.views.proposal import (
    ProposalAPIView,
    ProposalDashboardAPIView,
)

from agreements.views.proposal import ProposalAPIView
from agreements.views.sales_room import (
    SalesRoomListCreateAPIView,
    SalesRoomDetailAPIView,
    SalesRoomCancelAPIView,
)

urlpatterns = [
    path("proposals/list/", ProposalAPIView.as_view(), name="proposal-list"),
    path("proposals/create/", ProposalAPIView.as_view(), name="proposal-create"),
    path("proposals/<uuid:pk>/", ProposalAPIView.as_view(), name="proposal-detail"),
    path(
        "proposals/<uuid:pk>/withdraw/",
        ProposalAPIView.as_view(),
        name="proposal-withdraw",
    ),
    path(
        "proposals/update/<uuid:pk>/", ProposalAPIView.as_view(), name="proposal-update"
    ),
    path(
        "proposals/delete/<uuid:pk>/", ProposalAPIView.as_view(), name="proposal-delete"
    ),
    path(
        "proposal/dashboard/",
        ProposalDashboardAPIView.as_view(),
        name="proposal-dashboard",
    ),
    path(
        "salesroom/list/", SalesRoomListCreateAPIView.as_view(), name="salesroom-list"
    ),
    path(
        "salesroom/create/",
        SalesRoomListCreateAPIView.as_view(),
        name="salesroom-create",
    ),
    path(
        "salesroom/<uuid:pk>/",
        SalesRoomDetailAPIView.as_view(),
        name="salesroom-detail",
    ),
    path(
        "salesroom/update/<uuid:pk>/",
        SalesRoomDetailAPIView.as_view(),
        name="salesroom-update",
    ),
    path(
        "salesroom/<uuid:pk>/cancel/",
        SalesRoomCancelAPIView.as_view(),
        name="salesroom-cancel",
    ),
]
