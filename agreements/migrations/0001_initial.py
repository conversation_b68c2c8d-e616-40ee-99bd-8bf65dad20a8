# Generated by Django 5.1.1 on 2025-09-04 12:17

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accounts", "0020_organization_add_on_user_limit"),
        ("documents", "0009_userdocument_agreement_end_date_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Item",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("item_name", models.CharField(max_length=255)),
                ("item_quantity", models.IntegerField()),
                ("item_rate", models.IntegerField()),
                ("item_amount", models.IntegerField()),
                ("item_description", models.TextField()),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="TaxDetails",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("tax_name", models.CharField(max_length=255)),
                ("tax_percentage", models.IntegerField()),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="TransporterDetails",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("transporter_id", models.CharField(editable=False, max_length=255)),
                ("name", models.CharField(max_length=255)),
                ("mode_of_transport", models.CharField(max_length=255)),
                (
                    "transport_doc_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("transport_doc_date", models.DateField()),
                (
                    "vehicle_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "vehicle_type",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Proposal",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("proposal_id", models.CharField(editable=False, max_length=255)),
                ("proposal_title", models.CharField(max_length=255)),
                (
                    "proposal_status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("signed", "Signed"),
                            ("sent", "Sent"),
                        ],
                        max_length=255,
                    ),
                ),
                ("vat_tax_and_currency", models.IntegerField()),
                ("terms_and_conditions", models.TextField()),
                ("essential_information", models.TextField()),
                ("additional_information", models.TextField()),
                ("footer_information", models.TextField()),
                (
                    "proposal_document_file",
                    models.FileField(
                        blank=True, null=True, upload_to="proposals/documents/"
                    ),
                ),
                ("proposal_expires_at", models.DateTimeField()),
                (
                    "items",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="agreements.item",
                    ),
                ),
                (
                    "proposal_for",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="documents.organisationcontacts",
                    ),
                ),
                (
                    "proposal_from",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounts.organization",
                    ),
                ),
                (
                    "transporter_details",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="agreements.transporterdetails",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
