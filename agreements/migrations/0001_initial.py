# Generated by Django 5.1.1 on 2025-09-23 05:05

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accounts", "0020_organization_add_on_user_limit"),
        ("documents", "0010_organisationcontacts_assigner_org_address_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Item",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("item_name", models.CharField(max_length=255)),
                ("item_quantity", models.IntegerField()),
                ("item_rate", models.IntegerField()),
                ("item_amount", models.IntegerField()),
                ("item_description", models.TextField(blank=True, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="TaxDetails",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("tax_name", models.CharField(max_length=255)),
                ("tax_percentage", models.IntegerField()),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="TransporterDetails",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("transporter_id", models.CharField(editable=False, max_length=255)),
                ("name", models.CharField(max_length=255)),
                ("mode_of_transport", models.CharField(max_length=255)),
                (
                    "transport_doc_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("transport_doc_date", models.DateTimeField()),
                (
                    "vehicle_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "vehicle_type",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Proposal",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("proposal_id", models.CharField(editable=False, max_length=255)),
                ("proposal_title", models.CharField(max_length=255)),
                (
                    "proposal_status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("signed", "Signed"),
                            ("sent", "Sent"),
                        ],
                        max_length=255,
                    ),
                ),
                ("vat_tax_and_currency", models.IntegerField(blank=True, null=True)),
                ("terms_and_conditions", models.TextField(blank=True, null=True)),
                ("essential_information", models.TextField(blank=True, null=True)),
                ("additional_information", models.TextField(blank=True, null=True)),
                ("footer_information", models.TextField(blank=True, null=True)),
                (
                    "proposal_logo_file",
                    models.FileField(
                        blank=True, null=True, upload_to="proposals/documents/"
                    ),
                ),
                ("proposal_expires_at", models.DateTimeField()),
                ("is_withdrawn", models.BooleanField(default=False)),
                ("is_deleted", models.BooleanField(default=False)),
                ("is_assigned_deal", models.BooleanField(default=False)),
                ("items", models.ManyToManyField(to="agreements.item")),
                (
                    "proposal_for",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="documents.organisationcontacts",
                    ),
                ),
                (
                    "proposal_from",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounts.organization",
                    ),
                ),
                (
                    "transporter_details",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="agreements.transporterdetails",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="SalesRoom",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("deal_title", models.CharField(max_length=255)),
                ("deal_price", models.DecimalField(decimal_places=2, max_digits=10)),
                ("deal_description", models.TextField(blank=True, null=True)),
                (
                    "deal_status",
                    models.CharField(
                        choices=[
                            ("appointment_scheduled", "Appointment Scheduled"),
                            ("qualified_to_buy", "Qualified to Buy"),
                            ("presentation_scheduled", "Presentation Scheduled"),
                            ("decision_maker_brought_in", "Decision Maker Brought In"),
                            ("contract_sent", "Contract Sent"),
                            ("deal_signed", "Deal Signed"),
                        ],
                        default="appointment_scheduled",
                        max_length=50,
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField()),
                ("address", models.TextField(blank=True, null=True)),
                ("postal_code", models.CharField(blank=True, max_length=20, null=True)),
                ("city", models.CharField(max_length=100)),
                ("country", models.CharField(max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_deleted", models.BooleanField(default=False)),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="documents.organisationcontacts",
                    ),
                ),
                (
                    "proposal",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="agreements.proposal",
                    ),
                ),
            ],
        ),
    ]
