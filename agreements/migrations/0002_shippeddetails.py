# Generated by Django 5.1.1 on 2025-09-25 12:13

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("agreements", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ShippedDetails",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("shipped_date", models.DateTimeField()),
                (
                    "from_address_line1",
                    models.Char<PERSON><PERSON>(help_text="From address line 1", max_length=255),
                ),
                (
                    "from_address_line2",
                    models.CharField(
                        blank=True,
                        help_text="From address line 2",
                        max_length=255,
                        null=True,
                    ),
                ),
                ("from_city", models.Char<PERSON>ield(help_text="From city", max_length=100)),
                (
                    "from_state",
                    models.Char<PERSON><PERSON>(help_text="From state/province", max_length=100),
                ),
                (
                    "from_postal_code",
                    models.Char<PERSON><PERSON>(help_text="From postal/ZIP code", max_length=20),
                ),
                (
                    "from_country",
                    models.CharField(help_text="From country", max_length=100),
                ),
                (
                    "to_address_line1",
                    models.CharField(help_text="To address line 1", max_length=255),
                ),
                (
                    "to_address_line2",
                    models.CharField(
                        blank=True,
                        help_text="To address line 2",
                        max_length=255,
                        null=True,
                    ),
                ),
                ("to_city", models.CharField(help_text="To city", max_length=100)),
                (
                    "to_state",
                    models.CharField(help_text="To state/province", max_length=100),
                ),
                (
                    "to_postal_code",
                    models.CharField(help_text="To postal/ZIP code", max_length=20),
                ),
                (
                    "to_country",
                    models.CharField(help_text="To country", max_length=100),
                ),
                (
                    "shipping_notes",
                    models.TextField(
                        blank=True,
                        help_text="Additional shipping notes or instructions",
                        null=True,
                    ),
                ),
                (
                    "tracking_number",
                    models.CharField(
                        blank=True,
                        help_text="Shipping tracking number",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "proposal",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="agreements.proposal",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
