import uuid
from django.db import models
from documents.models.document_models import OrganisationContacts, UserDocument
from djmoney.models.fields import MoneyField


class BaseModel(models.Model):
    class Meta:
        abstract = True

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class Item(BaseModel):

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    item_name = models.CharField(max_length=255)
    item_quantity = models.IntegerField()
    item_rate = models.IntegerField()
    item_amount = models.IntegerField()
    item_description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.item_name


class TransporterDetails(BaseModel):

    id = models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True)
    transporter_id = models.CharField(max_length=255, editable=False)
    name = models.CharField(max_length=255)
    mode_of_transport = models.Char<PERSON>ield(max_length=255)
    transport_doc_number = models.CharField(max_length=255, blank=True, null=True)
    transport_doc_date = models.DateTimeField()
    vehicle_number = models.CharField(max_length=255, blank=True, null=True)
    vehicle_type = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        return self.name


class TaxDetails(BaseModel):

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tax_name = models.CharField(max_length=255)
    tax_percentage = models.IntegerField()

    def __str__(self):
        return self.tax_name


class Proposal(BaseModel):

    STATUS_CHOICES = (
        ("draft", "draft"),
        ("sent", "sent"),
        ("cancelled", "cancelled"),
        ("trash", "trash"),
        ("withdrawn", "withdrawn"),
        ("signed", "Signed")
    )

    id = models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True)
    proposal_id = models.CharField(max_length=255, editable=False)
    proposal_title = models.CharField(max_length=255)
    proposal_status = models.CharField(max_length=255, choices=STATUS_CHOICES)
    proposal_from = models.ForeignKey("accounts.Organization", on_delete=models.CASCADE)
    proposal_for = models.ForeignKey(OrganisationContacts, on_delete=models.CASCADE)
    vat_tax_and_currency = MoneyField(max_digits=10, decimal_places=2, default_currency='USD')
    transporter_details = models.ForeignKey(
        TransporterDetails, on_delete=models.CASCADE, null=True, blank=True
    )
    items = models.ManyToManyField(Item)
    terms_and_conditions = models.TextField(null=True, blank=True)
    essential_information = models.TextField(null=True, blank=True)
    additional_information = models.TextField(null=True, blank=True)
    footer_information = models.TextField(null=True, blank=True)
    proposal_logo_file = models.FileField(
        upload_to="proposals/documents/", null=True, blank=True
    )
    proposal_expires_at = models.DateTimeField()

    is_withdrawn = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    is_assigned_deal = models.BooleanField(default=False)
    
    # Reference to UserDocument for document management integration
    user_document = models.OneToOneField(
        UserDocument,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='proposal',
        help_text="Associated UserDocument for document management"
    )

    def __str__(self):
        return self.proposal_title


class ShippedDetails(BaseModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    proposal = models.ForeignKey(Proposal, on_delete=models.CASCADE)
    shipped_date = models.DateTimeField()
    
    # From Address Details
    from_address_line1 = models.CharField(max_length=255, help_text="From address line 1")
    from_address_line2 = models.CharField(max_length=255, blank=True, null=True, help_text="From address line 2")
    from_city = models.CharField(max_length=100, help_text="From city")
    from_state = models.CharField(max_length=100, help_text="From state/province")
    from_postal_code = models.CharField(max_length=20, help_text="From postal/ZIP code")
    from_country = models.CharField(max_length=100, help_text="From country")
    
    # To Address Details
    to_address_line1 = models.CharField(max_length=255, help_text="To address line 1")
    to_address_line2 = models.CharField(max_length=255, blank=True, null=True, help_text="To address line 2")
    to_city = models.CharField(max_length=100, help_text="To city")
    to_state = models.CharField(max_length=100, help_text="To state/province")
    to_postal_code = models.CharField(max_length=20, help_text="To postal/ZIP code")
    to_country = models.CharField(max_length=100, help_text="To country")
    
    # Additional shipping information
    shipping_notes = models.TextField(blank=True, null=True, help_text="Additional shipping notes or instructions")
    tracking_number = models.CharField(max_length=100, blank=True, null=True, help_text="Shipping tracking number")
    
    def __str__(self):
        return f"Shipment for {self.proposal.proposal_title} - {self.shipped_date.strftime('%Y-%m-%d')}"
    
    @property
    def from_address_full(self):
        """Return formatted from address"""
        address_parts = [
            self.from_address_line1,
            self.from_address_line2,
            f"{self.from_city}, {self.from_state} {self.from_postal_code}",
            self.from_country
        ]
        return ", ".join(filter(None, address_parts))
    
    @property
    def to_address_full(self):
        """Return formatted to address"""
        address_parts = [
            self.to_address_line1,
            self.to_address_line2,
            f"{self.to_city}, {self.to_state} {self.to_postal_code}",
            self.to_country
        ]
        return ", ".join(filter(None, address_parts))