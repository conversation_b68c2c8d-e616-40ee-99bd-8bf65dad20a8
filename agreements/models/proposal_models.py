import uuid
from django.db import models

from documents.models.document_models import OrganisationContacts


class BaseModel(models.Model):
    class Meta:
        abstract = True

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class Item(BaseModel):

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    item_name = models.CharField(max_length=255)
    item_quantity = models.IntegerField()
    item_rate = models.IntegerField()
    item_amount = models.IntegerField()
    item_description = models.TextField()

    def __str__(self):
        return self.item_name


class TransporterDetails(BaseModel):

    id = models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True)
    transporter_id = models.CharField(max_length=255, editable=False)
    name = models.CharField(max_length=255)
    mode_of_transport = models.Char<PERSON>ield(max_length=255)
    transport_doc_number = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    transport_doc_date = models.DateField()
    vehicle_number = models.CharField(max_length=255, blank=True, null=True)
    vehicle_type = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        return self.name


class TaxDetails(BaseModel):

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tax_name = models.CharField(max_length=255)
    tax_percentage = models.IntegerField()

    def __str__(self):
        return self.tax_name


class Proposal(BaseModel):

    STATUS_CHOICES = [("draft", "Draft"), ("signed", "Signed"), ("sent", "Sent")]

    id = models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True)
    proposal_id = models.CharField(max_length=255, editable=False)
    proposal_title = models.CharField(max_length=255)
    proposal_status = models.CharField(max_length=255, choices=STATUS_CHOICES)
    proposal_from = models.ForeignKey("accounts.Organization", on_delete=models.CASCADE)
    proposal_for = models.ForeignKey(OrganisationContacts, on_delete=models.CASCADE)
    vat_tax_and_currency = models.IntegerField()
    transporter_details = models.ForeignKey(
        TransporterDetails, on_delete=models.CASCADE
    )
    items = models.ForeignKey(Item, on_delete=models.CASCADE)
    terms_and_conditions = models.TextField()
    essential_information = models.TextField()
    additional_information = models.TextField()
    footer_information = models.TextField()
    proposal_logo_file = models.FileField(
        upload_to="proposals/documents/", null=True, blank=True
    )
    proposal_expires_at = models.DateTimeField()

    def __str__(self):
        return self.proposal_title
