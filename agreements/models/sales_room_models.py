import uuid
from django.db import models
from documents.models.document_models import OrganisationContacts
from djmoney.models.fields import MoneyField

DEAL_STATUS = (
    ("appointment_scheduled", "Appointment Scheduled"),
    ("qualified_to_buy", "Qualified to Buy"),
    ("presentation_scheduled", "Presentation Scheduled"),
    ("decision_maker_brought_in", "Decision Maker Brought In"),
    ("contract_sent", "Contract Sent"),
    ("deal_signed", "Deal Signed"),
)


class SalesRoom(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    deal_title = models.CharField(max_length=255)
    deal_price = models.FloatField()
    deal_currency = MoneyField(max_digits=10, decimal_places=2, default_currency='USD', null=True, blank=True)
    deal_description = models.TextField(blank=True, null=True)
    deal_status = models.CharField(
        max_length=50, choices=DEAL_STATUS, default="appointment_scheduled"
    )
    client = models.ForeignKey(OrganisationContacts, on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField()
    address = models.TextField(blank=True, null=True)
    postal_code = models.CharField(max_length=20, blank=True, null=True)
    city = models.CharField(max_length=100, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_deleted = models.BooleanField(default=False)
    proposal = models.ForeignKey(
        "agreements.Proposal", on_delete=models.CASCADE, null=True, blank=True
    )

    def __str__(self):
        return self.deal_title
