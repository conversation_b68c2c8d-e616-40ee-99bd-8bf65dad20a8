from rest_framework.views import APIView, Request
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from django.shortcuts import get_object_or_404
from rest_framework import status
from agreements.models.sales_room_models import SalesRoom
from agreements.serializers.sales_room import SalesRoomSerializer
from esign.utils.custom_response import api_response
from drf_yasg import openapi
from django.db.models import Sum, Count
from django.db.models import Q
from utils_app.services.email_send_helper import send_email_to_users
from agreements.decorators import require_subscription_features
from documents.utils.jwt_verifier import JWTAccessTokenAuthentication


class SalesRoomListCreateAPIView(APIView):
    """
    List all SalesRooms or create a new SalesRoom
    """
    authentication_classes = [JWTAccessTokenAuthentication]

    # @require_subscription_features(["proposal", "sales & pipeline"])
    @swagger_auto_schema(
        operation_summary="List all SalesRooms (with filters)",
        manual_parameters=[
            openapi.Parameter(
                "close_date",
                openapi.IN_QUERY,
                description="Filter by deal close date (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
            ),
            openapi.Parameter(
                "last_activity_date",
                openapi.IN_QUERY,
                description="Filter by last updated date (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
            ),
            openapi.Parameter(
                "all_prices",
                openapi.IN_QUERY,
                description="Filter by deal price (exact match)",
                type=openapi.TYPE_NUMBER,
                format="decimal",
            ),
            openapi.Parameter(
                "all_stages",
                openapi.IN_QUERY,
                description="Filter by deal stage (status)",
                type=openapi.TYPE_STRING,
                enum=[s[0] for s in SalesRoom._meta.get_field("deal_status").choices],
            ),
        ],
        responses={200: SalesRoomSerializer(many=True)},
        tags=["salesrooms"],
    )
    def get(self, request: Request) -> Response:
        base_qs = SalesRoom.objects.filter(is_deleted=False)
        deals = base_qs.order_by("-created_at")

        close_date = request.query_params.get("close_date")
        last_activity_date = request.query_params.get("last_activity_date")
        all_prices = request.query_params.get("all_prices")
        all_stages = request.query_params.get("all_stages")

        if close_date:
            deals = deals.filter(end_date=close_date)

        if last_activity_date:
            deals = deals.filter(updated_at__date=last_activity_date)

        if all_prices:
            try:
                min_price, max_price = all_prices.split("-")
                deals = deals.filter(deal_price__gte=min_price, deal_price__lte=max_price)
            except ValueError:
                # If only single price given, fallback to exact match
                deals = deals.filter(deal_price=all_prices)

        if all_stages:
            deals = deals.filter(deal_status=all_stages)

        totals_qs = base_qs.values("deal_status").annotate(
            total_price=Sum("deal_price"), deal_count=Count("id")
        )

        search = self.request.query_params.get("search")
        if search:
            deals = deals.filter(
                Q(deal_title__icontains=search)
                | Q(deal_status__icontains=search)
                | Q(deal_price__icontains=search)
                | Q(address__icontains=search)
                | Q(postal_code__icontains=search)
                | Q(city__icontains=search)
                | Q(country__icontains=search)
                | Q(start_date__icontains=search)
                | Q(end_date__icontains=search)
                | Q(client__name__icontains=search)
                | Q(client__email__icontains=search)
                | Q(client__assigner_org_name__icontains=search)
                | Q(client__assigner_org_address__icontains=search)
                | Q(client__assigner_postal_code__icontains=search)
            )

        serializer = SalesRoomSerializer(deals, many=True)

        totals = {
            item["deal_status"]: {
                "count": item["deal_count"],
                "total_price": item["total_price"] or 0,
            }
            for item in totals_qs
        }

        return api_response(
            action="data_retrieved",
            data={
                "totals": totals,
                "deals": serializer.data,
            },
            status="success",
        )

    # @require_subscription_features(["proposal", "sales & pipeline"])
    @swagger_auto_schema(
        operation_summary="Create a SalesRoom",
        request_body=SalesRoomSerializer,
        responses={201: SalesRoomSerializer, 400: "Invalid request"},
        tags=["salesrooms"],
    )
    def post(self, request: Request) -> Response:
        serializer = SalesRoomSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return api_response(
                action="data_created",
                data=serializer.data,
                status="success",
            )
        return api_response(
            action="data_not_created",
            data=serializer.errors,
            status="error",
        )


class SalesRoomDetailAPIView(APIView):
    """
    Retrieve, update or delete a SalesRoom by ID
    """
    authentication_classes = [JWTAccessTokenAuthentication]

    def get_object(self, pk: str) -> SalesRoom:
        return get_object_or_404(SalesRoom, pk=pk)

    # @require_subscription_features(["proposal", "sales & pipeline"])
    @swagger_auto_schema(
        operation_summary="Retrieve SalesRoom by ID",
        responses={200: SalesRoomSerializer, 404: "Not Found"},
        tags=["salesrooms"],
    )
    def get(self, request: Request, pk: str) -> Response:
        deal = self.get_object(pk)
        serializer = SalesRoomSerializer(deal)
        return api_response(
            action="data_retrieved",
            data=serializer.data,
            status="success",
        )

    # @require_subscription_features(["proposal", "sales & pipeline"])
    @swagger_auto_schema(
        operation_summary="Update SalesRoom",
        request_body=SalesRoomSerializer,
        responses={200: SalesRoomSerializer, 400: "Invalid request", 404: "Not Found"},
        tags=["salesrooms"],
    )
    def put(self, request: Request, pk: str) -> Response:
        deal = self.get_object(pk)

        if deal.is_deleted:
            return api_response(
                action="update_blocked",
                data={"error": "Canceled deals cannot be updated."},
                status="error",
            )

        if deal.deal_status == "deal_signed":
            return api_response(
                action="update_blocked",
                data={"error": "Signed deals cannot be edited."},
                status="error",
            )

        serializer = SalesRoomSerializer(deal, data=request.data, partial=True, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return api_response(
                action="data_updated",
                data=serializer.data,
                status="success",
            )
        return api_response(
            action="data_not_updated",
            data=serializer.errors,
            status="error",
        )

    # @require_subscription_features(["proposal", "sales & pipeline"])
    @swagger_auto_schema(
        operation_summary="Delete SalesRoom",
        responses={204: "Deleted successfully", 404: "Not Found"},
        tags=["salesrooms"],
    )
    def delete(self, request: Request, pk: str) -> Response:
        deal = self.get_object(pk)
        deal.is_deleted = True
        deal.save()
        return api_response(
            action="data_deleted",
            data={"id": pk},
            status="success",
        )


class SalesRoomCancelAPIView(APIView):
    """
    Cancel (soft delete) a SalesRoom deal
    """
    authentication_classes = [JWTAccessTokenAuthentication]

    # @require_subscription_features(["proposal", "sales & pipeline"])
    @swagger_auto_schema(
        operation_summary="Cancel a SalesRoom deal (only if signed)",
        responses={
            200: "Deal canceled successfully",
            400: "Invalid request",
            404: "Deal not found",
        },
        tags=["salesrooms"],
    )
    def post(self, request: Request, pk: str) -> Response:
        deal = get_object_or_404(SalesRoom, pk=pk)

        if deal.is_deleted:
            return api_response(
                action="cancel_failed",
                data={"error": "Deal is already canceled."},
                status="error",
            )

        if deal.deal_status != "deal_signed":
            return api_response(
                action="cancel_failed",
                data={"error": "Only signed deals can be canceled."},
                status="error",
            )

        deal.is_deleted = True
        deal.save()

        try:
            subject = f"Your deal '{deal.deal_title}' has been canceled"
            message = (
                f"Hello {deal.client.name},\n\n"
                f"Your deal '{deal.deal_title}' "
                f"has been canceled successfully.\n\n"
                f"Regards,\nSales Team"
            )

            send_email_to_users(
                subject=subject,
                recipients=[deal.client.email],
                message=message,
            )
        except Exception as e:
            print(f"⚠️ Failed to send email for deal {deal.id}: {e}")

        return api_response(
            action="deal_canceled",
            data={"id": str(deal.id), "is_deleted": deal.is_deleted},
            status="success",
        )
