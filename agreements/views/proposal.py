from rest_framework.views import APIView, Request
from rest_framework.response import Response
from esign.utils.custom_response import api_response
from agreements.serializers.proposal import ProposalSerializer
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from agreements.models import Proposal
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from django.utils.dateparse import parse_date
from django.db.models import Q
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser


class ProposalView(APIView):
    # ✅ required for file uploads and JSON
    parser_classes = [MultiPartParser, FormParser, JSONParser]

    @swagger_auto_schema(
        operation_summary="Get Proposal",
        operation_description="Get Proposal",
        manual_parameters=[
            openapi.Parameter(
                "proposal_status",
                openapi.IN_QUERY,
                description="Filter proposals by status (draft, signed, sent)",
                type=openapi.TYPE_STRING,
                enum=["draft", "signed", "sent"],
                required=False,
            ),
            openapi.Parameter(
                "client_name",
                openapi.IN_QUERY,
                description="Filter by client organization name",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "title",
                openapi.IN_QUERY,
                description="Filter by proposal title",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "start_date",
                openapi.IN_QUERY,
                description="Filter proposals created after this date (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "end_date",
                openapi.IN_QUERY,
                description="Filter proposals created before this date (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: ProposalSerializer,
            400: "Invalid request",
            401: "Unauthorized",
        },
        tags=["agreements"],
    )
    def get(self, request: Request):
        proposal_status = request.query_params.get("proposal_status")
        client_name = request.query_params.get("client_name")
        title = request.query_params.get("title")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        proposals = Proposal.objects.all()

        # Handle proposal_status filter
        if proposal_status:
            proposals = proposals.filter(proposal_status=proposal_status)

        # Handle client name search
        if client_name:
            proposals = proposals.filter(proposal_for__name__icontains=client_name)

        # Handle title search
        if title:
            proposals = proposals.filter(proposal_title__icontains=title)

        # Handle date range filter
        if start_date:
            proposals = proposals.filter(created_at__date__gte=parse_date(start_date))
        if end_date:
            proposals = proposals.filter(created_at__date__lte=parse_date(end_date))

        proposals = proposals.order_by("-created_at")

        # Serialize proposals
        serializer = ProposalSerializer(
            proposals, many=True, context={'request': request})

        # Dashboard counts
        counts = {
            "draft_count": Proposal.objects.filter(proposal_status="draft").count(),
            "sent_count": Proposal.objects.filter(proposal_status="sent").count(),
            "signed_count": Proposal.objects.filter(proposal_status="signed").count(),
            "all_count": Proposal.objects.all().count(),
        }

        return api_response(
            action="data_retrieved",
            data={"counts": counts, "proposals": serializer.data},
            status="success",
        )

    @swagger_auto_schema(
        operation_summary="Create Proposal",
        operation_description="Create Proposal - Supports both JSON and multipart/form-data. For file uploads, use multipart/form-data.",
        manual_parameters=[
            openapi.Parameter(
                "action",
                openapi.IN_QUERY,
                description="Action to set proposal status (save=draft, save_and_sign=signed)",
                type=openapi.TYPE_STRING,
                enum=["save", "save_and_sign"],
                required=True,
            ),
            openapi.Parameter(
                "proposal_logo_file",
                openapi.IN_FORM,
                type=openapi.TYPE_FILE,
                description="Proposal logo file (optional) - only for multipart/form-data requests",
                required=False,
            ),
        ],
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "proposal_title": openapi.Schema(type=openapi.TYPE_STRING, description="Proposal title"),
                "proposal_status": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["draft", "signed", "sent"],
                    description="Proposal status",
                ),
                "vat_tax_and_currency": openapi.Schema(type=openapi.TYPE_INTEGER, description="VAT tax and currency"),
                "terms_and_conditions": openapi.Schema(type=openapi.TYPE_STRING, description="Terms and conditions"),
                "essential_information": openapi.Schema(type=openapi.TYPE_STRING, description="Essential information"),
                "additional_information": openapi.Schema(type=openapi.TYPE_STRING, description="Additional information"),
                "footer_information": openapi.Schema(type=openapi.TYPE_STRING, description="Footer information"),
                "proposal_expires_at": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format=openapi.FORMAT_DATETIME,
                    description="Proposal expiration date",
                ),
                "proposal_from": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Proposal from details",
                    properties={
                        "organization_name": openapi.Schema(type=openapi.TYPE_STRING),
                        "contact_number": openapi.Schema(type=openapi.TYPE_STRING),
                        "email": openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_EMAIL),
                    }
                ),
                "proposal_for": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Proposal for details",
                    properties={
                        "client_organization_name": openapi.Schema(type=openapi.TYPE_STRING),
                        "contact_number": openapi.Schema(type=openapi.TYPE_STRING),
                        "email": openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_EMAIL),
                    }
                ),
                "transporter_details": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Transporter details",
                    properties={
                        "transporter_id": openapi.Schema(type=openapi.TYPE_STRING),
                        "name": openapi.Schema(type=openapi.TYPE_STRING),
                        "mode_of_transport": openapi.Schema(type=openapi.TYPE_STRING),
                        "transport_doc_number": openapi.Schema(type=openapi.TYPE_STRING),
                        "transport_doc_date": openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
                        "vehicle_number": openapi.Schema(type=openapi.TYPE_STRING),
                        "vehicle_type": openapi.Schema(type=openapi.TYPE_STRING),
                    }
                ),
                "items": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Item details",
                    properties={
                        "item_name": openapi.Schema(type=openapi.TYPE_STRING),
                        "item_quantity": openapi.Schema(type=openapi.TYPE_STRING),
                        "item_rate": openapi.Schema(type=openapi.TYPE_STRING),
                        "item_amount": openapi.Schema(type=openapi.TYPE_STRING),
                        "item_description": openapi.Schema(type=openapi.TYPE_STRING),
                    }
                ),
            }
        ),
        consumes=["application/json", "multipart/form-data"],
        responses={200: "Successfully Created",
                   400: "Bad Request", 401: "Unauthorized"},
        tags=["agreements"],
    )
    def post(self, request: Request) -> Response:
        action = request.query_params.get("action")
        if not action:
            return api_response(
                action="data_not_created",
                data={
                    "error": "Query parameter 'action' is required. Use ?action=save or ?action=save_and_sign"},
                status="error",
            )

        if action not in ["save", "save_and_sign"]:
            return api_response(
                action="data_not_created",
                data={"error": "Invalid action. Allowed values: 'save', 'save_and_sign'"},
                status="error",
            )

        data = request.data.copy()

        # Explicitly override proposal_status based on query param
        if action == "save":
            data["proposal_status"] = "draft"
        elif action == "save_and_sign":
            data["proposal_status"] = "signed"

        serializer = ProposalSerializer(data=data, context={"request": request})
        if serializer.is_valid():
            proposal = serializer.save()

            # Enforce proposal_status again
            if action == "save" and proposal.proposal_status != "draft":
                proposal.proposal_status = "draft"
                proposal.save(update_fields=["proposal_status"])
            elif action == "save_and_sign" and proposal.proposal_status != "signed":
                proposal.proposal_status = "signed"
                proposal.save(update_fields=["proposal_status"])

            return api_response(
                action="data_created",
                data=ProposalSerializer(proposal).data,
                status="success",
            )

        return api_response(
            action="data_not_created",
            data=serializer.errors,
            status="error",
        )


class ProposalDetailView(APIView):
    """
    Retrieve, update or delete a Proposal by ID
    """
    @swagger_auto_schema(
        operation_summary="Retrieve Proposal by ID",
        responses={
            200: ProposalSerializer,
            404: "Proposal not found",
        },
        tags=["agreements"],
    )
    def get(self, request: Request, pk: str) -> Response:
        proposal = get_object_or_404(Proposal, pk=pk)
        serializer = ProposalSerializer(proposal)
        return api_response(
            action="data_retrieved",
            data=serializer.data,
            status="success",
        )

    @swagger_auto_schema(
        operation_summary="Update Proposal",
        request_body=ProposalSerializer,
        responses={
            200: ProposalSerializer,
            400: "Invalid request",
            404: "Proposal not found",
        },
        tags=["agreements"],
    )
    def put(self, request: Request, pk: str) -> Response:
        proposal = get_object_or_404(Proposal, pk=pk)
        serializer = ProposalSerializer(proposal, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return api_response(
                action="data_updated",
                data=serializer.data,
                status="success",
            )
        return api_response(
            action="data_not_updated",
            data=serializer.errors,
            status="error",
        )

    @swagger_auto_schema(
        operation_summary="Delete Proposal",
        responses={
            204: "Proposal deleted successfully",
            404: "Proposal not found",
        },
        tags=["agreements"],
    )
    def delete(self, request: Request, pk: str) -> Response:
        proposal = get_object_or_404(Proposal, pk=pk)
        proposal.delete()
        return api_response(
            action="data_deleted",
            data={"id": pk},
            status="success",
        )
