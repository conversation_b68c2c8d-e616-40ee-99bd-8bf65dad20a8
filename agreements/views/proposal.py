import json
from rest_framework.views import APIView, Request
from rest_framework.response import Response
from esign.utils.custom_response import api_response
from agreements.serializers.proposal import ProposalSerializer
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from agreements.models import Proposal
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from django.utils.dateparse import parse_date
from django.db.models import Q
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser


class ProposalView(APIView):
    # ✅ required for file uploads and JSON
    parser_classes = [JSONParser, MultiPartParser, FormParser]

    def get_serializer_class(self):
        """Return appropriate serializer based on content type"""
        if hasattr(self.request, 'content_type'):
            if 'multipart/form-data' in self.request.content_type:
                return ProposalSerializer  # For form data with files
        return ProposalSerializer  # Default for JSON

    @swagger_auto_schema(
        operation_summary="Get Proposal",
        operation_description="Get Proposal",
        manual_parameters=[
            openapi.Parameter(
                "proposal_status",
                openapi.IN_QUERY,
                description="Filter proposals by status (draft, signed, sent)",
                type=openapi.TYPE_STRING,
                enum=["draft", "signed", "sent"],
                required=False,
            ),
            openapi.Parameter(
                "client_name",
                openapi.IN_QUERY,
                description="Filter by client organization name",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "title",
                openapi.IN_QUERY,
                description="Filter by proposal title",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "start_date",
                openapi.IN_QUERY,
                description="Filter proposals created after this date (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "end_date",
                openapi.IN_QUERY,
                description="Filter proposals created before this date (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: ProposalSerializer,
            400: "Invalid request",
            401: "Unauthorized",
        },
        tags=["agreements"],
    )
    def get(self, request: Request):
        proposal_status = request.query_params.get("proposal_status")
        client_name = request.query_params.get("client_name")
        title = request.query_params.get("title")
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")

        proposals = Proposal.objects.all()

        # Handle proposal_status filter
        if proposal_status:
            proposals = proposals.filter(proposal_status=proposal_status)

        # Handle client name search
        if client_name:
            proposals = proposals.filter(proposal_for__name__icontains=client_name)

        # Handle title search
        if title:
            proposals = proposals.filter(proposal_title__icontains=title)

        # Handle date range filter
        if start_date:
            proposals = proposals.filter(created_at__date__gte=parse_date(start_date))
        if end_date:
            proposals = proposals.filter(created_at__date__lte=parse_date(end_date))

        proposals = proposals.order_by("-created_at")

        # Serialize proposals
        serializer = ProposalSerializer(
            proposals, many=True, context={'request': request})

        # Dashboard counts
        counts = {
            "draft_count": Proposal.objects.filter(proposal_status="draft").count(),
            "sent_count": Proposal.objects.filter(proposal_status="sent").count(),
            "signed_count": Proposal.objects.filter(proposal_status="signed").count(),
            "all_count": Proposal.objects.all().count(),
        }

        return api_response(
            action="data_retrieved",
            data={"counts": counts, "proposals": serializer.data},
            status="success",
        )

    @swagger_auto_schema(
        operation_summary="Create Proposal",
        operation_description="""
        Create Proposal - Supports both JSON and multipart/form-data:

        **For JSON requests (Postman):**
        - Use Content-Type: application/json
        - Send nested objects directly
        - File upload not supported in JSON mode

        **For multipart/form-data (Swagger UI with file upload):**
        - Use Content-Type: multipart/form-data
        - Send complex objects as JSON strings in form fields
        - File upload supported via proposal_logo_file field

        **Example JSON payload:**
        ```json
        {
            "proposal_title": "Development Proposal",
            "proposal_from": {
                "organization_name": "test2",
                "contact_number": "*********",
                "email": "<EMAIL>"
            },
            "proposal_for": {
                "client_organization_name": "test",
                "contact_number": "*********",
                "email": "<EMAIL>"
            },
            "vat_tax_and_currency": 2500,
            "transporter_details": {
                "transporter_id": "",
                "name": "test",
                "mode_of_transport": "test",
                "transport_doc_number": "",
                "transport_doc_date": "2025-09-04",
                "vehicle_number": "12345",
                "vehicle_type": ""
            },
            "items": {
                "item_name": "test",
                "item_quantity": "123",
                "item_rate": "2313",
                "item_amount": "2345",
                "item_description": "test"
            },
            "terms_and_conditions": "Terms...",
            "essential_information": "Essential info...",
            "additional_information": "Additional info...",
            "footer_information": "Footer info...",
            "proposal_expires_at": "2025-02-15T23:59:59Z"
        }
        ```
        """,
        manual_parameters=[
            openapi.Parameter(
                "action",
                openapi.IN_QUERY,
                description="Action to set proposal status (save=draft, save_and_sign=signed)",
                type=openapi.TYPE_STRING,
                enum=["save", "save_and_sign"],
                required=True,
            ),
        ],
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "proposal_title": openapi.Schema(type=openapi.TYPE_STRING, description="Proposal title"),
                "proposal_status": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["draft", "signed", "sent"],
                    description="Proposal status (will be overridden by action parameter)",
                ),
                "vat_tax_and_currency": openapi.Schema(type=openapi.TYPE_INTEGER, description="VAT tax and currency"),
                "terms_and_conditions": openapi.Schema(type=openapi.TYPE_STRING, description="Terms and conditions"),
                "essential_information": openapi.Schema(type=openapi.TYPE_STRING, description="Essential information"),
                "additional_information": openapi.Schema(type=openapi.TYPE_STRING, description="Additional information"),
                "footer_information": openapi.Schema(type=openapi.TYPE_STRING, description="Footer information"),
                "proposal_expires_at": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format=openapi.FORMAT_DATETIME,
                    description="Proposal expiration date (ISO format)",
                ),
                "proposal_from": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Proposal from details",
                    properties={
                        "organization_name": openapi.Schema(type=openapi.TYPE_STRING),
                        "contact_number": openapi.Schema(type=openapi.TYPE_STRING),
                        "email": openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_EMAIL),
                    },
                    required=["organization_name", "contact_number", "email"]
                ),
                "proposal_for": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Proposal for details",
                    properties={
                        "client_organization_name": openapi.Schema(type=openapi.TYPE_STRING),
                        "contact_number": openapi.Schema(type=openapi.TYPE_STRING),
                        "email": openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_EMAIL),
                    },
                    required=["client_organization_name", "contact_number", "email"]
                ),
                "transporter_details": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Transporter details",
                    properties={
                        "transporter_id": openapi.Schema(type=openapi.TYPE_STRING),
                        "name": openapi.Schema(type=openapi.TYPE_STRING),
                        "mode_of_transport": openapi.Schema(type=openapi.TYPE_STRING),
                        "transport_doc_number": openapi.Schema(type=openapi.TYPE_STRING),
                        "transport_doc_date": openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
                        "vehicle_number": openapi.Schema(type=openapi.TYPE_STRING),
                        "vehicle_type": openapi.Schema(type=openapi.TYPE_STRING),
                    },
                    required=["name"]
                ),
                "items": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Item details",
                    properties={
                        "item_name": openapi.Schema(type=openapi.TYPE_STRING),
                        "item_quantity": openapi.Schema(type=openapi.TYPE_STRING),
                        "item_rate": openapi.Schema(type=openapi.TYPE_STRING),
                        "item_amount": openapi.Schema(type=openapi.TYPE_STRING),
                        "item_description": openapi.Schema(type=openapi.TYPE_STRING),
                    },
                    required=["item_name", "item_quantity", "item_rate", "item_amount"]
                ),
            },
            required=["proposal_title", "proposal_from", "proposal_for", "vat_tax_and_currency", "transporter_details", "items"]
        ),
        responses={200: "Successfully Created", 400: "Bad Request", 401: "Unauthorized"},
        tags=["agreements"],
    )
    def post(self, request: Request) -> Response:
        action = request.query_params.get("action")
        if not action:
            return api_response(
                action="data_not_created",
                data={
                    "error": "Query parameter 'action' is required. Use ?action=save or ?action=save_and_sign"},
                status="error",
            )

        if action not in ["save", "save_and_sign"]:
            return api_response(
                action="data_not_created",
                data={"error": "Invalid action. Allowed values: 'save', 'save_and_sign'"},
                status="error",
            )

        data = request.data.copy()

        # Handle JSON strings from multipart/form-data
        json_fields = ['proposal_from', 'proposal_for', 'transporter_details', 'items']

        for field in json_fields:
            if field in data and isinstance(data[field], str):
                try:
                    data[field] = json.loads(data[field])
                except json.JSONDecodeError:
                    return api_response(
                        action="data_not_created",
                        data={"error": f"Invalid JSON format in field '{field}'"},
                        status="error",
                    )

        # Explicitly override proposal_status based on query param
        if action == "save":
            data["proposal_status"] = "draft"
        elif action == "save_and_sign":
            data["proposal_status"] = "signed"

        serializer = ProposalSerializer(data=data, context={"request": request})
        if serializer.is_valid():
            proposal = serializer.save()

            # Enforce proposal_status again
            if action == "save" and proposal.proposal_status != "draft":
                proposal.proposal_status = "draft"
                proposal.save(update_fields=["proposal_status"])
            elif action == "save_and_sign" and proposal.proposal_status != "signed":
                proposal.proposal_status = "signed"
                proposal.save(update_fields=["proposal_status"])

            return api_response(
                action="data_created",
                data=ProposalSerializer(proposal).data,
                status="success",
            )

        return api_response(
            action="data_not_created",
            data=serializer.errors,
            status="error",
        )




class ProposalDetailView(APIView):
    """
    Retrieve, update or delete a Proposal by ID
    """
    @swagger_auto_schema(
        operation_summary="Retrieve Proposal by ID",
        responses={
            200: ProposalSerializer,
            404: "Proposal not found",
        },
        tags=["agreements"],
    )
    def get(self, request: Request, pk: str) -> Response:
        proposal = get_object_or_404(Proposal, pk=pk)
        serializer = ProposalSerializer(proposal)
        return api_response(
            action="data_retrieved",
            data=serializer.data,
            status="success",
        )

    @swagger_auto_schema(
        operation_summary="Update Proposal",
        request_body=ProposalSerializer,
        responses={
            200: ProposalSerializer,
            400: "Invalid request",
            404: "Proposal not found",
        },
        tags=["agreements"],
    )
    def put(self, request: Request, pk: str) -> Response:
        proposal = get_object_or_404(Proposal, pk=pk)
        serializer = ProposalSerializer(proposal, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return api_response(
                action="data_updated",
                data=serializer.data,
                status="success",
            )
        return api_response(
            action="data_not_updated",
            data=serializer.errors,
            status="error",
        )

    @swagger_auto_schema(
        operation_summary="Delete Proposal",
        responses={
            204: "Proposal deleted successfully",
            404: "Proposal not found",
        },
        tags=["agreements"],
    )
    def delete(self, request: Request, pk: str) -> Response:
        proposal = get_object_or_404(Proposal, pk=pk)
        proposal.delete()
        return api_response(
            action="data_deleted",
            data={"id": pk},
            status="success",
        )


class ProposalFileUploadView(APIView):
    """Separate view for handling proposal creation with file uploads"""
    parser_classes = [MultiPartParser, FormParser]

    @swagger_auto_schema(
        operation_summary="Create Proposal with File Upload",
        operation_description="""
        Create Proposal using multipart/form-data - supports file uploads.

        **Important:** Complex objects (proposal_from, proposal_for, transporter_details, items)
        should be sent as JSON strings in the form fields.

        **Example form data:**
        - proposal_title: "Development Proposal"
        - proposal_from: '{"organization_name": "test2", "contact_number": "*********", "email": "<EMAIL>"}'
        - proposal_for: '{"client_organization_name": "test", "contact_number": "*********", "email": "<EMAIL>"}'
        - transporter_details: '{"transporter_id": "", "name": "test", "mode_of_transport": "test", "transport_doc_number": "", "transport_doc_date": "2025-09-04", "vehicle_number": "12345", "vehicle_type": ""}'
        - items: '{"item_name": "test", "item_quantity": "123", "item_rate": "2313", "item_amount": "2345", "item_description": "test"}'
        - proposal_logo_file: [file upload]
        """,
        manual_parameters=[
            openapi.Parameter(
                "action",
                openapi.IN_QUERY,
                description="Action to set proposal status (save=draft, save_and_sign=signed)",
                type=openapi.TYPE_STRING,
                enum=["save", "save_and_sign"],
                required=True,
            ),
            openapi.Parameter(
                "proposal_title",
                openapi.IN_FORM,
                type=openapi.TYPE_STRING,
                description="Proposal title",
                required=True,
            ),
            openapi.Parameter(
                "vat_tax_and_currency",
                openapi.IN_FORM,
                type=openapi.TYPE_INTEGER,
                description="VAT tax and currency",
                required=True,
            ),
            openapi.Parameter(
                "terms_and_conditions",
                openapi.IN_FORM,
                type=openapi.TYPE_STRING,
                description="Terms and conditions",
            ),
            openapi.Parameter(
                "essential_information",
                openapi.IN_FORM,
                type=openapi.TYPE_STRING,
                description="Essential information",
            ),
            openapi.Parameter(
                "additional_information",
                openapi.IN_FORM,
                type=openapi.TYPE_STRING,
                description="Additional information",
            ),
            openapi.Parameter(
                "footer_information",
                openapi.IN_FORM,
                type=openapi.TYPE_STRING,
                description="Footer information",
            ),
            openapi.Parameter(
                "proposal_expires_at",
                openapi.IN_FORM,
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATETIME,
                description="Proposal expiration date (ISO format)",
            ),
            openapi.Parameter(
                "proposal_logo_file",
                openapi.IN_FORM,
                type=openapi.TYPE_FILE,
                description="Proposal logo file (optional)",
                required=False,
            ),
            openapi.Parameter(
                "proposal_from",
                openapi.IN_FORM,
                type=openapi.TYPE_STRING,
                description='JSON string: {"organization_name": "test2", "contact_number": "*********", "email": "<EMAIL>"}',
                required=True,
            ),
            openapi.Parameter(
                "proposal_for",
                openapi.IN_FORM,
                type=openapi.TYPE_STRING,
                description='JSON string: {"client_organization_name": "test", "contact_number": "*********", "email": "<EMAIL>"}',
                required=True,
            ),
            openapi.Parameter(
                "transporter_details",
                openapi.IN_FORM,
                type=openapi.TYPE_STRING,
                description='JSON string: {"transporter_id": "", "name": "test", "mode_of_transport": "test", "transport_doc_number": "", "transport_doc_date": "2025-09-04", "vehicle_number": "12345", "vehicle_type": ""}',
                required=True,
            ),
            openapi.Parameter(
                "items",
                openapi.IN_FORM,
                type=openapi.TYPE_STRING,
                description='JSON string: {"item_name": "test", "item_quantity": "123", "item_rate": "2313", "item_amount": "2345", "item_description": "test"}',
                required=True,
            ),
        ],
        consumes=["multipart/form-data"],
        responses={200: "Successfully Created", 400: "Bad Request", 401: "Unauthorized"},
        tags=["agreements"],
    )
    def post(self, request: Request) -> Response:
        """Handle multipart/form-data requests with file uploads"""
        action = request.query_params.get("action")
        if not action:
            return api_response(
                action="data_not_created",
                data={
                    "error": "Query parameter 'action' is required. Use ?action=save or ?action=save_and_sign"},
                status="error",
            )

        if action not in ["save", "save_and_sign"]:
            return api_response(
                action="data_not_created",
                data={"error": "Invalid action. Allowed values: 'save', 'save_and_sign'"},
                status="error",
            )

        data = request.data.copy()

        # Handle JSON strings from multipart/form-data
        json_fields = ['proposal_from', 'proposal_for', 'transporter_details', 'items']

        for field in json_fields:
            if field in data and isinstance(data[field], str):
                try:
                    data[field] = json.loads(data[field])
                except json.JSONDecodeError:
                    return api_response(
                        action="data_not_created",
                        data={"error": f"Invalid JSON format in field '{field}'"},
                        status="error",
                    )

        # Explicitly override proposal_status based on query param
        if action == "save":
            data["proposal_status"] = "draft"
        elif action == "save_and_sign":
            data["proposal_status"] = "signed"

        serializer = ProposalSerializer(data=data, context={"request": request})
        if serializer.is_valid():
            proposal = serializer.save()

            # Enforce proposal_status again
            if action == "save" and proposal.proposal_status != "draft":
                proposal.proposal_status = "draft"
                proposal.save(update_fields=["proposal_status"])
            elif action == "save_and_sign" and proposal.proposal_status != "signed":
                proposal.proposal_status = "signed"
                proposal.save(update_fields=["proposal_status"])

            return api_response(
                action="data_created",
                data=ProposalSerializer(proposal).data,
                status="success",
            )

        return api_response(
            action="data_not_created",
            data=serializer.errors,
            status="error",
        )
