import json
from rest_framework.views import APIView, Request
from rest_framework.response import Response
from documents.models.document_models import UserDocument
from esign.utils.custom_response import api_response
from agreements.serializers.proposal import (
    ProposalDashboardSerializer,
    ProposalSerializer,
)
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from agreements.models import Proposal
from drf_yasg import openapi
from django.db.models import Q
from drf_yasg.utils import swagger_auto_schema
from django.utils.dateparse import parse_date
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>ars<PERSON>, FormPars<PERSON>, JSONParser
from typing import Any, Dict, Optional
from agreements.decorators import require_subscription_features
from documents.utils.jwt_verifier import JWTAccessTokenAuthentication



def _extract_first_error(errors):
    """
    Extract the first error from serializer errors.
    Handles both list and dict error formats.
    """
    if isinstance(errors, dict):
        # For dict errors, get the first key and its first error
        first_key = list(errors.keys())[0]
        first_value = errors[first_key]
        
        if isinstance(first_value, list):
            return f"{first_key}: {first_value[0]}"
        else:
            return f"{first_key}: {first_value}"
    
    elif isinstance(errors, list):
        # For list errors, return the first error
        return errors[0] if errors else "Unknown validation error"
    
    else:
        # For other types, convert to string
        return str(errors)
    
class ProposalAPIView(APIView):
    parser_classes = [JSONParser, MultiPartParser, FormParser]
    authentication_classes = [JWTAccessTokenAuthentication]

    def get_serializer_class(self) -> type[ProposalSerializer]:
        if hasattr(self.request, "content_type"):
            if "multipart/form-data" in self.request.content_type:
                return ProposalSerializer
        return ProposalSerializer

    # ---------------- GET (List / Retrieve) ----------------
    # @require_subscription_features(["proposal", "sales & pipeline"])
    @swagger_auto_schema(
        operation_summary="Get Proposals / Proposal by ID",
        manual_parameters=[
            openapi.Parameter(
                "proposal_status",
                openapi.IN_QUERY,
                description="Filter proposals by status (draft, signed, sent)",
                type=openapi.TYPE_STRING,
                enum=["draft", "signed", "sent"],
                required=False,
            ),
            openapi.Parameter(
                "client_name",
                openapi.IN_QUERY,
                description="Filter by client organization name",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "title",
                openapi.IN_QUERY,
                description="Filter by proposal title",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "start_date",
                openapi.IN_QUERY,
                description="Filter proposals created after this date (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "end_date",
                openapi.IN_QUERY,
                description="Filter proposals created before this date (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "search",
                openapi.IN_QUERY,
                description="Search by proposal status or client organization name",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={200: ProposalSerializer, 400: "Invalid request"},
        tags=["agreements"],
    )
    def get(self, request: Request, pk: Optional[str] = None) -> Response:
        if pk:  # retrieve single
            proposal: Proposal = get_object_or_404(Proposal, pk=pk)
            serializer = ProposalSerializer(proposal)
            return api_response("data_retrieved", serializer.data, "success")

        # list with filters
        proposals = Proposal.objects.all().exclude(is_deleted=True)
        user_doc = UserDocument.objects.all().exclude(is_deleted=True)

        if status := request.query_params.get("proposal_status"):
            proposals = proposals.filter(proposal_status=status)
        if name := request.query_params.get("client_name"):
            proposals = proposals.filter(proposal_for__name__icontains=name)
        if title := request.query_params.get("proposal_title"):
            proposals = proposals.filter(proposal_title__icontains=title)

        start = request.query_params.get("start_date")
        end = request.query_params.get("end_date")

        start_date = parse_date(start) if start else None
        end_date = parse_date(end) if end else None

        if start_date and end_date:
            proposals = proposals.filter(created_at__date__gte=start_date, created_at__date__lte=end_date)
        elif start_date:
            proposals = proposals.filter(created_at__date__gte=start_date)
        elif end_date:
            proposals = proposals.filter(created_at__date__lte=end_date)

        search = self.request.query_params.get('search')
        if search:
            proposals = proposals.filter(
                Q(proposal_status__icontains=search)
                | Q(proposal_title__icontains=search)
                | Q(proposal_for__name__icontains=search)
                | Q(proposal_for__assigner_org_name__icontains=search)
            )
            user_doc = user_doc.filter(
                Q(title__icontains=search) 
            )

        proposals = proposals.order_by("-created_at")
        serializer = ProposalSerializer(
            proposals, many=True, context={"request": request}
        )
        return api_response("data_retrieved", {"proposals": serializer.data}, "success")

    # ---------------- POST (Create / Withdraw) ----------------
    # @require_subscription_features(["proposal", "sales & pipeline"])
    @swagger_auto_schema(
        operation_summary="Create Proposal or Withdraw Proposal",
        manual_parameters=[
            openapi.Parameter(
                "action",
                openapi.IN_QUERY,
                description="Action: save, save_and_sign",
                type=openapi.TYPE_STRING,
                enum=["save", "save_and_sign"],
                required=True,
            ),
        ],
        tags=["agreements"],
    )
    def post(self, request: Request, pk: Optional[str] = None) -> Response:
        action: Optional[str] = request.query_params.get("action")
        data: Dict[str, Any] = request.data.copy()

        if pk:  # withdraw
            proposal: Proposal = get_object_or_404(Proposal, pk=pk, is_deleted=False)
            if proposal.is_withdrawn:
                return api_response("withdraw_failed", {"id": pk}, "error")
            proposal.is_withdrawn = True
            proposal.save(update_fields=["is_withdrawn"])
            return api_response("data_updated", {"id": pk}, "success")

        # create
        if action not in ["save", "save_and_send"]:
            return api_response(
                "data_not_created",
                {"error": "Invalid action. Use 'save' or 'save_and_send'"},
                "error",
            )

        if "multipart/form-data" in getattr(request, "content_type", ""):
            for field in [
                "proposal_from",
                "proposal_for",
                "transporter_details",
                "items",
                "shipped_to_data",
                "shipped_from_data",
            ]:
                if field in data and isinstance(data[field], str):
                    try:
                        data[field] = json.loads(data[field])
                    except Exception:
                        pass

        serializer = ProposalSerializer(data=data, context={"request": request})
        if serializer.is_valid():
            proposal: Proposal = serializer.save()
            return api_response(
                "data_created", ProposalSerializer(proposal).data, "success"
            )
        
        # Extract first error from serializer errors
        first_error = _extract_first_error(serializer.errors)
        return api_response("data_not_created", {"error": first_error}, "error")

    # ---------------- PUT (Update) ----------------
    # @require_subscription_features(["proposal", "sales & pipeline"])
    @swagger_auto_schema(
        operation_summary="Update Proposal",
        request_body=ProposalSerializer,
        responses={200: ProposalSerializer, 400: "Invalid request"},
        tags=["agreements"],
    )
    def put(self, request: Request, pk: str) -> Response:
        proposal: Proposal = get_object_or_404(Proposal, pk=pk)
        serializer = ProposalSerializer(
            proposal, data=request.data, partial=True, context={"request": request}
        )
        if serializer.is_valid():
            serializer.save()
            return api_response("data_updated", serializer.data, "success")
        
        # Extract first error from serializer errors
        first_error =_extract_first_error(serializer.errors)
        return api_response("data_not_updated", {"error": first_error}, "error")

    # @require_subscription_features(["proposal", "sales & pipeline"])
    @swagger_auto_schema(
        operation_summary="Delete Proposal (only if withdrawn)",
        responses={204: "Proposal deleted", 400: "Must withdraw first"},
        tags=["agreements"],
    )
    def delete(self, request: Request, pk: str) -> Response:
        proposal: Proposal = get_object_or_404(Proposal, pk=pk, is_deleted=False)
        if not proposal.is_withdrawn:
            return api_response("delete_failed", {"id": pk}, "error")
        proposal.is_deleted = True
        proposal.save(update_fields=["is_deleted"])
        return api_response("data_deleted", {"id": pk}, "success")


class ProposalDashboardAPIView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]

    # @require_subscription_features(["proposal", "sales & pipeline"])
    @swagger_auto_schema(
        operation_summary="Get Proposal Dashboard Counts",
        responses={200: ProposalDashboardSerializer},
        manual_parameters=[
            openapi.Parameter(
                "proposal_status",
                openapi.IN_QUERY,
                description="Filter proposals by status (draft, sent, signed)",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        tags=["agreements"],
    )
    def get(self, request: Request) -> Response:
        draft_count: int = Proposal.objects.filter(
            proposal_status="draft", is_deleted=False
        ).count()
        sent_count: int = Proposal.objects.filter(
            proposal_status="sent", is_deleted=False
        ).count()
        signed_count: int = Proposal.objects.filter(
            proposal_status="signed", is_deleted=False
        ).count()
        all_count: int = Proposal.objects.filter(is_deleted=False).count()

        proposals = Proposal.objects.filter(is_deleted=False)

        proposal_status: Optional[str] = request.query_params.get("proposal_status")
        if proposal_status in ["draft", "sent", "signed"]:
            proposals = proposals.filter(proposal_status=proposal_status)

        serializer = ProposalDashboardSerializer(
            {
                "draft_count": draft_count,
                "sent_count": sent_count,
                "signed_count": signed_count,
                "all_count": all_count,
                "proposal_data": proposals,
            }
        )

        return Response(serializer.data, status=200)
