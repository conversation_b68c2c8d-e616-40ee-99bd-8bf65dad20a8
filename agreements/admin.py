from django.contrib import admin
from .models.proposal_models import Proposal, TransporterDetails, TaxDetails


# @admin.register(TransporterDetails)
# class TransporterDetailsAdmin(admin.ModelAdmin):
#     list_display = ("transporter_id", "name", "mode_of_transport", "vehicle_number", "vehicle_type")
#     search_fields = ("name", "mode_of_transport", "vehicle_number")
#     list_filter = ("mode_of_transport",)


# @admin.register(TaxDetails)
# class TaxDetailsAdmin(admin.ModelAdmin):
#     list_display = ("tax_name", "tax_percentage", "created_at")
#     search_fields = ("tax_name",)


# @admin.register(Proposal)
# class ProposalAdmin(admin.ModelAdmin):
#     list_display = (
#         "proposal_id",
#         "proposal_title",
#         "proposal_status",
#         "proposal_from",
#         "proposal_for"
#     )
#     search_fields = ("proposal_title", "proposal_id", "proposal_status")
#     list_filter = ("proposal_status", "proposal_expires_at", "created_at")
#     date_hierarchy = "proposal_expires_at"
