from urllib.parse import parse_qs

import jwt
from channels.db import database_sync_to_async
from django.conf import settings
from django.contrib.auth.models import AnonymousUser

from accounts.models.users import User
from accounts.services.jwt_authentication.authentication import fernet_decrypt

import logging

logger = logging.getLogger('app')

secret_key = settings.SECRET_KEY  # Secret key to decode JWT
algorithm = "HS256"

@database_sync_to_async
def returnUser(token_string):
    try:
        payload = jwt.decode(token_string, secret_key, algorithms=[algorithm])
        reference_id = fernet_decrypt(payload["reference_id"].encode())
        user = User.objects.get(reference_id=reference_id)
        logger.info(
            "User authenticated via token",
            extra={'user_reference_id': reference_id}
        )
        return user
    except jwt.ExpiredSignatureError:
        logger.warning("Expired JWT token received")
        return AnonymousUser()
    except jwt.InvalidTokenError:
        logger.warning("Invalid JWT token received")
        return AnonymousUser()
    except User.DoesNotExist:
        logger.error(
            "User not found for token",
            extra={'reference_id': reference_id if 'reference_id' in locals() else None}
        )
        return AnonymousUser()
    except Exception as e:
        logger.error(
            "Error authenticating token",
            extra={'error': str(e)},
            exc_info=True
        )
        return AnonymousUser()


class TokenAuthMiddleWare:
    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        try:
            query_string = scope["query_string"]
            query_params = query_string.decode()
            query_dict = parse_qs(query_params)
            
            if 'token' not in query_dict:
                logger.warning("No token provided in WebSocket connection")
                return await self.app(scope, receive, send)
                
            token = query_dict["token"][0]
            user = await returnUser(token)
            scope["auth_user"] = user
            
            logger.debug(
                "Token authentication processed",
                extra={
                    'authenticated': user.is_authenticated,
                    'path': scope.get('path', '')
                }
            )
            
            return await self.app(scope, receive, send)
            
        except Exception as e:
            logger.error(
                "Error in token authentication middleware",
                extra={'error': str(e)},
                exc_info=True
            )
            return await self.app(scope, receive, send)
