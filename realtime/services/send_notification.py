import logging

from asgiref.sync import async_to_sync

# from django.utils import timezone
from channels.layers import get_channel_layer
from django.apps import apps

from accounts.services.bankid_authentication.bankid_utils import send_notification_email
from accounts.services.mobile_authentication.mobile_clients import (
    send_notfication_via_sms,
)

logger = logging.getLogger('app')


def send_document_created_notification(sender, **kwargs):
    try:
        instance = kwargs.get("instance")
        if instance is None:
            logger.error("Signal received without 'instance' keyword argument")
            return

        try:
            user = instance.user
            if not user:
                logger.error("No user associated with document instance")
                return
        except AttributeError:
            logger.error("Failed to get user from instance")
            return

        NotificationModel = apps.get_model("realtime", "Notification")      
        logger.info(
            "Creating document creation notification",
            extra={
                'document_id': str(instance.id),
                'user_id': str(user.id)
            }
        )

        notification = NotificationModel.objects.create(
            user=user,
            notification_type=NotificationModel.DOCUMENT_CREATED,
            message=f"Document '{instance.title}' Created.",
            user_document=instance,
            data={"document_title": instance.title},
        )

        message = f"Document '{notification.user_document.title}' has been created. Action performed at {notification.created_at}."

        if notification.email_notification and user.get_decrypted_email():
            try:
                send_notification_email(user.get_decrypted_email(), message)
                logger.info(
                    "Email notification sent",
                    extra={
                        'user_id': str(user.id),
                        'notification_id': notification.id
                    }
                )
            except Exception as e:
                logger.error(
                    "Failed to send email notification",
                    extra={
                        'user_id': str(user.id),
                        'error': str(e)
                    }
                )

        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            f"user_{user.reference_id}",
            {"type": "send_notification"}
        )

        logger.info(
            "Document creation notification completed",
            extra={
                'document_id': str(instance.id),
                'notification_id': notification.id
            }
        )

    except Exception as e:
        logger.error(
            "Error in document creation notification",
            extra={'error': str(e)},
            exc_info=True
        )


def send_signer_added_notification(sender, **kwargs):
    try:
        instance = kwargs.get("instance")
        if instance is None:
            logger.error("Signal received without 'instance' keyword argument.")
            return

        # Try to get the user, with more robust error handling
        try:
            user = instance.document.created_by
        except AttributeError:
            logger.error(
                f"User not found for instance {instance}. Notification not sent."
            )
            return

        # Get the Notification model dynamically
        NotificationModel = apps.get_model("realtime", "Notification")
        # Create notification
        notification = NotificationModel.objects.create(
            user=user,
            notification_type=NotificationModel.SIGNER_ADDED,
            message=f"Signer '{instance.get_decrypted_name()}' Created.",
            user_document=instance.document,
            data={"document_title": instance.document.title},
        )
        print(notification)
        message = f"Signer has been added for document '{notification.user_document.title}'. Action performed at {notification.created_at}."
        # Send SMS if enabled
        # if notification.sms_notification:
        #     if user.get_decrypted_phone_number():
        #         send_notfication_via_sms(user.get_decrypted_phone_number(), message)

        # Send email if enabled
        if notification.email_notification:
            if user.get_decrypted_email():
                send_notification_email(user.get_decrypted_email(), message)

        # Get channel layer
        channel_layer = get_channel_layer()
        # Send notification to user's group
        async_to_sync(channel_layer.group_send)(
            f"user_{user.reference_id}", {"type": "send_notification"}
        )

        logger.info(
            f"Notification sent successfully for user {user.reference_id}, "
            f"document {instance.document.title}"
        )

    except Exception as e:
        logger.error(f"Comprehensive error in notification sending: {e}", exc_info=True)


def send_document_approved_notification(sender, **kwargs):
    """Sends a notification when a document is approved."""
    try:
        instance = kwargs.get("instance")
        if instance is None:
            logger.error("Signal received without 'instance' keyword argument.")
            return

        # Try to get the user, with more robust error handling
        try:
            user = instance.document.created_by
        except AttributeError:
            logger.error(
                f"User not found for instance {instance}. Notification not sent."
            )
            return
        if instance.status == "signed":
            # Get the Notification model dynamically
            NotificationModel = apps.get_model("realtime", "Notification")
            # Create notification
            notification = NotificationModel.objects.create(
                user=user,
                notification_type=NotificationModel.DOCUMENT_SIGNER_APPROVED,
                message=f"Signer '{instance.get_decrypted_name()}' Approved.",
                user_document=instance.document,
                data={"document_title": instance.document.title},
            )
            print(notification)

            message = f"Document signer has been approved for document '{notification.user_document.title}'. Action performed at {notification.created_at}."
            # Get channel layer
            # Send SMS if enabled
            # if notification.sms_notification:
            #     if user.get_decrypted_phone_number():
            #         send_notfication_via_sms(user.get_decrypted_phone_number(), message)

            # Send email if enabled
            if notification.email_notification:
                if user.get_decrypted_email():
                    send_notification_email(user.get_decrypted_email(), message)

            channel_layer = get_channel_layer()
            # Send notification to user's group
            async_to_sync(channel_layer.group_send)(
                f"user_{user.reference_id}", {"type": "send_notification"}
            )

            logger.info(
                f"Notification sent successfully for user {user.reference_id}, "
                f"document {instance.document.title}"
            )

    except Exception as e:
        logger.error(f"Comprehensive error in notification sending: {e}", exc_info=True)


def send_document_deleted_notification(sender, **kwargs):
    try:
        instance = kwargs.get("instance")
        if not instance:
            logger.error("No instance provided for deletion notification")
            return

        user = instance.user
        if not user:
            logger.error(
                "No user associated with deleted document",
                extra={'document_id': str(instance.id)}
            )
            return

        if instance.is_deleted:
            NotificationModel = apps.get_model("realtime", "Notification")
            
            logger.info(
                "Creating document deletion notification",
                extra={
                    'document_id': str(instance.id),
                    'user_id': str(user.id)
                }
            )

            notification = NotificationModel.objects.create(
                user=user,
                notification_type=NotificationModel.DOCUMENT_DELETED,
                message=f"Document '{instance.title}' Deleted.",
                user_document=instance,
                data={"document_title": instance.title},
            )

            message = f"Document '{notification.user_document.title}' has been deleted. Action performed at {notification.created_at}."

            if notification.email_notification and user.get_decrypted_email():
                try:
                    send_notification_email(user.get_decrypted_email(), message)
                    logger.info(
                        "Deletion email notification sent",
                        extra={
                            'user_id': str(user.id),
                            'notification_id': notification.id
                        }
                    )
                except Exception as e:
                    logger.error(
                        "Failed to send deletion email notification",
                        extra={
                            'user_id': str(user.id),
                            'error': str(e)
                        }
                    )

            channel_layer = get_channel_layer()
            async_to_sync(channel_layer.group_send)(
                f"user_{user.reference_id}",
                {"type": "send_notification"}
            )

            logger.info(
                "Document deletion notification completed",
                extra={
                    'document_id': str(instance.id),
                    'notification_id': notification.id
                }
            )

    except Exception as e:
        logger.error(
            "Error in document deletion notification",
            extra={'error': str(e)},
            exc_info=True
        )
