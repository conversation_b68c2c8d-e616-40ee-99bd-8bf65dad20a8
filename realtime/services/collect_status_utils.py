import asyncio
import logging

# from accounts.services.jwt_authentication.authentication import fernet_decrypt
from accounts.models.users import SessionTrack, User
from accounts.services.bankid_authentication.bankid_utils import save_user_details
from asgiref.sync import sync_to_async
from cryptography.fernet import Fernet

# from django.conf import settings
from django.contrib.auth.hashers import check_password

# from accounts.services.jwt_authentication.authentication import JWTTokenManager

logger = logging.getLogger("app")
# cipher_suite = Fernet(settings.ENCRYPTION_KEY)


@sync_to_async
def user_with_matching_hash(hash_password):
    try:
        exists = User.objects.filter(private_number=hash_password).exists()
        logger.debug("Checked for user with matching hash", extra={"exists": exists})
        return exists
    except Exception as e:
        logger.error(
            "Error checking for user with matching hash",
            extra={"error": str(e)},
            exc_info=True,
        )
        raise


@sync_to_async
def user_with_matching_personal_number(personal_number):
    try:
        for user in User.objects.all():
            if check_password(personal_number, user.private_number):
                logger.info(
                    "Found user with matching personal number",
                    extra={"user_id": str(user.id)},
                )
                return user
        logger.debug("No user found with matching personal number")
        return None
    except Exception as e:
        logger.error(
            "Error checking for user with matching personal number",
            extra={"error": str(e)},
            exc_info=True,
        )
        raise


@sync_to_async
def create_user(hash_password, jwt_manager):
    try:
        user = User.objects.create(private_number=hash_password)
        access_token = jwt_manager.generate_token(user.reference_id)
        refresh_token = jwt_manager.generate_token(
            user.reference_id, token_type="refresh"
        )

        logger.info("New user created", extra={"user_id": str(user.id)})

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
        }
    except Exception as e:
        logger.error("Error creating new user", extra={"error": str(e)}, exc_info=True)
        raise


async def async_save_user_details(
    personal_number, user_type=None, personal_info=None, is_onetime=False
):
    try:
        logger.info(
            "Saving user details asynchronously",
            extra={"user_type": user_type, "is_onetime": is_onetime},
        )
        return await asyncio.to_thread(
            save_user_details,
            personal_number,
            "sweden_bank_id",
            personal_info,
            is_onetime,
        )
    except Exception as e:
        logger.error(
            "Error saving user details asynchronously",
            extra={"user_type": user_type, "error": str(e)},
            exc_info=True,
        )
        raise


# async def handle_user_creation(personal_number, jwt_manager,is_onetime=False):
#     hash_password = make_password(personal_number)
#     # Check if a user with the hashed password exists
#     existing_user = await user_with_matching_hash(hash_password)
#     if existing_user:
#         access_token = jwt_manager.generate_token(existing_user.reference_id)
#         if is_onetime:
#             access_token = "onetime" + access_token
#         refresh_token = jwt_manager.generate_token(
#             existing_user.id, token_type="refresh"
#         )

#         print("User with this password already exists")
#         return {
#             "access_token": access_token,
#             "refresh_token": refresh_token,
#         }

#     # Check if any user matches the personal number
#     exist_password = await user_with_matching_personal_number(personal_number)
#     if exist_password:
#         access_token = jwt_manager.generate_token(exist_password.reference_id)
#         if is_onetime:
#             access_token = "onetime" + access_token
#         refresh_token = jwt_manager.generate_token(
#             exist_password.reference_id, token_type="refresh"
#         )

#         return {
#             "access_token": access_token,
#             "refresh_token": refresh_token,
#         }

#     # If no match, create a new user
#     print("Created new user")
#     return await create_user(hash_password, jwt_manager)


async def handle_user_creation(
    personal_number,
    jwt_manager,
    personal_info=None,
    is_onetime=False,
    client_ip=None,
    meta_data=None,
):
    try:
        logger.info("Handling user creation", extra={"is_onetime": is_onetime})
        context = await async_save_user_details(
            personal_number, "sweden_bank_id", personal_info, is_onetime
        )
        logger.info("User creation handled successfully")
        user_detail_dict = context.get(
            "user_details", None
        )  # Assuming save_user_details returns user in context
        # print("---------------------here-----------------------",user_detail_dict)
        user_reference_id = user_detail_dict.get("user_refernce_id")
        user = await sync_to_async(User.objects.filter)(reference_id=user_reference_id)
        user = await sync_to_async(user.first)()
        user.meta_data = meta_data
        await sync_to_async(user.save)()

        context["user_details"]["user_type"] = "sweden_bank_id"
        context["bank_id_info"] = meta_data
        # if user and client_ip:
        #     # Check existing sessions
        #     existing_session = await sync_to_async(SessionTrack.objects.filter)(
        #         user=user,
        #         is_logged_in=True
        #     )
        #     existing_session = await sync_to_async(existing_session.exists)()

        #     if existing_session:
        #         logger.warning(f"User {user.id} already has an active session")
        #         return {
        #             "user_reference_id": user_reference_id,
        #             "status": "error",
        #             "message": "User is already logged in on another device",
        #             "status_code": 403
        #         }

        #     # Create new session
        #     await sync_to_async(SessionTrack.objects.update_or_create)(
        #         user=user,
        #         ip_address=client_ip,
        #         is_logged_in=True
        #     )
        #     logger.info(f"Created new session for user {user.id}")

        return context
    except Exception as e:
        logger.error(
            "Error handling user creation", extra={"error": str(e)}, exc_info=True
        )
        raise
