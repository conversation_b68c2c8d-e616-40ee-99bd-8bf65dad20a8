# Generated by Django 5.1.1 on 2025-03-24 17:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("documents", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("document_created", "Document Created"),
                            ("signer_added", "Signer Added"),
                            ("document_signer_approved", "Document Signer Approved"),
                            ("document_deleted", "Document Deleted"),
                        ],
                        default="hidden_default",
                        max_length=255,
                    ),
                ),
                ("message", models.TextField()),
                ("reference_id", models.CharField(max_length=255)),
                ("is_read", models.BooleanField(default=False)),
                ("push_notification", models.BooleanField(default=True)),
                ("email_notification", models.BooleanField(default=True)),
                ("sms_notification", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("read_at", models.DateTimeField(blank=True, null=True)),
                ("data", models.JSONField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user_document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="documents.userdocument",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="NotificationSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("push_notification", models.BooleanField(default=True)),
                ("email_notification", models.BooleanField(default=True)),
                ("sms_notification", models.BooleanField(default=True)),
                (
                    "document_signed_email_notification",
                    models.BooleanField(default=True),
                ),
                (
                    "document_signed_push_notification",
                    models.BooleanField(default=True),
                ),
                ("document_signed_sms_notification", models.BooleanField(default=True)),
                (
                    "subscription_plan_payment_pending_push_reminder",
                    models.BooleanField(default=True),
                ),
                (
                    "subscription_plan_payment_pending_email_reminder",
                    models.BooleanField(default=True),
                ),
                (
                    "subscription_plan_payment_pending_sms_reminder",
                    models.BooleanField(default=True),
                ),
                (
                    "subscription_plan_deactivated_email_reminder",
                    models.BooleanField(default=True),
                ),
                (
                    "subscription_plan_deactivated_sms_reminder",
                    models.BooleanField(default=True),
                ),
                (
                    "subscription_plan_deactivated_push_reminder",
                    models.BooleanField(default=True),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
