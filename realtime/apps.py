# from django.apps import AppConfig, apps

# from realtime.signals import document_created, signer_added


# class RealtimeConfig(AppConfig):
#     name = "realtime"

#     def ready(self):
#         from django.db.models.signals import post_save

#         from realtime.services.send_notification import (
#             send_document_approved_notification,
#             send_document_created_notification,
#             send_document_deleted_notification,
#             send_signer_added_notification,
#         )

#         # UserDocumentModel = apps.get_model("documents", "UserDocument")
#         # DocumentSignerModel = apps.get_model("documents", "DocumentSigner")

#         # document_created.connect(send_document_created_notification)
#         # signer_added.connect(send_signer_added_notification)
#         # post_save.connect(send_document_deleted_notification, sender=UserDocumentModel)
#         # post_save.connect(
#         #     send_document_approved_notification, sender=DocumentSignerModel
#         # )
