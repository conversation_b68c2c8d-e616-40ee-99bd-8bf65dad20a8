# Create your models here.
import uuid

from django.db import models
from django.utils import timezone

from accounts.models.users import User
from accounts.services.jwt_authentication.authentication import fernet_encrypt
from documents.models.document_models import UserDocument


class Notification(models.Model):
    # Define all choices explicitly
    DOCUMENT_CREATED = "document_created"
    SIGNER_ADDED = "signer_added"
    DOCUMENT_SIGNER_APPROVED = "document_signer_approved"
    DOCUMENT_DELETED = "document_deleted"

    # Define a special hidden default value
    HIDDEN_DEFAULT = "hidden_default"  # This is not visible to the user

    NOTIFICATION_TYPES = [
        (DOCUMENT_CREATED, "Document Created"),
        (SIGNER_ADDED, "Signer Added"),
        (DOCUMENT_SIGNER_APPROVED, "Document Signer Approved"),
        (DOCUMENT_DELETED, "Document Deleted"),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)

    is_deleted = models.Bo<PERSON>anField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)

    notification_type = models.CharField(
        max_length=255,
        choices=NOTIFICATION_TYPES,  # This limits user-visible choices
        default=HIDDEN_DEFAULT,  # Hidden default value
    )
    message = models.TextField()
    reference_id = models.CharField(max_length=255)
    # Encrypting reference_id
    is_read = models.BooleanField(default=False)
    push_notification = models.BooleanField(default=True)
    email_notification = models.BooleanField(default=True)
    sms_notification = models.BooleanField(default=True)
    user_document = models.ForeignKey(
        UserDocument, on_delete=models.CASCADE
    )  # Foreign key if you have a Document model
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)
    data = models.JSONField(null=True, blank=True)  # Use JSONField for flexibility

    class Meta:
        ordering = ["-created_at"]  # Order by creation time (newest first)

    def save(self, *args, **kwargs):
        # Encrypt fields if they are being set for the first time
        if not self.reference_id:
            raw_reference_id = str(uuid.uuid4())
            self.reference_id = fernet_encrypt(raw_reference_id).decode()
        super().save(*args, **kwargs)

    def mark_as_read(self):
        self.is_read = True
        self.read_at = timezone.now()
        self.save()

    def __str__(self):
        return f"Notification for {self.user} ({self.notification_type})"


class NotificationSettings(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    push_notification = models.BooleanField(default=True)
    email_notification = models.BooleanField(default=True)
    sms_notification = models.BooleanField(default=True)

    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)

    document_signed_email_notification = models.BooleanField(default=True)
    document_signed_push_notification = models.BooleanField(default=True)
    document_signed_sms_notification = models.BooleanField(default=True)

    subscription_plan_payment_pending_push_reminder = models.BooleanField(default=True)
    subscription_plan_payment_pending_email_reminder = models.BooleanField(default=True)
    subscription_plan_payment_pending_sms_reminder = models.BooleanField(default=True)

    subscription_plan_deactivated_email_reminder = models.BooleanField(default=True)
    subscription_plan_deactivated_sms_reminder = models.BooleanField(default=True)
    subscription_plan_deactivated_push_reminder = models.BooleanField(default=True)
