import logging
import async<PERSON>
import base64
from io import BytesIO

from channels.generic.websocket import AsyncWebsocketConsumer
from django.core.cache import cache

from accounts.services.bankid_authentication.bankid_utils import generate_qr_code

logger = logging.getLogger('app')


class QRCodeConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        try:
            self.order_ref = self.scope["url_route"]["kwargs"]["order_ref"]
            logger.info(
                "QR code connection attempt",
                extra={'order_ref': self.order_ref}
            )

            session_data = cache.get(f"bankid_auth_response_{self.order_ref}", None)
            if session_data is None:
                logger.error(
                    "BankID session data not found for QR code",
                    extra={'order_ref': self.order_ref}
                )
                await self.close()
                return

            self.auth_response = session_data["response_data"]
            self.verification_start_time = session_data["verification_start_time"]
            await self.accept()
            logger.info(
                "QR code connection established",
                extra={'order_ref': self.order_ref}
            )

            qr_duration = 30
            try:
                for i in range(qr_duration):
                    qr_image = generate_qr_code(
                        self.auth_response, 
                        self.verification_start_time
                    )
                    
                    buffer = BytesIO()
                    qr_image.save(buffer, format="PNG")
                    qr_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")

                    await self.send(text_data=f"data:image/png;base64,{qr_base64}")
                    logger.debug(
                        "QR code sent",
                        extra={
                            'order_ref': self.order_ref,
                            'iteration': i + 1
                        }
                    )
                    await asyncio.sleep(1)

            except asyncio.CancelledError:
                logger.info(
                    "QR code generation cancelled",
                    extra={'order_ref': self.order_ref}
                )

        except Exception as e:
            logger.error(
                "Error in QR code consumer",
                extra={
                    'order_ref': getattr(self, 'order_ref', None),
                    'error': str(e)
                },
                exc_info=True
            )
            await self.close()
        finally:
            logger.info(
                "Cleaning up QR code session",
                extra={'order_ref': self.order_ref}
            )
            cache.delete(f"bankid_auth_response_{self.order_ref}")
