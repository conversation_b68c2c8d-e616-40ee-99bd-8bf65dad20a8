import json
import logging

from asgiref.sync import async_to_sync
from channels.generic.websocket import WebsocketConsumer
from django.apps import apps
from django.contrib.auth import get_user_model

logger = logging.getLogger(__name__)

User = get_user_model()


class NotificationConsumer(WebsocketConsumer):
    def connect(self):
        try:
            user_reference_id = self.scope["url_route"]["kwargs"]["user_reference_id"]
            logger.info(
                "Notification connection attempt",
                extra={'user_reference_id': user_reference_id}
            )

            self.auth_user = self.scope["auth_user"]
            if not self.auth_user.is_authenticated:
                logger.warning(
                    "Unauthenticated notification connection attempt",
                    extra={'user_reference_id': user_reference_id}
                )
                self.close(code=4001)
                return

            try:
                user = User.objects.get(reference_id=user_reference_id)
                logger.debug(
                    "User found for notification connection",
                    extra={'user_id': str(user.id)}
                )
            except User.DoesNotExist:
                logger.error(
                    "User not found for notification connection",
                    extra={'user_reference_id': user_reference_id}
                )
                self.close(code=4002)
                return

            self.group_name = f"user_{user_reference_id}"
            async_to_sync(self.channel_layer.group_add)(self.group_name, self.channel_name)
            self.accept()
            logger.info(
                "Notification connection established",
                extra={'user_reference_id': user_reference_id}
            )

        except Exception as e:
            logger.error(
                "Error in notification connection",
                extra={
                    'user_reference_id': user_reference_id if 'user_reference_id' in locals() else None,
                    'error': str(e)
                },
                exc_info=True
            )
            self.close()

    def disconnect(self, close_code):
        try:
            if hasattr(self, "group_name"):
                # Remove the channel from the group
                async_to_sync(self.channel_layer.group_discard)(
                    self.group_name, self.channel_name
                )
            logger.info(f"WebSocket disconnected for group {self.group_name}")
        except Exception as e:
            logger.error(f"Error in WebSocket disconnection: {e}")

    def receive(self, text_data):
        # Optional: Handle incoming messages if needed
        # Additional authorization checks if needed
        try:
            data = json.loads(text_data)
            if data["type"] == "mark_as_read":
                self.mark_notification_as_read(data["notification_reference_id"])
            else:
                logger.info(f"Received message: {data}")
        except json.JSONDecodeError:
            logger.error("Invalid JSON received")

    def send_notification(self, event):
        try:
            NotificationModel = apps.get_model("realtime", "Notification")
            user = User.objects.get(reference_id=self.user_id)
            notifications = list(
                    NotificationModel.objects.filter(user=user)
                    .order_by("-created_at")
                    .values(
                        "id",
                        "message",
                        "reference_id",
                        "document_reference_id",
                        "notification_type",
                        "created_at",
                        "is_read",
                        "read_at",
                    )
                )
            formatted_notifications = [
                {**n, "created_at": str(n["created_at"].isoformat())}
                for n in notifications
            ]

            logger.info(
                "Sending notifications to user",
                extra={
                    'user_reference_id': self.user_id,
                    'notification_count': len(notifications)
                }
            )
            self.send(text_data=json.dumps({
                "type": "notification",
                "notifications": formatted_notifications
            }))

        except Exception as e:
            logger.error(
                "Error sending notification",
                extra={
                    'user_reference_id': self.user_id,
                    'error': str(e)
                },
                exc_info=True
            )

    def send_all_notifications(self):
        try:
            NotificationModel = apps.get_model("realtime", "Notification")
            try:
                user = User.objects.get(reference_id=self.user_id)
            except User.DoesNotExist:
                logger.error(f"User with reference ID {user.reference_id} not found")
                # self.close()
                return
            notifications = list(
                NotificationModel.objects.filter(user=user)
                .order_by("-created_at")
                .values(
                    "id",
                    "message",
                    "reference_id",
                    "document_reference_id",
                    "notification_type",
                    "created_at",
                    "is_read",
                    "read_at",
                )
            )
            formatted_notifications = [
                {**n, "created_at": str(n["created_at"].isoformat())}
                for n in notifications
            ]
            self.send(
                text_data=json.dumps(
                    {
                        "type": "initial_notifications",
                        "notifications": formatted_notifications,
                    }
                )
            )
            logger.info("Initial notifications sent")
        except Exception as e:
            logger.exception(f"Error sending initial notifications: {e}")

    def mark_notification_as_read(self, notification_reference_id):
        try:
            NotificationModel = apps.get_model(
                "realtime", "Notification"
            )  # replace 'your_app_name'
            notification = NotificationModel.objects.get(
                reference_id=notification_reference_id,
                user__reference_id=self.auth_user.reference_id,
            )
            notification.mark_as_read()
            logger.info(f"Notification {notification_reference_id} marked as read")
            # Optionally, re-send the updated notification list to the client.
            self.send_all_notifications()  # Or send only updated notification
        except NotificationModel.DoesNotExist:
            logger.error(
                f"""Notification {notification_reference_id} not found or
                not belonging to the user"""
            )
        except Exception as e:
            logger.exception(f"Error marking notification as read: {e}")
