import logging
import async<PERSON>
import json
from datetime import datetime
from urllib.parse import parse_qs

from channels.generic.websocket import AsyncWebsocketConsumer
from rest_framework import status

from accounts.services.bankid_authentication.bankid_client import collect_status
from accounts.services.bankid_authentication.bankid_utils import (
    get_bankid_message,
    i18n_message,
)
from accounts.services.jwt_authentication.authentication import JWTTokenManager
from realtime.services.collect_status_utils import handle_user_creation

logger = logging.getLogger('app')


class CollectOneStatusConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        try:
            self.order_ref = self.scope["url_route"]["kwargs"]["order_ref"]
            self.language = self.scope["url_route"]["kwargs"].get("language", "en")
            
            logger.info(
                "One-time BankID collect status connection attempt",
                extra={
                    'order_ref': self.order_ref,
                    'language': self.language
                }
            )

            query_params = parse_qs(self.scope["query_string"].decode())
            self.document_content = query_params.get("document", [None])[0]
            self.jwt_manager = JWTTokenManager()
            
            await self.accept()
            logger.info(
                "One-time BankID collect status connection established",
                extra={'order_ref': self.order_ref}
            )

            await self.collect_status_websocket()

        except Exception as e:
            logger.error(
                "Error establishing one-time BankID collect status connection",
                extra={
                    'order_ref': getattr(self, 'order_ref', None),
                    'error': str(e)
                },
                exc_info=True
            )
            await self.close()

    async def collect_status_websocket(self):
        try:
            response_data = collect_status(self.order_ref)
            logger.info(
                "One-time BankID status collected",
                extra={
                    'order_ref': self.order_ref,
                    'status': response_data.get('status')
                }
            )

            if response_data.get("status") == "complete":
                token = await self._handle_completion_data(
                    response_data,
                    self.document_content
                )
                completion_data = response_data.get("completionData")
                personal_details = completion_data.get("user")

                logger.info(
                    "One-time BankID authentication completed",
                    extra={
                        'order_ref': self.order_ref,
                        'user_name': personal_details.get("name")
                    }
                )

                await self.send(text_data=json.dumps({
                    "data": {
                        "name": personal_details.get("name"),
                        "token": token,
                    },
                    "status": "success",
                    "message": i18n_message("Data retrieved successfully", self.language),
                    "status_code": 200,
                }))

            else:
                response, status_code = await self._handle_response_status(response_data)
                logger.warning(
                    "One-time BankID status not complete",
                    extra={
                        'order_ref': self.order_ref,
                        'status': response_data.get('status'),
                        'hint_code': response_data.get('hintCode')
                    }
                )

                await self.send(text_data=json.dumps({
                    "status": "failed",
                    "message": i18n_message(response["message"], self.language),
                    "status_code": 400,
                }))

                await asyncio.sleep(5)
                await self.collect_status_websocket()

        except Exception as e:
            logger.error(
                "Error in one-time BankID status collection",
                extra={
                    'order_ref': self.order_ref,
                    'error': str(e)
                },
                exc_info=True
            )
            await self.send(text_data=json.dumps({
                "status": "failed",
                "message": i18n_message("Validation error", self.language),
                "status_code": 422,
            }))

        finally:
            await asyncio.sleep(1)
            await self.close()

    async def disconnect(self, close_code):
        logger.info(
            "One-time BankID connection disconnected",
            extra={
                'order_ref': self.order_ref,
                'close_code': close_code
            }
        )

    async def receive(self, text_data=None):
        pass

    async def _handle_completion_data(self, response_data, document_content):
        """Handles the completion data and creates a user."""
        completion_data = response_data.get("completionData")
        personal_details = completion_data.get("user")
        personal_number = personal_details.get("personalNumber")

        return await handle_user_creation(personal_number, self.jwt_manager, True)

    async def _handle_issued_date(self, str_date):
        if isinstance(str_date, str) and str_date.endswith("Z"):
            value = str_date[:-1]
            return datetime.strptime(value, "%Y-%m-%d").date()

        await self.send(
            text_data=json.dumps(
                {
                    "status": "failed",
                    "message": i18n_message(
                        "Invalid date format. Use YYYY-MM-DD.", self.language
                    ),
                    "status_code": 422,
                }
            )
        )

    async def _handle_response_status(self, response_data):
        """Handles non-complete response statuses."""
        hint_code = response_data.get("hintCode")
        status_type = response_data.get("status")

        if status_type and status_type != "complete":
            response_message = get_bankid_message(status_type, hint_code)
            status_code = (
                status.HTTP_202_ACCEPTED
                if status_type == "pending"
                else status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            if response_message:
                return (
                    {"message": response_message, "status": status_type},
                    status_code,
                )
        return None, None
