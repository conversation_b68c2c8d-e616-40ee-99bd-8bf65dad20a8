from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from documents.utils.jwt_verifier import JWTAccessTokenAuthentication
from ..services.pdf_conversion_service import PDFConversionService
import base64

class HTMLToPDFConversionView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        html_content = request.data.get('body_html',None)
      
        footer_html = request.data.get('footer_html',None)
        if not html_content:
            return Response(
                {'error': 'HTML content is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Convert HTML to PDF
            pdf_service = PDFConversionService()
            if footer_html:
                pdf_content = pdf_service.convert_html_to_pdf(html_content,footer_html)
            else:
                pdf_content = pdf_service.convert_html_to_pdf(html_content)
            
            # Encode PDF content as base64
            pdf_base64 = base64.b64encode(pdf_content).decode('utf-8')
            
            return Response({
                'pdf_content': pdf_base64,
                'content_type': 'application/pdf'
            })
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 