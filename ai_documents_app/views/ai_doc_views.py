from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from asgiref.sync import sync_to_async, async_to_sync
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from subscriptions.services.subscription_feature_service import SubscriptionFeatureManager
from django.http import StreamingHttpResponse
import json
from datetime import datetime

from ai_documents_app.services.openai_doc_generator import OpenAIDocGenerator
from documents.utils.jwt_verifier import JWTAccessTokenAuthentication

@method_decorator(csrf_exempt, name='dispatch')
class GenerateDocumentView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.doc_generator = OpenAIDocGenerator()
    
    def _validate_request(self, data, is_update=False):
        """Validate request data"""
        if is_update:
            required_fields = ['prompt', 'conversation_id']
            # Optional: conversation_id
        else:
            required_fields = [
                'prompt', 'document_country', 'document_language',
                'document_type',
            ]
        
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return False, f'Missing required fields: {", ".join(missing_fields)}'
        return True, None

    async def _generate_document(self, data):
        """Async helper to generate document"""
        request = {
            "prompt": data['prompt'],
            "document_country": data['document_country'],
            "document_language": data['document_language'],
            "document_type": data['document_type']
        }
        
        response = await sync_to_async(self.doc_generator.generate_document)(request)
        return response

    async def _enhance_document(self, data):
        """Async helper to enhance document"""
        response = await sync_to_async(self.doc_generator.enhance_document)(
            conversation_id=data.get('conversation_id'),
            enhancement=data['prompt']
        )
        return response

    def post(self, request):
        data = request.data
        
        # Validate request
        is_valid, error_message = self._validate_request(data)
        if not is_valid:
            return Response(
                {'error': error_message},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check subscription limits
        feature_manager = SubscriptionFeatureManager(request.user)
        limit_info = feature_manager.check_feature_availability("templates_token")
        if not limit_info.get("unlimited") and not limit_info.get("remaining", 0) > 0:
            return Response(
                {'error': limit_info.get("error")},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            response = async_to_sync(self._generate_document)(data)

            if isinstance(response, dict) and 'error' in response:
                return Response(
                    {'error': response['error']},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            return Response({
                'content': response['content'],
                'conversation_id': response['conversation_id'],
                'metadata': {
                    'country': data['document_country'],
                    'language': data['document_language'],
                    'type': data['document_type'],
                    'size': len(response['content']),
                    'model_used': response['model_used'],
                    'tokens_used': response['tokens_used'],
                    'timestamp': response['timestamp']
                }
            })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def put(self, request):
        """Handle document updates using conversation context"""
        data = request.data
        
        # Validate request
        is_valid, error_message = self._validate_request(data, is_update=True)
        if not is_valid:
            return Response(
                {'error': error_message},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check subscription limits
        feature_manager = SubscriptionFeatureManager(request.user)
        limit_info = feature_manager.check_feature_availability("templates_token")
        if not limit_info.get("unlimited") and not limit_info.get("remaining", 0) > 0:
            return Response(
                {'error': limit_info.get("error")},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            response = async_to_sync(self._enhance_document)(data)

            if isinstance(response, dict) and 'error' in response:
                return Response(
                    {'error': response['error']},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            return Response({
                'content': response['content'],
                'conversation_id': response['conversation_id'],
                'metadata': {
                    'size': len(response['content']),
                    'updated': True,
                    'model_used': response['model_used'],
                    'tokens_used': response['tokens_used'],
                    'timestamp': response['timestamp']
                }
            })

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )