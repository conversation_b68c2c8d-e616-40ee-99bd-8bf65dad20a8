from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from ..services.groq_service import GroqService
# If you're using authentication, uncomment these
from rest_framework.permissions import IsAuthenticated
from documents.utils.jwt_verifier import JWTAccessTokenAuthentication
from subscriptions.services.subscription_feature_service import (
    SubscriptionFeatureManager,
)

class GenerateGroqDocumentView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    def post(self, request):
        data = request.data
        required_fields = [
            'prompt', 'document_country', 'document_language',
            'document_type',
        ]
        
        # Validate required fields
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return Response(
                {'error': f'Missing required fields: {", ".join(missing_fields)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        feature_manager = SubscriptionFeatureManager(request.user)
        can_create, limit_info = feature_manager.can_create_feature("templates_token")
        if not can_create:
            return Response(
                {'error': limit_info.get("error")},
                status=status.HTTP_400_BAD_REQUEST
            )


        groq_service = GroqService(user=request.user,limit_info=limit_info.get("limit",{}))
        try:
            response = groq_service.generate_document(
                prompt=data['prompt'],
                document_country=data['document_country'],
                document_language=data['document_language'],
                document_type=data['document_type'],

            )
            
            if isinstance(response, dict) and 'error' in response:
                return Response(
                    {'error': response['error']},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
                
            return Response({
                'content': response,
                'metadata': {
                    'country': data['document_country'],
                    'language': data['document_language'],
                    'type': data['document_type'],

                }
            })
            
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
