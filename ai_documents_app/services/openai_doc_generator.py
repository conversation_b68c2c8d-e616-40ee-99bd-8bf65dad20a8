from typing import TypedDict, Optional
from openai import OpenAI
import os
import tiktoken
from datetime import datetime
import re


class DocumentRequest(TypedDict):
    prompt: str
    document_country: str
    document_language: str
    document_type: str


class DocumentResponse(TypedDict):
    conversation_id: str
    content: str
    status: str
    timestamp: str
    model_used: str
    tokens_used: Optional[int]


class OpenAIDocGenerator:
    def __init__(self):
        """Initialize the OpenAI client with API key."""
        self.client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.model = "gpt-4-turbo"
        self.encoding = tiktoken.encoding_for_model("gpt-3.5-turbo-16k")
        self.max_context_tokens = 128000  # Maximum context length for GPT-4 Turbo
        self.max_response_tokens = 2048  # Using most of the context for response
        self.reserved_tokens = 2000  # Reserve tokens for system and user prompts
        self.conversation_history = {}  # Store conversation history

    def count_tokens(self, text: str) -> int:
        """Count the number of tokens in a text string."""
        return len(self.encoding.encode(text))

    def clean_html_output(self, content: str) -> str:
        """
        Ensure response contains only clean HTML content without unwanted newlines.
        """
        # Remove any markdown code blocks
        content = re.sub(r'```html?\n|```', '', content.strip())

        # Remove all newlines and extra spaces between tags
        content = re.sub(r'\s*\n\s*', ' ', content)

        # Clean up multiple spaces
        content = re.sub(r'\s+', ' ', content)

        # Clean up spaces around HTML tags
        content = re.sub(r'\s*(<[^>]+>)\s*', r'\1', content)

        # Remove any doctype, html, head, body tags
        content = re.sub(r'<!DOCTYPE.*?>|<html[^>]*>|</html>|<head>.*?</head>|<body[^>]*>|</body>', '', content,
                         flags=re.DOTALL | re.IGNORECASE)

        # Ensure proper spacing between text content
        content = re.sub(r'>([\w\s])', r'> \1', content)
        content = re.sub(r'([\w\s])<', r'\1 <', content)

        # Remove any remaining newlines
        content = content.replace('\n', '')

        return content.strip()

    # def clean_html_output(self, content: str) -> str:
    #     """Clean the HTML output to remove any non-HTML formatting."""
    #     # Replace any newlines with <br> tags
    #     content = re.sub(r'\n+', '<br>', content)
    #     # Remove any consecutive <br> tags
    #     content = re.sub(r'(<br>){2,}', '<br>', content)
    #     # Remove any whitespace between tags
    #     content = re.sub(r'>\s+<', '><', content)
    #     # Remove any remaining newlines or carriage returns
    #     content = content.replace('\n', '').replace('\r', '')
    #     return content.strip()

    def generate_document(self, request: DocumentRequest) -> DocumentResponse:
        """
        Generate a new legal document based on the request parameters.

        Args:
            request (DocumentRequest): Document generation parameters

        Returns:
            DocumentResponse: Generated document with conversation ID
        """
        try:
            system_prompt = f"""You are an expert legal document generator. Generate an extremely detailed and comprehensive {request['document_type']} for {request['document_country']} in {request['document_language']} language as clean HTML. The document should be extensive (equivalent to 7+ pages) with thorough explanations and subclauses.

                OUTPUT RULES:
                1. Use ONLY HTML tags - no special characters or markdown 
                2. Generate extensive, detailed content for each section
                3. Include multiple detailed subclauses (1.1, 1.1.1, etc.)
                4. Add comprehensive explanations and specific examples
                5. Use proper legal terminology and formal language
                6. Maintain clean HTML structure without unnecessary whitespace
                7. Don't use any special characters or escape sequences (\n, \t, \r, etc.)

                REQUIRED CONTENT STRUCTURE:
                <body class="legal-document">
                    <header class="document-header">
                        <h1 class="document-title"></h1>
                        <div class="document-meta">
                            <p class="date"></p>
                            <p class="parties"></p>
                            <div class="preamble"></div>
                        </div>
                    </header>
                    <main class="document-main">
                        <section class="definitions">
                            <h2>1. Definitions and Interpretations</h2>
                            <div class="section-content">
                                <h3>1.1 Defined Terms</h3>
                                <div class="definitions-list"></div>
                                <h3>1.2 Interpretation Rules</h3>
                                <div class="interpretation-rules"></div>
                            </div>
                        </section>
                        <section class="scope">
                            <h2>2. Scope and Purpose</h2>
                            <div class="section-content">
                                <h3>2.1 Scope of Agreement</h3>
                                <h3>2.2 Purpose and Objectives</h3>
                                <h3>2.3 Implementation Timeline</h3>
                            </div>
                        </section>
                        <section class="rights">
                            <h2>3. Rights and Obligations</h2>
                            <div class="section-content">
                                <h3>3.1 Rights of First Party</h3>
                                <h3>3.2 Obligations of First Party</h3>
                                <h3>3.3 Rights of Second Party</h3>
                                <h3>3.4 Obligations of Second Party</h3>
                                <h3>3.5 Mutual Obligations</h3>
                            </div>
                        </section>
                        <section class="term">
                            <h2>4. Term and Termination</h2>
                            <div class="section-content">
                                <h3>4.1 Effective Date and Term</h3>
                                <h3>4.2 Renewal Terms</h3>
                                <h3>4.3 Termination Rights</h3>
                                <h3>4.4 Effects of Termination</h3>
                            </div>
                        </section>
                        <section class="governing-law">
                            <h2>5. Governing Law</h2>
                            <div class="section-content">
                                <h3>5.1 Applicable Law</h3>
                                <h3>5.2 Jurisdiction</h3>
                                <h3>5.3 Compliance Requirements</h3>
                            </div>
                        </section>
                        <section class="dispute">
                            <h2>6. Dispute Resolution</h2>
                            <div class="section-content">
                                <h3>6.1 Negotiation Process</h3>
                                <h3>6.2 Mediation</h3>
                                <h3>6.3 Arbitration</h3>
                                <h3>6.4 Court Proceedings</h3>
                            </div>
                        </section>
                        <section class="force-majeure">
                            <h2>7. Force Majeure</h2>
                            <div class="section-content">
                                <h3>7.1 Definition of Force Majeure</h3>
                                <h3>7.2 Notice Requirements</h3>
                                <h3>7.3 Effects and Obligations</h3>
                                <h3>7.4 Termination Due to Force Majeure</h3>
                            </div>
                        </section>
                        <section class="notices">
                            <h2>8. Notices</h2>
                            <div class="section-content">
                                <h3>8.1 Form of Notices</h3>
                                <h3>8.2 Delivery Methods</h3>
                                <h3>8.3 Contact Information</h3>
                                <h3>8.4 Change of Address</h3>
                            </div>
                        </section>
                        <section class="general">
                            <h2>9. General Provisions</h2>
                            <div class="section-content">
                                <h3>9.1 Entire Agreement</h3>
                                <h3>9.2 Amendments</h3>
                                <h3>9.3 Severability</h3>
                                <h3>9.4 Waiver</h3>
                                <h3>9.5 Assignment</h3>
                                <h3>9.6 Counterparts</h3>
                            </div>
                        </section>
                    </main>
                    <footer class="document-footer">
                        <div class="signatures">
                            <div class="signature-block first-party"></div>
                            <div class="signature-block second-party"></div>
                        </div>
                        <div class="document-id"></div>
                        <div class="witness-block"></div>
                    </footer>
                </body>

                CONTENT REQUIREMENTS:
                1. Fill each section and subsection with comprehensive legal content
                2. Include detailed explanations for each clause
                3. Add specific examples where appropriate
                4. Use formal legal language and terminology
                5. Ensure all sections are thoroughly detailed
                6. Include cross-references between related clauses
                7. Add specific conditions and exceptions for each major clause
                STRICT FORMAT RULES:
                1. NO special characters or escape sequences (\n, \t, \r, etc.) - use proper HTML tags instead
                2. Use <br> for line breaks, not any other special characters
                3. Use <p> tags for paragraphs
                4. Use CSS classes for spacing and formatting
                5. Join all HTML tags without newlines between them
                6. Remove ALL whitespace between tags
                 """

            # Count tokens in prompts
            system_tokens = self.count_tokens(system_prompt)
            user_tokens = self.count_tokens(request['prompt'])
            available_tokens = self.max_context_tokens - system_tokens - user_tokens

            # Ensure we don't exceed model's capacity
            max_tokens = min(available_tokens, self.max_response_tokens)

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": request['prompt']}
                ],
                temperature=0.7,
                max_tokens=max_tokens,
                top_p=1,
                frequency_penalty=0.3,
                presence_penalty=0.2
            )
            cleaned_content = self.clean_html_output(
                response.choices[0].message.content)
            return {
                "conversation_id": response.id,
                "content": cleaned_content,
                "status": "success",
                "timestamp": datetime.now().isoformat(),
                "model_used": self.model,
                "tokens_used": response.usage.total_tokens if response.usage else None
            }

        except Exception as e:
            raise Exception(f"Failed to generate document: {str(e)}")

    def enhance_document(self, conversation_id: str, enhancement: str) -> DocumentResponse:
        """
        Enhance an existing document using the conversation ID for context.

        Args:
            conversation_id (str): Previous conversation ID
            enhancement (str): Requested enhancements/changes

        Returns:
            DocumentResponse: Enhanced document with new conversation ID
        """
        try:
            # Retrieve the previous conversation using the proper endpoint
            try:
                previous_chat = self.client.chat.completions.retrieve(conversation_id)
                previous_content = previous_chat.choices[0].message.content
            except Exception as e:
                raise Exception(f"Failed to retrieve previous conversation: {str(e)}")

            if not previous_content:
                raise Exception("No content found in previous conversation")

            system_prompt = """You are continuing work on a legal document. IMPORTANT: Return ONLY the complete updated HTML document without any explanations or comments about the changes.

                CRITICAL RULES:
                1. Return ONLY the updated HTML document
                2. NO explanations about changes
                3. NO comments or descriptions
                4. NO special characters or newlines (\n, \t, etc.)
                5. Use <br> for line breaks
                6. Join all HTML tags without spaces
                7. Output as a single line of pure HTML
                8. Maintain the same HTML structure
                9. Preserve all CSS classes and IDs
                10. Keep all existing content unless specifically modified"""

            # Calculate available tokens
            system_tokens = self.count_tokens(system_prompt)
            user_tokens = self.count_tokens(enhancement)
            previous_tokens = self.count_tokens(previous_content)
            available_tokens = self.max_context_tokens - system_tokens - \
                user_tokens - previous_tokens - self.reserved_tokens

            if available_tokens <= 0:
                raise Exception("Not enough tokens available for the update")

            # Create the chat completion with the previous content as context
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "assistant", "content": previous_content},
                    {"role": "user", "content": f"Update the above document with these changes: {enhancement}. Return ONLY the complete updated HTML document."}
                ],
                temperature=0.7,
                max_tokens=available_tokens,
                top_p=1,
                frequency_penalty=0.3,
                presence_penalty=0.2
            )

            # Clean and validate the response
            if not response.choices or not response.choices[0].message.content:
                raise Exception("No valid response received from the API")

            cleaned_content = self.clean_html_output(
                response.choices[0].message.content)

            if not cleaned_content.strip():
                raise Exception("Generated content is empty after cleaning")

            return {
                "conversation_id": response.id,
                "content": cleaned_content,
                "status": "success",
                "timestamp": datetime.now().isoformat(),
                "model_used": self.model,
                "tokens_used": response.usage.total_tokens if response.usage else None
            }

        except Exception as e:
            raise Exception(f"Failed to enhance document: {str(e)}")
