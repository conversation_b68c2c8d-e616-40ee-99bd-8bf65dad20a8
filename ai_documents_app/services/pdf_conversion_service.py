import logging
import os
import html
from weasyprint import CSS, HTML
from weasyprint.text.fonts import FontConfiguration

logger = logging.getLogger(__name__)


class PDFConversionService:

    def convert_html_to_pdf(
            self,
            body_html: str,

            footer_html: str = None,
            margins: dict = None,
            output_path: str = None,
            save_file: bool = True,
            page_size: str = 'A4'  # New parameter for page size
    ) -> bytes | str:
        """
        Convert HTML content to PDF with repeating header and footer on each page.
        Args:
            body_html: Main HTML content with inline styles.
            header_html: HTML content for header with inline styles.
            footer_html: HTML content for footer with inline styles.
            margins: Dictionary with margin settings (top, right, bottom, left) in mm.
            output_path: Path where to save the PDF file. If None, returns bytes.
            save_file: If True and output_path is provided, saves file and returns path.
                      If False, returns PDF content as bytes.
            page_size: The size of the PDF document (e.g., 'A3', 'A4', 'A5', 'A6').
        Returns:
            bytes if save_file is False or output_path is None,
            otherwise absolute path of saved file (str).
        """
        try:
            # Clean and escape header and footer content

            font_config = FontConfiguration()

            # Define font sizes for different page formats
            page_formats = {
                'A3': {
                    'base_font': '16px',
                    'h1': '32px',
                    'h2': '24px',
                    'h3': '20px',
                    'margin_top': '1cm',
                    'margin_bottom': '5cm',

                },
                'A4': {
                    'base_font': '12px',
                    'h1': '24px',
                    'h2': '18px',
                    'h3': '16px',
                    'margin_top': '1cm',
                    'margin_bottom': '5cm',

                },
                'A5': {
                    'base_font': '10px',
                    'h1': '20px',
                    'h2': '16px',
                    'h3': '14px',
                    'margin_top': '2cm',
                    'margin_bottom': '5cm',

                },
                'A6': {
                    'base_font': '8px',
                    'h1': '16px',
                    'h2': '14px',
                    'h3': '12px',
                    'margin_top': '2cm',
                    'margin_bottom': '5cm',

                }
            }

            format_settings = page_formats.get(page_size.upper(), page_formats['A4'])

            # Adjust margins if no header/footer
            if not footer_html:

                format_settings['margin_bottom'] = '1cm'

            css_string = f"""
                @page {{
                    size: {page_size};
                    margin: {format_settings['margin_top']} 0 
                            {format_settings['margin_bottom']} 0;
                }}
                body {{
                    font-size: {format_settings['base_font']};
                    line-height: 1.5;
                    margin: 0;
                    padding: 0;
                }}
                #content {{
                    margin-left: 2cm;
                    margin-right: 2cm;
                }}
                h1 {{
                    font-size: {format_settings['h1']};
                }}
                h2 {{
                    font-size: {format_settings['h2']};
                }}
                h3 {{
                    font-size: {format_settings['h3']};
                }}
                """

            # Add footer if provided
            if footer_html:
                footer_css = """
                    @page {
                        @bottom-left {
                            content: element(footer);
                            margin: 0;
                            padding: 0;
                            width: 100%;
                            font-size: 8pt;
                        }
                    }
                    #footer {
                        position: running(footer);
                        margin-left: 0;
                        margin-right: 0;
                        width: 100%;
                        font-size: 6pt;
                       
                    }
                """
                css_string += footer_css

            css = CSS(
                string=css_string,
                font_config=font_config,
            )

            # Wrap the content in a container with proper margins
            full_html = f"""
                <div id="footer">{footer_html or ''}</div>
                <div id="content">{body_html}</div>
            """
            html = HTML(string=full_html)

            # Generate PDF to bytes instead of file
            pdf_content = html.write_pdf(stylesheets=[css], font_config=font_config)

            if output_path and save_file:
                with open(output_path, 'wb') as f:
                    f.write(pdf_content)
                return os.path.abspath(output_path)

            # Return PDF content as bytes if no output path specified or save_file is False
            return pdf_content
        except Exception as e:
            logger.error(f"Error converting HTML to PDF: {str(e)}")
            raise


if __name__ == "__main__":

    # Sample Usage

    data = {
        "body_html": "<div style=\"position: relative; height: 189px; width: 100%; border-bottom: 1px solid #e5e7eb;\">\n      <div style=\"position: absolute; left: 20px; top: 20px;\">\n        <img src=\"https://skrivly.com/wp-content/uploads/2024/12/skrivly-logo.png\" alt=\"Company Logo\" style=\"width: 80px; height: 80px; object-fit: contain; border-radius: 4px;\">\n      </div>\n      \n      <div style=\"position: absolute; left: 200px; top: 30px;\">\n        <h1 style=\"font-size: 24px; font-weight: bold; color: #1f2937; margin: 0;\">Skrivly</h1>\n      </div>\n      \n      <div style=\"position: absolute; left: 400px; top: 20px;\">\n        \n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\n            <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" style=\"margin-right: 4px;\">\n              <path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\"></path>\n              <polyline points=\"22,6 12,13 2,6\"></polyline>\n            </svg>\n            <span style=\"font-size: 14px; color: #4b5563;\"><EMAIL></span>\n          </div>\n        \n        \n        \n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\n            <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" style=\"margin-right: 4px;\">\n              <path d=\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"></path>\n            </svg>\n            <span style=\"font-size: 14px; color: #4b5563;\">+1 (555) 123-4567</span>\n          </div>\n        \n        \n        \n          <div style=\"display: flex; align-items: center;\">\n            <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" style=\"margin-right: 4px;\">\n              <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"></path>\n              <circle cx=\"12\" cy=\"10\" r=\"3\"></circle>\n            </svg>\n            <span style=\"font-size: 14px; color: #4b5563;\">123 Business Avenue, Tech City, TC 12345</span>\n          </div>\n        \n      </div>\n\n      \n\n      \n    </div>\n  <h1>NON-DISCLOSURE AGREEMENT (NDA) BETWEEN PARTY A AND PARTY B</h1><h2>INTRODUCTION</h2><p>This Non-Disclosure Agreement (\"Agreement\") is made and entered into on [DATE] (\"Effective Date\") by and between Party A, with its principal place of business at [ADDRESS] (\"Disclosing Party\"), and Party B, with its principal place of business at [ADDRESS] (\"Receiving Party\").</p><p>The parties agree to the terms and conditions outlined in this Agreement, which is governed by and construed in accordance with the laws of India. This Agreement is subject to the Indian Contract Act, 1872, and other applicable laws and regulations in India.</p><h2>DEFINITIONS</h2><p>For the purposes of this Agreement, the following definitions shall apply:</p><ul><li><strong>Confidential Information</strong>: means all information, whether written or oral, tangible or intangible, that is disclosed by the Disclosing Party to the Receiving Party, including but not limited to trade secrets, business strategies, technical data, and other proprietary information.</li><li><strong>Disclosing Party</strong>: means Party A, as defined above.</li><li><strong>Receiving Party</strong>: means Party B, as defined above.</li><li><strong>Authorized Representative</strong>: means an employee or agent of the Receiving Party who is authorized to receive Confidential Information on behalf of the Receiving Party.</li></ul><h2>OBLIGATIONS OF THE RECEIVING PARTY</h2><p>The Receiving Party agrees to:</p><ul><li>Hold the Confidential Information in confidence and not disclose it to any third party without the prior written consent of the Disclosing Party.</li><li>Use the Confidential Information solely for the purpose of evaluating the potential business relationship between the parties.</li><li>Not reproduce, modify, or distribute the Confidential Information without the prior written consent of the Disclosing Party.</li><li>Return all Confidential Information to the Disclosing Party upon request, including but not limited to documents, notes, and other materials.</li></ul><h2>EXCLUSIONS</h2><p>The obligations of the Receiving Party under this Agreement shall not apply to any Confidential Information that:</p><ul><li>Is or becomes publicly available through no fault of the Receiving Party.</li><li>Is already known to the Receiving Party at the time of disclosure.</li><li>Is lawfully obtained by the Receiving Party from a third party without restriction on disclosure.</li><li>Is independently developed by the Receiving Party without use of the Confidential Information.</li></ul><h2>TERM AND TERMINATION</h2><p>This Agreement shall commence on the Effective Date and shall continue for a period of [LENGTH OF TIME] from the date of disclosure of the Confidential Information.</p><p>Upon termination of this Agreement, the Receiving Party shall return all Confidential Information to the Disclosing Party and shall destroy any copies of the Confidential Information in its possession.</p><h2>GOVERNING LAW AND JURISDICTION</h2><p>This Agreement shall be governed by and construed in accordance with the laws of India.</p><p>Any disputes arising out of or in connection with this Agreement shall be resolved through arbitration in accordance with the Arbitration and Conciliation Act, 1996, as amended from time to time.</p><h2>ENTIRE AGREEMENT</h2><p>This Agreement constitutes the entire agreement between the parties with respect to the subject matter hereof and supersedes all prior or contemporaneous agreements or understandings.</p><h2>AMENDMENTS</h2><p>This Agreement may not be amended or modified except in writing signed by both parties.</p><h2>COUNTERPARTS</h2><p>This Agreement may be executed in counterparts, each of which shall be deemed an original, but all of which together shall constitute one and the same instrument.</p><h2>NOTICES</h2><p>Any notice or communication required or permitted to be given under this Agreement shall be in writing and shall be delivered personally or sent by registered mail or email to the address of the other party as specified below:</p><ul><li>Party A: [ADDRESS]</li><li>Party B: [ADDRESS]</li></ul><h2>ACKNOWLEDGMENT</h2><p>By signing below, the parties acknowledge that they have read, understand, and agree to be bound by the terms and conditions of this Agreement.</p><p>Party A (Disclosing Party):</p><p>Signature: ______________________________</p><p>Name: __________________________________</p><p>Title: __________________________________</p><p>Date: _____________________________________</p><p><br></p><p>Party B (Receiving Party):</p><p>Signature: ______________________________</p><p>Name: __________________________________</p><p>Title: __________________________________</p><p>Date: _____________________________________</p><h2>LEGAL REFERENCES</h2><p>This Agreement is governed by and construed in accordance with the laws of India, including but not limited to:</p><ul><li>Indian Contract Act, 1872</li><li>Specific Relief Act, 1963</li><li>Code of Civil Procedure, 1908</li><li>Indian Stamp Act, 1899</li><li>Registration Act, 1908</li></ul><h2>DEFINITIONS OF LEGAL TERMS</h2><p>For the purposes of this Agreement, the following legal terms shall have the following meanings:</p><ul><li><strong>Consideration</strong>: means something of value given or promised in exchange for a promise or performance.</li><li><strong>Intention to Create a Legal Relationship</strong>: means the parties' intention to create a legally binding contract.</li><li><strong>Capacity to Contract</strong>: means the parties' competence to enter into a contract.</li><li><strong>Free Consent</strong>: means the parties' free and informed consent to the agreement.</li><li><strong>Lawful Object</strong>: means the object of the agreement must be lawful.</li></ul><h2>EXAMPLES AND EXPLANATIONS</h2><p>The following examples and explanations are provided to illustrate the application of this Agreement:</p><ul><li>If the Disclosing Party discloses Confidential Information to the Receiving Party, the Receiving Party must hold the Confidential Information in confidence and not disclose it to any third party without the prior written consent of the Disclosing Party.</li><li>If the Receiving Party is required to disclose Confidential Information by law, the Receiving Party must notify the Disclosing Party in writing and provide the Disclosing Party with an opportunity to seek a protective order or other remedy.</li></ul>",
        "footer_html": "<div style=\"position: relative; height: 100px; width: 100%; border-top: 1px solid #e5e7eb;\">\n      <div style=\"position: absolute; left: 20px; top: 20px;\">\n        <p style=\"font-size: 12px; color: #4b5563; margin: 0;\">© 2024 Skrivly. All rights reserved.</p>\n      </div>\n\n      <table style=\"width: 100%; border-collapse: collapse;\">\n        \n          <tbody><tr>\n            <td style=\"position: absolute; left: 280px; top: 20px;\">\n              <span style=\"font-size: 12px; color: #4b5563; font-weight: 500;\">email:</span>\n              <span style=\"font-size: 12px; color: #4b5563; margin-left: 4px;\"><EMAIL></span>\n            </td>\n          </tr>\n        \n\n        \n          <tr>\n            <td style=\"position: absolute; left: 500px; top: 20px;\">\n              <span style=\"font-size: 12px; color: #4b5563; font-weight: 500;\">Website:</span>\n              <span style=\"font-size: 12px; color: #4b5563; margin-left: 4px;\">www.skrivly.com</span>\n            </td>\n          </tr>\n        \n      </tbody></table>\n    </div>\n  "
    }
    pdf_service = PDFConversionService()
    output_file = "output_file.pdf"
    if data.get("footer_html"):
        body_html = html.unescape(data.get("body_html").replace('\n', ''))
        footer_html = html.unescape(data.get("footer_html").replace('\n', ''))
        print(footer_html)

        pdf_service.convert_html_to_pdf(body_html=body_html, footer_html=footer_html,
                                        output_path=output_file, save_file=True)
    else:
        pdf_service.convert_html_to_pdf(
            data["body_html"], output_path=output_file, save_file=True)
    print(f"PDF has been saved to {output_file}")
