import numpy as np
from typing import List, Tuple
from sklearn.metrics.pairwise import cosine_similarity
from .openai_embedding_service import OpenAIEmbeddingService
from ..models import TemplateEmbedding
from openai import OpenAI
from django.conf import settings

import logging

logger = logging.getLogger(__name__)


class OpenAIRAGService:
    def __init__(self):
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
        self.embedding_service = OpenAIEmbeddingService()
        self.model = "gpt-4"

    def find_similar_templates(self, query: str, top_k: int = 3) -> List[Tuple[TemplateEmbedding, float]]:
        """
        Find similar templates using enhanced RAG with both vector and semantic similarity
        """
        query_embedding = self.embedding_service.generate_embedding(query)

        # Get all template embeddings
        template_embeddings = TemplateEmbedding.objects.all()

        similarities = []
        for template_embed in template_embeddings:
            # Calculate vector similarity
            vector_similarity = cosine_similarity(
                [query_embedding['vector']],
                [template_embed.embedding['vector']]
            )[0][0]

            # Calculate semantic similarity based on analysis
            semantic_similarity = self._calculate_semantic_similarity(
                query_embedding['analysis'],
                template_embed.embedding['analysis']
            )

            # Combine similarities with weighted average
            combined_similarity = (0.7 * vector_similarity) + \
                (0.3 * semantic_similarity)
            similarities.append((template_embed, combined_similarity))

        # Sort by combined similarity and get top_k results
        similarities.sort(key=lambda x: x[1], reverse=True)
        top_templates = similarities[:top_k]

        return self._process_templates_for_openai(top_templates)

    def _calculate_semantic_similarity(self, query_analysis: dict, template_analysis: dict) -> float:
        """
        Calculate semantic similarity based on analysis components
        """
        similarities = []

        # Compare topics overlap
        topic_overlap = len(
            set(query_analysis['main_topics']) &
            set(template_analysis['main_topics'])
        ) / max(len(query_analysis['main_topics']), len(template_analysis['main_topics']))
        similarities.append(topic_overlap)

        # Compare complexity
        complexity_diff = 1 - abs(
            query_analysis['complexity'] - template_analysis['complexity']
        ) / 10
        similarities.append(complexity_diff)

        # Compare tone
        tone_similarity = 1.0 if query_analysis['tone'] == template_analysis['tone'] else 0.0
        similarities.append(tone_similarity)

        # Return average similarity
        return sum(similarities) / len(similarities)

    def _process_templates_for_openai(self, templates: List[Tuple[TemplateEmbedding, float]]) -> List[Tuple[TemplateEmbedding, float]]:
        """
        Process templates with additional OpenAI analysis
        """
        try:
            for template, similarity in templates:
                prompt = f"""Analyze this template and extract key components:
                {template.content}
                """

                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": "You are a template analysis expert."},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.3,
                    max_tokens=500
                )

                template.openai_analysis = response.choices[0].message.content

        except Exception as e:
            logger.error(f"Error in OpenAI template processing: {str(e)}")

        return templates
