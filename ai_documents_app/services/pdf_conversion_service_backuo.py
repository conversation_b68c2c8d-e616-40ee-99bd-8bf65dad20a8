import logging
import os
import html
from weasyprint import CSS, HTML
from weasyprint.text.fonts import FontConfiguration

logger = logging.getLogger(__name__)


class PDFConversionService:

    def convert_html_to_pdf(
            self,
            body_html: str,
            header_html: str = None,
            footer_html: str = None,
            margins: dict = None,
            output_path: str = None,
            save_file: bool = True,
            page_size: str = 'A4'  # New parameter for page size
    ) -> bytes | str:
        """
        Convert HTML content to PDF with repeating header and footer on each page.
        Args:
            body_html: Main HTML content with inline styles.
            header_html: HTML content for header with inline styles.
            footer_html: HTML content for footer with inline styles.
            margins: Dictionary with margin settings (top, right, bottom, left) in mm.
            output_path: Path where to save the PDF file. If None, returns bytes.
            save_file: If True and output_path is provided, saves file and returns path.
                      If False, returns PDF content as bytes.
            page_size: The size of the PDF document (e.g., 'A3', 'A4', 'A5', 'A6').
        Returns:
            bytes if save_file is False or output_path is None,
            otherwise absolute path of saved file (str).
        """
        try:
            # Clean and escape header and footer content

            font_config = FontConfiguration()

            # Define font sizes for different page formats
            page_formats = {
                'A3': {
                    'base_font': '16px',
                    'h1': '32px',
                    'h2': '24px',
                    'h3': '20px',
                    'margin_top': '5cm',
                    'margin_bottom': '5cm',
                    'margin_left': '2.5cm',
                    'margin_right': '2.5cm'
                },
                'A4': {
                    'base_font': '12px',
                    'h1': '24px',
                    'h2': '18px',
                    'h3': '16px',
                    'margin_top': '5cm',
                    'margin_bottom': '5cm',
                    'margin_left': '2cm',
                    'margin_right': '2cm'
                },
                'A5': {
                    'base_font': '10px',
                    'h1': '20px',
                    'h2': '16px',
                    'h3': '14px',
                    'margin_top': '5cm',
                    'margin_bottom': '5cm',
                    'margin_left': '1.5cm',
                    'margin_right': '1.5cm'
                },
                'A6': {
                    'base_font': '8px',
                    'h1': '16px',
                    'h2': '14px',
                    'h3': '12px',
                    'margin_top': '5cm',
                    'margin_bottom': '5cm',
                    'margin_left': '1cm',
                    'margin_right': '1cm'
                }
            }

            format_settings = page_formats.get(page_size.upper(), page_formats['A4'])

            # Adjust margins if no header/footer
            if not header_html and not footer_html:
                format_settings['margin_top'] = '1cm'
                format_settings['margin_bottom'] = '1cm'

            css_string = f"""
                @page {{
                    size: {page_size};
                    margin: {format_settings['margin_top']} 
                            {format_settings['margin_right']} 
                            {format_settings['margin_bottom']} 
                            {format_settings['margin_left']};
                    @top-left {{
                        content: element(header-left);
                    }}
                    @top-center {{
                        content: element(header-center);
                    }}
                    @top-right {{
                        content: element(header-right);
                    }}
                    @bottom-left {{
                        content: element(footer-left);
                    }}
                    @bottom-center {{
                        content: element(footer-center);
                    }}
                    @bottom-right {{
                        content: element(footer-right);
                    }}
                }}
                body {{
                    font-size: {format_settings['base_font']};
                    line-height: 1.5;
                }}
                h1 {{
                    font-size: {format_settings['h1']};
                }}
                h2 {{
                    font-size: {format_settings['h2']};
                }}
                h3 {{
                    font-size: {format_settings['h3']};
                }}
                .header-left {{
                    position: running(header-left);
                    font-size: {format_settings['base_font']};
                }}
                .header-center {{
                    position: running(header-center);
                    font-size: {format_settings['base_font']};
                }}
                .header-right {{
                    position: running(header-right);
                    font-size: {format_settings['base_font']};
                }}
                .footer-left {{
                    position: running(footer-left);
                    font-size: {format_settings['base_font']};
                }}
                .footer-center {{
                    position: running(footer-center);
                    font-size: {format_settings['base_font']};
                }}
                .footer-right {{
                    position: running(footer-right);
                    font-size: {format_settings['base_font']};
                }}
              
                """

            # Add header if provided
            if header_html:
                header_css = """
                    @page {
                        @top-center {
                            content: element(header);
                            margin: 0;
                            padding: 0;
                            width: 100%;
                            font-size: 9pt;
                        }
                    }
                    #header {
                        position: running(header);
                    }
                """
                css_string += header_css

            # Add footer if provided
            if footer_html:
                footer_css = """
                    @page {
                        @bottom-center {
                            content: element(footer);
                            margin: 0;
                            padding: 0;
                            width: 100%;
                            font-size: 9pt;
                        }
                    }
                    #footer {
                        position: running(footer);
                    }
                """
                css_string += footer_css

            css = CSS(
                string=css_string,
                font_config=font_config,
            )

            # Wrap the content in a container with proper margins
            full_html = f"""
                <div class="header-left">{header_html or ''}</div>
               
                 <div class="footer-left">{footer_html or ''}</div>
               
                <div id="content">{body_html}</div>
               
            """
            html = HTML(string=full_html)

            # Generate PDF to bytes instead of file
            pdf_content = html.write_pdf(stylesheets=[css], font_config=font_config)

            if output_path and save_file:
                with open(output_path, 'wb') as f:
                    f.write(pdf_content)
                return os.path.abspath(output_path)

            # Return PDF content as bytes if no output path specified or save_file is False
            return pdf_content
        except Exception as e:
            logger.error(f"Error converting HTML to PDF: {str(e)}")
            raise


if __name__ == "__main__":

    # Sample Usage

    data = {
        "header_html": "<div style=\"position: relative; height: 189px; width: 100%; border-bottom: 1px solid #e5e7eb;\">\n      <div style=\"position: absolute; left: 20px; top: 20px;\">\n        <img src=\"https://skrivly.com/wp-content/uploads/2024/12/skrivly-logo.png\" alt=\"Company Logo\" style=\"width: 80px; height: 80px; object-fit: contain; border-radius: 4px;\">\n      </div>\n      \n      <div style=\"position: absolute; left: 192px; top: 74px;\">\n        <h1 style=\"font-size: 24px; font-weight: bold; color: #1f2937; margin: 0;\">Skrivly</h1>\n      </div>\n      \n      <div style=\"position: absolute; left: 400px; top: 47px;\">\n        \n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\n            <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" style=\"margin-right: 4px;\">\n              <path d=\"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\"></path>\n              <polyline points=\"22,6 12,13 2,6\"></polyline>\n            </svg>\n            <span style=\"font-size: 14px; color: #4b5563;\"><EMAIL></span>\n          </div>\n        \n        \n        \n          <div style=\"display: flex; align-items: center; margin-bottom: 4px;\">\n            <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" style=\"margin-right: 4px;\">\n              <path d=\"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"></path>\n            </svg>\n            <span style=\"font-size: 14px; color: #4b5563;\">+1 (555) 123-4567</span>\n          </div>\n        \n        \n        \n          <div style=\"display: flex; align-items: center;\">\n            <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" style=\"margin-right: 4px;\">\n              <path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"></path>\n              <circle cx=\"12\" cy=\"10\" r=\"3\"></circle>\n            </svg>\n            <span style=\"font-size: 14px; color: #4b5563;\">123 Business Avenue, Tech City, TC 12345</span>\n          </div>\n        \n      </div>\n\n      \n\n      \n    </div>\n  ",
        "body_html": "<h1>Rent Agreement Document</h1><h2>Between Pardeep (Landlord) and Ravinder (Tenant)</h2><p>This Rent Agreement is made on [Date] and is subject to the laws of India.</p><h2>1. Introduction</h2><p>This Rent Agreement is a legally binding contract between Pardeep (hereinafter referred to as the \"Landlord\") and Ravinder (hereinafter referred to as the \"Tenant\") for the rental of the property located at [Property Address].</p><p>The Landlord agrees to rent the property to the Tenant, and the Tenant agrees to rent the property from the Landlord, subject to the terms and conditions outlined in this Agreement.</p><h2>2. Definitions</h2><p>In this Agreement, the following terms shall have the meanings assigned to them:</p><ul><li><strong>Rent</strong>: The monthly rent payable by the Tenant to the Landlord for the use and occupation of the property.</li><li><strong>Security Deposit</strong>: The deposit payable by the Tenant to the Landlord as security for the performance of the Tenant's obligations under this Agreement.</li><li><strong>Term</strong>: The period of time during which the Tenant is entitled to occupy the property, as specified in Clause 3.</li><li><strong>Property</strong>: The premises located at [Property Address], including all fixtures, fittings, and appliances.</li></ul><h2>3. Term</h2><p>The Term of this Agreement shall commence on [Commencement Date] and shall continue for a period of [Length of Term] months, unless terminated earlier in accordance with Clause 10.</p><p>Upon expiration of the Term, this Agreement shall automatically renew for a further period of [Length of Renewal] months, unless either party gives written notice to the other of their intention to terminate this Agreement, at least [Notice Period] months prior to the expiration of the Term.</p><h2>4. Rent</h2><p>The Rent shall be [Rent Amount] per month, payable in advance on or before the [Rent Due Date] of each month.</p><p>The Rent shall be increased by [Rent Increase Percentage]% per annum, on each anniversary of the Commencement Date, unless otherwise agreed by the parties in writing.</p><h2>5. Security Deposit</h2><p>The Tenant shall pay a Security Deposit of [Security Deposit Amount] to the Landlord, which shall be refundable at the end of the Term, subject to any deductions for damages or unpaid rent.</p><p>The Security Deposit shall be held by the Landlord as security for the performance of the Tenant's obligations under this Agreement.</p><h2>6. Maintenance and Repairs</h2><p>The Landlord shall be responsible for maintaining the property in good condition and carrying out any necessary repairs, except for damage caused by the Tenant's negligence or misuse.</p><p>The Tenant shall be responsible for maintaining the property in a clean and tidy condition and for reporting any damage or defects to the Landlord promptly.</p><h2>7. Utilities</h2><p>The Tenant shall be responsible for paying all utility bills, including electricity, water, and gas, unless otherwise agreed by the parties in writing.</p><p>The Landlord shall be responsible for paying all property taxes and other outgoings in respect of the property.</p><h2>8. Notice Period</h2><p>Either party may terminate this Agreement by giving written notice to the other, at least [Notice Period] months prior to the termination date.</p><p>Upon termination, the Tenant shall vacate the property and return all keys and other property of the Landlord to the Landlord.</p><h2>9. Dispute Resolution</h2><p>Any disputes arising out of or in connection with this Agreement shall be resolved through arbitration, in accordance with the Arbitration and Conciliation Act, 1996.</p><p>The arbitration shall be conducted by a single arbitrator, appointed by mutual agreement between the parties, or in the absence of such agreement, by the [Arbitration Institution].</p><h2>10. Termination</h2><p>This Agreement may be terminated by either party, upon written notice to the other, in the event of:</p><ul><li>Breach of any term or condition of this Agreement by the other party.</li><li>Failure by the Tenant to pay rent or other charges due under this Agreement.</li><li>Damage to the property caused by the Tenant's negligence or misuse.</li></ul><h2>11. Governing Law</h2><p>This Agreement shall be governed by and construed in accordance with the laws of India.</p><p>The courts of [Jurisdiction] shall have exclusive jurisdiction to resolve any disputes arising out of or in connection with this Agreement.</p><h2>12. Registration and Stamp Duty</h2><p>This Agreement shall be registered with the relevant authorities, such as the Sub-Registrar's office, and shall be stamped with the applicable stamp duty, in accordance with the laws of India.</p><p>The parties shall cooperate with each other to ensure that this Agreement is properly registered and stamped.</p><h2>13. Notarization</h2><p>This Agreement shall be notarized by a Notary Public, to verify the identities of the parties involved.</p><p>The parties shall cooperate with each other to ensure that this Agreement is properly notarized.</p><h2>Signature of Parties</h2><p>By signing below, the parties acknowledge that they have read, understand, and agree to be bound by the terms and conditions of this Agreement.</p><p>Landlord: _________________________________</p><p>Tenant: _________________________________</p><p>Date: __________________________________</p><h2>Witness</h2><p>We, the undersigned, being two witnesses, do hereby attest and declare that we witnessed the signing of this Agreement by the parties.</p><p>Witness 1: _________________________________</p><p>Witness 2: _________________________________</p><p>Date: __________________________________</p>",
        "footer_html": "<div style=\"position: relative; height: 100px; width: 100%; border-top: 1px solid #e5e7eb;\">\n      <div style=\"position: absolute; left: 273px; top: 30px;\">\n        <p style=\"font-size: 14px; color: #4b5563; margin: 0;\">© 2024 Skrivly. All rights reserved.</p>\n      </div>\n    </div>\n  "}

    # data = {
    #    "body_html": "<h1>Rent Agreement Document</h1><h2>Between Pardeep (Landlord) and Ravinder (Tenant)</h2><p>This Rent Agreement is made on [Date] and is subject to the laws of India.</p><h2>1. Introduction</h2><p>This Rent Agreement is a legally binding contract between Pardeep (hereinafter referred to as the \"Landlord\") and Ravinder (hereinafter referred to as the \"Tenant\") for the rental of the property located at [Property Address].</p><p>The Landlord agrees to rent the property to the Tenant, and the Tenant agrees to rent the property from the Landlord, subject to the terms and conditions outlined in this Agreement.</p><h2>2. Definitions</h2><p>In this Agreement, the following terms shall have the meanings assigned to them:</p><ul><li><strong>Rent</strong>: The monthly rent payable by the Tenant to the Landlord for the use and occupation of the property.</li><li><strong>Security Deposit</strong>: The deposit payable by the Tenant to the Landlord as security for the performance of the Tenant's obligations under this Agreement.</li><li><strong>Term</strong>: The period of time during which the Tenant is entitled to occupy the property, as specified in Clause 3.</li><li><strong>Property</strong>: The premises located at [Property Address], including all fixtures, fittings, and appliances.</li></ul><h2>3. Term</h2><p>The Term of this Agreement shall commence on [Commencement Date] and shall continue for a period of [Length of Term] months, unless terminated earlier in accordance with Clause 10.</p><p>Upon expiration of the Term, this Agreement shall automatically renew for a further period of [Length of Renewal] months, unless either party gives written notice to the other of their intention to terminate this Agreement, at least [Notice Period] months prior to the expiration of the Term.</p><h2>4. Rent</h2><p>The Rent shall be [Rent Amount] per month, payable in advance on or before the [Rent Due Date] of each month.</p><p>The Rent shall be increased by [Rent Increase Percentage]% per annum, on each anniversary of the Commencement Date, unless otherwise agreed by the parties in writing.</p><h2>5. Security Deposit</h2><p>The Tenant shall pay a Security Deposit of [Security Deposit Amount] to the Landlord, which shall be refundable at the end of the Term, subject to any deductions for damages or unpaid rent.</p><p>The Security Deposit shall be held by the Landlord as security for the performance of the Tenant's obligations under this Agreement.</p><h2>6. Maintenance and Repairs</h2><p>The Landlord shall be responsible for maintaining the property in good condition and carrying out any necessary repairs, except for damage caused by the Tenant's negligence or misuse.</p><p>The Tenant shall be responsible for maintaining the property in a clean and tidy condition and for reporting any damage or defects to the Landlord promptly.</p><h2>7. Utilities</h2><p>The Tenant shall be responsible for paying all utility bills, including electricity, water, and gas, unless otherwise agreed by the parties in writing.</p><p>The Landlord shall be responsible for paying all property taxes and other outgoings in respect of the property.</p><h2>8. Notice Period</h2><p>Either party may terminate this Agreement by giving written notice to the other, at least [Notice Period] months prior to the termination date.</p><p>Upon termination, the Tenant shall vacate the property and return all keys and other property of the Landlord to the Landlord.</p><h2>9. Dispute Resolution</h2><p>Any disputes arising out of or in connection with this Agreement shall be resolved through arbitration, in accordance with the Arbitration and Conciliation Act, 1996.</p><p>The arbitration shall be conducted by a single arbitrator, appointed by mutual agreement between the parties, or in the absence of such agreement, by the [Arbitration Institution].</p><h2>10. Termination</h2><p>This Agreement may be terminated by either party, upon written notice to the other, in the event of:</p><ul><li>Breach of any term or condition of this Agreement by the other party.</li><li>Failure by the Tenant to pay rent or other charges due under this Agreement.</li><li>Damage to the property caused by the Tenant's negligence or misuse.</li></ul><h2>11. Governing Law</h2><p>This Agreement shall be governed by and construed in accordance with the laws of India.</p><p>The courts of [Jurisdiction] shall have exclusive jurisdiction to resolve any disputes arising out of or in connection with this Agreement.</p><h2>12. Registration and Stamp Duty</h2><p>This Agreement shall be registered with the relevant authorities, such as the Sub-Registrar's office, and shall be stamped with the applicable stamp duty, in accordance with the laws of India.</p><p>The parties shall cooperate with each other to ensure that this Agreement is properly registered and stamped.</p><h2>13. Notarization</h2><p>This Agreement shall be notarized by a Notary Public, to verify the identities of the parties involved.</p><p>The parties shall cooperate with each other to ensure that this Agreement is properly notarized.</p><h2>Signature of Parties</h2><p>By signing below, the parties acknowledge that they have read, understand, and agree to be bound by the terms and conditions of this Agreement.</p><p>Landlord: _________________________________</p><p>Tenant: _________________________________</p><p>Date: __________________________________</p><h2>Witness</h2><p>We, the undersigned, being two witnesses, do hereby attest and declare that we witnessed the signing of this Agreement by the parties.</p><p>Witness 1: _________________________________</p><p>Witness 2: _________________________________</p><p>Date: __________________________________</p>"
    # }
    pdf_service = PDFConversionService()
    output_file = "output_file.pdf"
    if data.get("header_html") and data.get("footer_html"):
        header_html = html.unescape(data.get("header_html").replace('\n', ''))
        footer_html = html.unescape(data.get("footer_html").replace('\n', ''))

        print(header_html)
        pdf_service.convert_html_to_pdf(data["body_html"], header_html, footer_html,
                                        output_path=output_file, save_file=True)
    else:
        pdf_service.convert_html_to_pdf(
            data["body_html"], output_path=output_file, save_file=True)
    print(f"PDF has been saved to {output_file}")
