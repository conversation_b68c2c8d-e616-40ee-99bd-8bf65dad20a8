from openai import OpenAI
from django.conf import settings
from typing import List
from ..models import TemplateEmbedding
from superadmin_dashboard.models import DocumentTemplate

import logging
import json

logger = logging.getLogger(__name__)


class OpenAIEmbeddingService:
    def __init__(self):
        self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
        self.model = "text-embedding-ada-002"

    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embeddings using OpenAI's embedding model with enhanced analysis.
        """
        try:
            # First, get semantic analysis using GPT-4
            analysis_prompt = """Analyze the following text and create a structured representation 
            with key features. Return the result as a JSON object with the following fields:
            - main_topics: list of main topics (max 5)
            - key_terms: list of important terms (max 10)
            - tone: formal/informal/technical
            - complexity: score from 1-10
            - length: short/medium/long
            - document_type: type of document
            
            Text to analyze:
            {text}
            
            Return ONLY the JSON object, no additional text."""

            analysis_response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a text analysis expert. Always return valid JSON."},
                    {"role": "user", "content": analysis_prompt.format(
                        text=text[:1000])}
                ],
                temperature=0.1
            )

            analysis = json.loads(analysis_response.choices[0].message.content.strip())

            # Then, get vector embedding
            embedding_response = self.client.embeddings.create(
                model=self.model,
                input=text
            )

            vector_embedding = embedding_response.data[0].embedding

            # Combine semantic analysis with vector embedding for enhanced representation
            return {
                'vector': vector_embedding,
                'analysis': analysis
            }

        except Exception as e:
            logger.error(f"Error generating OpenAI embedding: {str(e)}")
            raise

    def create_template_embedding(self, template: DocumentTemplate):
        """
        Create and store an embedding for a document template
        """
        content = template.content
        embedding_data = self.generate_embedding(content)

        return TemplateEmbedding.objects.create(
            template=template,
            content=content,
            embedding=embedding_data
        )

    def batch_create_embeddings(self, templates: List[DocumentTemplate]):
        """
        Create embeddings for multiple templates in batch
        """
        results = []
        for template in templates:
            try:
                embedding = self.create_template_embedding(template)
                results.append(embedding)
            except Exception as e:
                logger.error(
                    f"Error creating embedding for template {template.id}: {str(e)}")
                continue
        return results
