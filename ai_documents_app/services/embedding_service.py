from openai import OpenAI
from django.conf import settings

from esign.settings import OPENAI_API_KEY
from ..models import TemplateEmbedding
from superadmin_dashboard.models import DocumentTemplate


class EmbeddingService:
    def __init__(self):
        self.client = OpenAI(api_key=OPENAI_API_KEY)

    def generate_embedding(self, text):
        response = self.client.embeddings.create(
            model="text-embedding-ada-002",
            input=text
        )
        return response.data[0].embedding

    def create_template_embedding(self, template: DocumentTemplate):
        content = template.content
        embedding = self.generate_embedding(content)

        return TemplateEmbedding.objects.create(
            template=template,
            content=content,
            embedding=embedding
        )
