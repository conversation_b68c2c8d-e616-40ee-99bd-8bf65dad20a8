import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from .embedding_service import EmbeddingService
from ..models import TemplateEmbedding


class RAGService:
    def __init__(self):
        self.embedding_service = EmbeddingService()

    def find_similar_templates(self, query, top_k=3):
        query_embedding = self.embedding_service.generate_embedding(query)

        # Get all template embeddings
        template_embeddings = TemplateEmbedding.objects.all()

        similarities = []
        for template_embed in template_embeddings:
            similarity = cosine_similarity(
                [query_embedding],
                [template_embed.embedding]
            )[0][0]
            similarities.append((template_embed, similarity))

        # Sort by similarity and get top_k results
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
