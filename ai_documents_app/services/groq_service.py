import logging
import re
from typing import List

from django.conf import settings
from groq import Groq
from subscriptions.services.subscription_feature_service import (
    SubscriptionFeatureManager,
)

from .groq_rag_service import GroqRAGService

logger = logging.getLogger(__name__)


class GroqService:
    def __init__(self, user, limit_info):
        self.client = Groq(api_key=settings.GROQ_API_KEY)
        self.rag_service = GroqRAGService()
        self.user = user
        self.model = "llama-3.3-70b-versatile"
        self.limit_info = limit_info
        self.last_generated_content = None  # Store the last generated document
        self.conversation_history = []  # Store conversation history

    def generate_document(
        self,
        prompt,
        document_country,
        document_language,
        document_type,

    ):
        try:
            # Construct a more detailed prompt that includes all parameters
            enhanced_prompt = f"""Create a {document_type} document for {document_country} in {document_language}.<br>
              
                Please include all relevant legal terms and requirements for {document_country}.<br>
                Document details:<br>
                {prompt}<br>
                IMPORTANT RULES:<br>
                1. Response MUST be in proper HTML format<br>
                2. Use appropriate HTML tags for document structure (h1, h2, p, ul, li, etc.)<br>
                3. Each section should be properly nested in div elements with appropriate classes<br>
                4. Include proper spacing and indentation in the HTML<br>
                5. Do not include <!DOCTYPE>, <html>, <head>, or <body> tags - only the content<br>
                6. Use semantic HTML elements where appropriate (section, article, header, etc.)<br>
                7. Provide extremely detailed and comprehensive content<br>
                8. Include all possible relevant sections and subsections<br>
                9. Add extensive explanations and examples where appropriate<br>
                10. Include relevant legal references and citations<br>
                11. Add detailed definitions for technical or legal terms<br>"""

            # Get relevant legal context using RAG service
            legal_context = self.rag_service.get_legal_context(
                document_country, document_type
            )
            if legal_context:
                enhanced_prompt += f"<br><br>Legal Context:<br>{legal_context}"

            # Make the API call to Groq

            chat_completion = self.client.chat.completions.create(
                messages=[
                    {
                        "role": "system",
                        "content": f"""You are a legal document expert specializing in {document_country} law and {document_type} documents. Create an extremely detailed and comprehensive document. Include all possible relevant sections, extensive explanations, examples, legal references, and detailed definitions. Use maximum allowed length to provide the most thorough and valuable content possible. Format your response in clean, semantic HTML without DOCTYPE, html, head, or body tags. Use appropriate HTML elements (h1, h2, p, ul, li, div, section, etc.) for document structure.""",
                    },
                    {"role": "user", "content": enhanced_prompt},
                ],
                model=self.model,
                temperature=0.7,
                max_tokens=self.limit_info
                if self.limit_info and type(self.limit_info) == int
                else 32000,  # Increased to maximum allowed tokens
                top_p=0.9,
            )
            token_usage = chat_completion.usage.total_tokens
            feature_manager = SubscriptionFeatureManager(self.user)
            success, result = feature_manager.update_feature_count(
                feature_name="templates_token",
                count=token_usage,  # number of tokens to use
                increment=True,
            )
            if not success:
                logger.error(f"Error updating token usage: {result}")
                print(f"Error updating token usage: {result}")
            # Extract the generated content and remove any newlines
            generated_content = chat_completion.choices[0].message.content.replace(
                "\n", ""
            )

            # Format as proper HTML document without newlines
            html_content = f"""<body>{generated_content}</body>"""

            # Store the generated content
            self.last_generated_content = html_content

            # Add to conversation history
            self.conversation_history.append(
                {"role": "assistant", "content": html_content}
            )

            return html_content

        except Exception as e:
            logger.error(f"Error generating document: {str(e)}")
            raise Exception(f"Failed to generate document: {str(e)}")

    def get_last_generated_content(self):
        return self.last_generated_content

    def get_conversation_history(self):
        return self.conversation_history
