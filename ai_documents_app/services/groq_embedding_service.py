from groq import <PERSON><PERSON><PERSON>
from django.conf import settings
from typing import List
from ..models import TemplateEmbedding
from superadmin_dashboard.models import DocumentTemplate

import logging
import json

logger = logging.getLogger(__name__)


class GroqEmbeddingService:
    def __init__(self):
        self.client = Groq(api_key=settings.GROQ_API_KEY)
        self.model = "llama-3.3-70b-versatile"

    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate a semantic representation of the text using Groq's LLM.
        """
        try:
            prompt = """Analyze the following text and create a structured representation.
            Return ONLY a valid JSON object in the following format:
            {
                "main_topics": ["topic1", "topic2", "topic3"],
                "key_terms": ["term1", "term2", "term3"],
                "tone": "formal",
                "complexity": 5,
                "length": "medium",
                "document_type": "contract"
            }
            
            Text to analyze:
            {text}
            
            Remember: Return ONLY the JSON object with no additional text or formatting."""

            response = self.client.chat.completions.create(
                messages=[
                    {"role": "system", "content": "You are a text analysis expert. Always return valid JSON."},
                    {"role": "user", "content": prompt.format(
                        text=text[:1000])}  # Limit text length
                ],
                model=self.model,
                temperature=0.1,  # Lower temperature for more consistent output
                max_tokens=1000
            )

            response_content = response.choices[0].message.content.strip()

            # Clean the response to ensure it only contains the JSON object
            response_content = response_content.replace(
                '```json', '').replace('```', '').strip()

            try:
                analysis = json.loads(response_content)
            except json.JSONDecodeError as je:
                logger.error(
                    f"JSON parsing error: {je}. Response content: {response_content}")
                # Fallback to default structure if JSON parsing fails
                analysis = {
                    "main_topics": ["general"],
                    "key_terms": ["document"],
                    "tone": "formal",
                    "complexity": 5,
                    "length": "medium",
                    "document_type": "general"
                }

            numerical_embedding = self._convert_analysis_to_numerical(analysis)
            return numerical_embedding

        except Exception as e:
            logger.error(f"Error generating Groq embedding: {str(e)}")
            # Return a default embedding in case of error
            return [0.0] * 100  # Return zero vector of length 100

    def _convert_analysis_to_numerical(self, analysis: dict) -> List[float]:
        """
        Convert the text analysis into a numerical representation that can be used
        for similarity calculations.
        """
        numerical_embedding = []

        # Convert topics to one-hot encoding using a predefined set of common topics
        common_topics = set(['legal', 'financial', 'technical',
                            'administrative', 'general'])
        topic_encoding = [1.0 if topic.lower() in common_topics else 0.0
                          for topic in analysis.get('main_topics', [])]
        numerical_embedding.extend(topic_encoding)

        # Convert complexity score (1-10) to float
        complexity = float(analysis.get('complexity', 5)) / 10.0
        numerical_embedding.append(complexity)

        # Convert tone to numerical values
        tone_mapping = {'formal': 1.0, 'technical': 0.8, 'informal': 0.2}
        tone_value = tone_mapping.get(analysis.get('tone', 'formal'), 0.5)
        numerical_embedding.append(tone_value)

        # Convert length to numerical values
        length_mapping = {'short': 0.2, 'medium': 0.5, 'long': 1.0}
        length_value = length_mapping.get(analysis.get('length', 'medium'), 0.5)
        numerical_embedding.append(length_value)

        # Pad the embedding to ensure consistent length
        padding_length = 100  # Choose an appropriate length
        while len(numerical_embedding) < padding_length:
            numerical_embedding.append(0.0)

        return numerical_embedding[:padding_length]

    def create_template_embedding(self, template: DocumentTemplate):
        """
        Create and store an embedding for a document template
        """
        content = template.content
        embedding = self.generate_embedding(content)

        return TemplateEmbedding.objects.create(
            template=template,
            content=content,
            embedding=embedding
        )

    def batch_create_embeddings(self, templates: List[DocumentTemplate]):
        """
        Create embeddings for multiple templates in batch
        """
        results = []
        for template in templates:
            try:
                embedding = self.create_template_embedding(template)
                results.append(embedding)
            except Exception as e:
                logger.error(
                    f"Error creating embedding for template {template.id}: {str(e)}")
                continue
        return results
