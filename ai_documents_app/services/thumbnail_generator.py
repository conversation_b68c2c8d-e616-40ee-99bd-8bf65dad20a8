import logging
import io
import tempfile
from urllib.parse import urlparse, unquote
from typing import Optional

import boto3
from pdf2image import convert_from_path
from PIL import Image
from django.conf import settings
import base64
from django.apps import apps

logger = logging.getLogger('app')


class ThumbnailGenerator:
    def __init__(self, thumbnail_size: tuple = (200, 200)):
        self.thumbnail_size = thumbnail_size
        # Initialize S3 client with proper credentials
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_S3_REGION_NAME
        )

    def extract_s3_key(self, s3_url) -> str:
        """
        Extract the S3 key from direct S3 URL or FieldFile
        Example URL: https://esign-docs.s3.amazonaws.com/8522be87-1a51-470c-b4a1-0ac9a981fd89.pdf
        """
        logger.info(f"Extracting S3 key from URL: {s3_url}")

        # Handle Django FieldFile objects
        if hasattr(s3_url, 'name'):
            return s3_url.name

        # Handle string URLs
        if isinstance(s3_url, str):
            # If it's a full URL, extract just the key part
            if 'https://' in s3_url:
                # Split on the bucket name and get the last part
                parts = s3_url.split('esign-docs.s3.amazonaws.com/')
                if len(parts) > 1:
                    return parts[1]

            # If it's already just a key (no https://)
            if not s3_url.startswith('http'):
                return s3_url.lstrip('/')

        raise ValueError(f"Unsupported URL type: {type(s3_url)}")

    def generate_pdf_thumbnail(self, pdf_path: str, document_id: str) -> Optional[bytes]:
        """
        Convert the first page of a PDF to a thumbnail image and return the image buffer.

        Args:
            pdf_path (str): Path to the PDF file
            document_id (str): Unique identifier for the document

        Returns:
            bytes: Image buffer containing the thumbnail, or None if conversion fails
        """
        try:
            logger.info(
                "Starting thumbnail generation from S3",
                extra={
                    'document_id': document_id,
                    'pdf_path': str(pdf_path)
                }
            )

            # Get the UserDocument model
            UserDocument = apps.get_model('documents', 'UserDocument')
            document = UserDocument.objects.select_related('user').get(id=document_id)

            # Get encryption key components
            str_date_joined = document.owner.date_joined.isoformat()
            str_created_obj = document.created_at.isoformat()
            final_str_datetime = f"{str_date_joined}/{str_created_obj}"

            # Extract S3 key
            s3_key = self.extract_s3_key(pdf_path)
            logger.info(
                "Extracted S3 key for thumbnail generation",
                extra={
                    'document_id': document_id,
                    'original_path': str(pdf_path),
                    's3_key': s3_key
                }
            )

            # Create a temporary file to store the downloaded PDF
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=True) as temp_pdf:
                try:
                    # Download and decrypt the PDF from S3
                    from documents.utils import download_and_decrypt_from_s3
                    decrypted_data = download_and_decrypt_from_s3(
                        s3_key, final_str_datetime)

                    # Write decrypted data to temporary file
                    temp_pdf.write(decrypted_data)
                    temp_pdf.flush()

                    # Convert first page of PDF to image
                    images = convert_from_path(temp_pdf.name, first_page=1, last_page=1)
                    if not images:
                        logger.warning(
                            "No pages found in PDF",
                            extra={
                                'document_id': document_id,
                                'pdf_path': str(pdf_path)
                            }
                        )
                        return None

                    # Get first page and create thumbnail
                    first_page = images[0]
                    first_page.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)

                    # Save thumbnail to buffer
                    buffer = io.BytesIO()
                    first_page.save(buffer, format="JPEG", quality=85)
                    image_buffer = buffer.getvalue()

                    logger.info(
                        "Thumbnail generated successfully",
                        extra={
                            'document_id': document_id
                        }
                    )

                    return image_buffer

                except Exception as e:
                    logger.error(
                        "Error downloading or processing file from S3",
                        extra={
                            'document_id': document_id,
                            'error': str(e),
                            'bucket': settings.AWS_STORAGE_BUCKET_NAME,
                            's3_key': s3_key
                        }
                    )
                    raise

        except Exception as e:
            logger.error(
                "Error generating thumbnail",
                extra={
                    'document_id': document_id,
                    'error': str(e)
                },
                exc_info=True
            )
            return None

    def delete_thumbnail(self, thumbnail_url: str) -> bool:
        """
        Delete a thumbnail from S3.

        Args:
            thumbnail_url (str): The S3 URL of the thumbnail

        Returns:
            bool: True if deletion was successful, False otherwise
        """
        try:
            # Extract the S3 key from the URL
            s3_key = self.extract_s3_key(thumbnail_url)

            # Delete the object from S3
            self.s3_client.delete_object(
                Bucket=settings.AWS_STORAGE_BUCKET_NAME,
                Key=s3_key
            )

            logger.info(
                "Thumbnail deleted successfully",
                extra={
                    'thumbnail_url': thumbnail_url,
                    's3_key': s3_key
                }
            )
            return True

        except Exception as e:
            logger.error(
                "Error deleting thumbnail",
                extra={
                    'thumbnail_url': thumbnail_url,
                    'error': str(e)
                },
                exc_info=True
            )
            return False


if __name__ == "__main__":
    thumbnail_generator = ThumbnailGenerator()
    pdf_path = "/home/<USER>/Desktop/Projects/Jasvir-E-SignProject/esign/ai_documents_app/services/output_file.pdf"
    document_id = "example_document_id"
    thumbnail_path = thumbnail_generator.generate_pdf_thumbnail(pdf_path, document_id)
    print(f"Thumbnail generated: {thumbnail_path}")
