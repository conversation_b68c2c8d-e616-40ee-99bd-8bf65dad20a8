from typing import List, Dict
from openai import AsyncOpenAI
import logging
import re
from uuid import uuid4

logger = logging.getLogger(__name__)


class OpenAIService:
    def __init__(self, api_key, user=None):
        self.llm = AsyncOpenAI(api_key=api_key)
        # Use GPT-3.5-turbo-16k for maximum content generation
        self.model = "gpt-3.5-turbo-16k"
        self.user = user
        self.last_generated_content = None
        self.conversations = {}
        # Maximum tokens for GPT-3.5-turbo-16k
        self.max_context_length = 16384  # Total context window
        self.max_response_tokens = 15500  # Almost maximum response tokens
        # Set content length limits
        self.max_content_length = 150000  # Maximum characters in response

    def _get_or_create_conversation(self, conversation_id=None):
        """
        Get existing conversation or create a new one
        """
        if conversation_id and conversation_id in self.conversations:
            return conversation_id, self.conversations[conversation_id]

        new_conversation_id = str(uuid4())
        self.conversations[new_conversation_id] = []
        return new_conversation_id, self.conversations[new_conversation_id]

    def _sanitize_html_response(self, content: str) -> str:
        """
        Ensure response contains only clean HTML content without unwanted newlines.
        """
        # Remove any markdown code blocks
        content = re.sub(r'```html?\n|```', '', content.strip())

        # Remove all newlines and extra spaces between tags
        content = re.sub(r'\s*\n\s*', ' ', content)

        # Clean up multiple spaces
        content = re.sub(r'\s+', ' ', content)

        # Clean up spaces around HTML tags
        content = re.sub(r'\s*(<[^>]+>)\s*', r'\1', content)

        # Remove any doctype, html, head, body tags
        content = re.sub(
            r'<!DOCTYPE.*?>|<html[^>]*>|</html>|<head>.*?</head>|<body[^>]*>|</body>', '', content, flags=re.DOTALL | re.IGNORECASE)

        # Ensure proper spacing between text content
        content = re.sub(r'>([\w\s])', r'> \1', content)
        content = re.sub(r'([\w\s])<', r'\1 <', content)

        # Remove any remaining newlines
        content = content.replace('\n', '')

        return content.strip()

    def _truncate_content(self, content: str, max_length: int = None) -> str:
        """
        Truncate content to specified length while preserving HTML structure
        """
        if not max_length:
            max_length = self.max_content_length

        if len(content) <= max_length:
            return content

        # Find the last complete HTML tag before max_length
        truncated = content[:max_length]
        last_close_tag = truncated.rfind('>')
        last_open_tag = truncated.rfind('<')

        if last_open_tag > last_close_tag:
            # We're in the middle of a tag, go back to the last complete tag
            truncated = truncated[:last_open_tag]

        # Add a note about truncation
        truncation_note = "<div class='truncation-notice'><em>Note: Content has been truncated due to length.</em></div>"
        return truncated + truncation_note

    async def generate_document(self, prompt: str, document_country=None,
                                document_language=None, document_type=None) -> Dict:
        try:
            enhanced_prompt = f"""Generate a detailed {document_type} document for {document_country} in {document_language}.
                Document requirements: {prompt}
                
                DOCUMENT STRUCTURE AND FORMAT:
                The document must be structured in the following sections, each wrapped in proper HTML:

                <section class="preamble">
                    [Agreement introduction, date, parties involved]
                </section>

                <section class="definitions">
                    <h2>1. DEFINITIONS AND INTERPRETATION</h2>
                    [All defined terms with detailed explanations]
                </section>

                <section class="main-terms">
                    <h2>2. MAIN TERMS AND CONDITIONS</h2>
                    [Core agreement terms]
                </section>

                <section class="obligations">
                    <h2>3. RIGHTS AND OBLIGATIONS</h2>
                    [Detailed rights and responsibilities]
                </section>

                <section class="compliance">
                    <h2>4. COMPLIANCE AND REGULATIONS</h2>
                    [Regulatory requirements and compliance measures]
                </section>

                <section class="liability">
                    <h2>5. LIABILITY AND INDEMNIFICATION</h2>
                    [Liability clauses and indemnification terms]
                </section>

                <section class="termination">
                    <h2>6. TERMINATION</h2>
                    [Termination conditions and procedures]
                </section>

                <section class="dispute-resolution">
                    <h2>7. DISPUTE RESOLUTION</h2>
                    [Dispute handling procedures]
                </section>

                <section class="governing-law">
                    <h2>8. GOVERNING LAW</h2>
                    [Applicable laws and jurisdiction]
                </section>

                <section class="execution">
                    <h2>9. EXECUTION</h2>
                    [Signature and execution details]
                </section>"""

            response = await self.llm.chat.completions.create(
                messages=[
                    {
                        "role": "system",
                        "content": f"""You are a legal document expert specializing in {document_country} law and {document_type} documents.
                        Create a properly structured document following the exact HTML format provided.
                        Each section must be wrapped in semantic HTML tags with appropriate classes.
                        Use maximum detail while maintaining proper document structure."""
                    },
                    {"role": "user", "content": enhanced_prompt},
                ],
                model=self.model,
                temperature=0.7,
                max_tokens=self.max_response_tokens,
                top_p=1,
                presence_penalty=0.1,
                frequency_penalty=0.1,
                stream=False
            )

            # Get complete content and clean it
            generated_content = response.choices[0].message.content
            cleaned_content = self._sanitize_html_response(generated_content)

            # Add document wrapper
            final_content = f"""<div class="legal-document">{cleaned_content}</div>"""

            self.last_generated_content = final_content

            # Store conversation
            conversation_id, conversation = self._get_or_create_conversation()
            conversation.append({"role": "user", "content": enhanced_prompt})
            conversation.append({"role": "assistant", "content": final_content})

            return {
                "content": final_content,
                "conversation_id": conversation_id
            }

        except Exception as e:
            logger.error(f"Error generating document: {str(e)}")
            raise Exception(f"Failed to generate document: {str(e)}")

    async def update_document(self, prompt: str, conversation_id: str = None) -> Dict:
        try:
            # Get existing conversation or create new one
            conversation_id, conversation_history = self._get_or_create_conversation(
                conversation_id)

            # Create a focused update prompt
            update_prompt = f"""
            Update the following specific changes in the document:
            {prompt}
            
            Only show the modified parts. Keep everything else exactly the same.
            """

            # Add update request to conversation
            conversation_history.append({"role": "user", "content": update_prompt})

            response = await self.llm.chat.completions.create(
                model=self.model,
                temperature=0.0,
                messages=conversation_history,
                max_tokens=self.max_response_tokens,
            )

            updated_content = self._sanitize_html_response(
                response.choices[0].message.content)
            self.last_generated_content = updated_content

            # Store the response in conversation
            conversation_history.append(
                {"role": "assistant", "content": updated_content})

            return {
                "content": updated_content,
                "conversation_id": conversation_id
            }

        except Exception as e:
            logger.error(f"Document update error: {str(e)}")
            raise Exception(f"Failed to update document: {str(e)}")

    def get_conversation_history(self, conversation_id: str) -> List[Dict]:
        """
        Get the conversation history for a specific conversation ID
        """
        return self.conversations.get(conversation_id, [])

    def clear_conversation(self, conversation_id: str) -> bool:
        """
        Clear a specific conversation history
        """
        if conversation_id in self.conversations:
            del self.conversations[conversation_id]
            return True
        return False
