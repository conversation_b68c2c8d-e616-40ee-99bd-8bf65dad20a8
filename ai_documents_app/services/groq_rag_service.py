import numpy as np
from typing import List, Tuple
from sklearn.metrics.pairwise import cosine_similarity
from .groq_embedding_service import GroqEmbeddingService
from ..models import TemplateEmbedding
from groq import Groq
from django.conf import settings

import logging

logger = logging.getLogger(__name__)


class GroqRAGService:
    def __init__(self):
        self.client = Groq(api_key=settings.GROQ_API_KEY)
        self.embedding_service = GroqEmbeddingService()  # Use Groq-specific embedding service
        self.model = "llama-3.3-70b-versatile"

    def find_similar_templates(self, query: str, top_k: int = 3) -> List[Tuple[TemplateEmbedding, float]]:
        """
        Find similar templates using RAG with Groq-specific processing
        """
        # Get query embedding (still using OpenAI as Groq doesn't provide embeddings)
        query_embedding = self.embedding_service.generate_embedding(query)

        # Get all template embeddings
        template_embeddings = TemplateEmbedding.objects.all()

        similarities = []
        for template_embed in template_embeddings:
            similarity = cosine_similarity(
                [query_embedding],
                [template_embed.embedding]
            )[0][0]
            similarities.append((template_embed, similarity))

        # Sort by similarity and get top_k results
        similarities.sort(key=lambda x: x[1], reverse=True)
        top_templates = similarities[:top_k]

        # Enhanced context processing for Groq
        return self._process_templates_for_groq(top_templates)

    def _process_templates_for_groq(self, templates: List[Tuple[TemplateEmbedding, float]]) -> List[Tuple[TemplateEmbedding, float]]:
        """
        Process templates specifically for Groq's context handling
        """
        try:
            # Enhance templates with Groq-specific analysis
            for template, similarity in templates:
                # You can add Groq-specific template processing here
                prompt = f"""Analyze this template and extract key components:
                {template.content}
                """

                response = self.client.chat.completions.create(
                    messages=[
                        {"role": "system", "content": "You are a template analysis expert."},
                        {"role": "user", "content": prompt}
                    ],
                    model=self.model,
                    temperature=0.3,
                    max_tokens=500
                )

                # Store the analysis in the template object for later use
                template.groq_analysis = response.choices[0].message.content

        except Exception as e:
            logger.error(f"Error in Groq template processing: {str(e)}")
            # Fall back to original templates if processing fails

        return templates

    def generate_enhanced_prompt(self, user_prompt: str, similar_templates: List[Tuple[TemplateEmbedding, float]]) -> str:
        """
        Generate an enhanced prompt using the similar templates and their Groq analysis
        """
        template_contexts = []

        for template, similarity in similar_templates:
            analysis = getattr(template, 'groq_analysis', None)
            context = f"""
            Template Content:
            {template.content}
            
            Template Analysis:
            {analysis if analysis else 'No analysis available'}
            
            Similarity Score: {similarity:.2f}
            """
            template_contexts.append(context)

        enhanced_prompt = f"""
        User Request: {user_prompt}
        
        Reference Templates:
        {'-' * 50}
        {''.join(template_contexts)}
        {'-' * 50}
        
        Based on these reference templates and their analysis, generate a new document that:
        1. Maintains the professional style and structure of the reference templates
        2. Incorporates the specific requirements from the user's request
        3. Ensures consistency in formatting and tone
        """

        return enhanced_prompt

    def get_legal_context(self, document_type: str, jurisdiction: str = None) -> str:
        """
        Retrieve legal context and requirements for a specific document type

        Args:
            document_type (str): The type of document being generated
            jurisdiction (str, optional): The legal jurisdiction for the document

        Returns:
            str: Legal context and requirements for the document type
        """
        try:
            prompt = f"Provide the key legal requirements and context for {document_type} documents"
            if jurisdiction:
                prompt += f" in {jurisdiction} jurisdiction"

            response = self.client.chat.completions.create(
                messages=[
                    {"role": "system", "content": "You are a legal document expert."},
                    {"role": "user", "content": prompt}
                ],
                model=self.model,
                temperature=0.3,
                max_tokens=500
            )

            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error getting legal context: {str(e)}")
            return "Unable to retrieve legal context at this time."
