from django.db import models
from documents.models import UserDocument
from superadmin_dashboard.models import DocumentTemplate
from django.contrib.postgres.search import SearchVectorField


class DocumentEmbedding(models.Model):
    document = models.ForeignKey(UserDocument, on_delete=models.CASCADE)
    content = models.TextField(default="")  # Store the actual document content

    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)

    embedding = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['created_at']),
        ]


class TemplateEmbedding(models.Model):
    template = models.ForeignKey(DocumentTemplate, on_delete=models.CASCADE)
    content = models.TextField()
    embedding = models.JSONField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['created_at']),
            models.Index(fields=['template']),
        ]


class Document(models.Model):
    content = models.TextField()
    content_vector = SearchVectorField(null=True)

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.update_vectors()

    def update_vectors(self):
        from django.contrib.postgres.search import SearchVector
        Document.objects.filter(pk=self.pk).update(
            content_vector=SearchVector('content')
        )
