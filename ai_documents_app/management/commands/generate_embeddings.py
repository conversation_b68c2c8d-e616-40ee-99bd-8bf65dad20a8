from django.core.management.base import BaseCommand
from superadmin_dashboard.models import DocumentTemplate
from ai_documents_app.services.groq_embedding_service import GroqEmbeddingService
from ai_documents_app.models import TemplateEmbedding


class Command(BaseCommand):
    help = 'Generate embeddings for all templates that don\'t have them'

    def handle(self, *args, **kwargs):
        embedding_service = GroqEmbeddingService()

        # Get templates without embeddings
        templates_without_embeddings = DocumentTemplate.objects.exclude(
            id__in=TemplateEmbedding.objects.values_list('template_id', flat=True)
        )

        total = templates_without_embeddings.count()
        self.stdout.write(f"Generating embeddings for {total} templates...")

        for i, template in enumerate(templates_without_embeddings, 1):
            try:
                embedding_service.create_template_embedding(template)
                self.stdout.write(
                    f"Generated embedding for template {template.id} ({i}/{total})")
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"Error generating embedding for template {template.id}: {e}"
                    )
                )
