# Generated by Django 5.1.1 on 2025-03-24 17:09

import django.contrib.postgres.search
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("documents", "0001_initial"),
        ("superadmin_dashboard", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Document",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content", models.TextField()),
                (
                    "content_vector",
                    django.contrib.postgres.search.SearchVectorField(null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DocumentEmbedding",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content", models.TextField(default="")),
                ("embedding", models.<PERSON><PERSON><PERSON><PERSON>()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="documents.userdocument",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["created_at"], name="ai_document_created_344038_idx"
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="TemplateEmbedding",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content", models.TextField()),
                ("embedding", models.JSONField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="superadmin_dashboard.documenttemplate",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["created_at"], name="ai_document_created_a1827c_idx"
                    ),
                    models.Index(
                        fields=["template"], name="ai_document_templat_71e7fa_idx"
                    ),
                ],
            },
        ),
    ]
