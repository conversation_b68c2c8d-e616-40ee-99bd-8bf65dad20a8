from django.db.models.signals import post_save
from django.dispatch import receiver
from superadmin_dashboard.models import DocumentTemplate
from .services.groq_embedding_service import GroqEmbeddingService
from .services.openai_embedding_service import OpenAIEmbeddingService
from .models import TemplateEmbedding

import logging

logger = logging.getLogger(__name__)

@receiver(post_save, sender=DocumentTemplate)
def generate_template_embedding(sender, instance, created, **kwargs):
    """
    Generate and store embeddings whenever a new template is created or updated
    """
    if created or not TemplateEmbedding.objects.filter(template=instance).exists():
        try:
            # Choose your preferred embedding service
            embedding_service = GroqEmbeddingService()  # or OpenAIEmbeddingService()
            embedding_service.create_template_embedding(instance)
            logger.info(f"Generated embedding for template {instance.id}")
        except Exception as e:
            logger.error(f"Error generating embedding for template {instance.id}: {e}") 