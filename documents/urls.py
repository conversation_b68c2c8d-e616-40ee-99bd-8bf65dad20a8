from django.urls import include, path
from documents.views.access_request_views import DocumentAccessRequestViewSet
from documents.views.directory_views import UserDocumentDirectoryViewSet
from documents.views.document_views import (  # DocumentDownloadPreviewOneTimeApi,
    ActivityLogDownloadView,
    ActivityLogView,
    DeleteFromTrashView,
    DocumentCountsAPIView,
    DocumentDashboardListView,
    DocumentListView,
    DocumentPreiviewView,
    DocumentPreviewListCreateView,
    DocumentPreviewRetrieveViewApi,
    DocumentPreviewSignerView,
    DocumentPreviewWithActivityView,
    DocumentReminderView,
    DocumentRetrieveViewApi,
    DocumentSignerManageAPIView,
    DocumentSignerUpdateView,
    DocumentTrashView,
    DocumentVerificationDetailsView,
    DocumentVerificationView,
    OneTimeJWTManageAPIView,
    RestoreFromTrashView,
    SendSignerEmailAPIView,
    SignerUpdateManagementAPIView,
    UpdateLatestDocumentFileAPIView,
    UpdateSignerDetailsView,
    UserDocumentListCreateAPIView,
    UserDocumentRetrieveUpdateDestroyAPIView,
    ResendSignedDocumentAPIView, DirectoryDocumentDeleteView,
)
from documents.views.organisation_contacts_views import (
    OrganisationContactsDetailAPIView,
    OrganisationContactsListCreateAPIView,
)
from documents.views.user_document_template_views import (
    SystemDocumentTemplateDetailView,
    UserDocumentTemplateDetailView,
    UserDocumentTemplateView,
    UserTemplateCategoryView,
)
from rest_framework.routers import DefaultRouter
from .views.card_views import CardManagementAPIView, SetupIntentAPIView

# Create a router and register the viewset
router = DefaultRouter()
router.register(r"directories", UserDocumentDirectoryViewSet, basename="directory")
router.register(
    r"access-requests", DocumentAccessRequestViewSet, basename="access-request"
)

urlpatterns = [
    path("", include(router.urls)),
    # List and created user template categories
    path(
        "user-template-categories/",
        UserTemplateCategoryView.as_view(),
        name="user-template-categories",
    ),
    # List and create documents
    path(
        "details/", UserDocumentListCreateAPIView.as_view(), name="document-list-create"
    ),
    path(
        "details/update/<reference_id>/",
        UserDocumentListCreateAPIView.as_view(),
        name="document-update",
    ),
    # Retrieve, update, and delete a document using `reference_id` (as string, not UUID)
    path(
        "user-retrive-details/",
        UserDocumentRetrieveUpdateDestroyAPIView.as_view(),
        name="document-detail",
    ),
    path(
        "user-retrive-details/<str:reference_id>/",
        UserDocumentRetrieveUpdateDestroyAPIView.as_view(),
        name="document-detail",
    ),
    # Manage signers for a document using `reference_id` and `reference_signer_id`
    path(
        "details/signers/",
        DocumentSignerManageAPIView.as_view(),
        name="document-signers",
    ),
    # Manage a single signer using `reference_signer_id` and `reference_id`
    path(
        "details/signers/<str:reference_signer_id>/",
        DocumentSignerManageAPIView.as_view(),
        name="single-signer",
    ),
    path(
        "details/signers/<str:reference_signer_id>/update/",
        UpdateSignerDetailsView.as_view(),
        name="update-signer-details",
    ),
    path(
        "send-mail/",
        SendSignerEmailAPIView.as_view(),
        name="send-mail",
    ),
    path(
        "verify/<str:reference_user_id>/<str:reference_document_id>/<str:reference_signer_id>/",
        DocumentVerificationView.as_view(),
        name="document-verification",
    ),
    path(
        "details/preview_document/<str:reference_signer_id>/<str:reference_user_id>/<str:reference_document_id>/<str:reference_org_doc_id>/",
        DocumentPreviewListCreateView.as_view(),
        name="preview_document",
    ),
    path(
        "document-dashboard-list/",
        DocumentDashboardListView.as_view(),
        name="document-dashboard-list",
    ),
    path(
        "document-inbox/",
        DocumentListView.as_view(),
        name="document-inbox",
    ),
    path(
        "document-inbox-trash/",
        DocumentTrashView.as_view(),
        name="document-inbox-trash",
    ),
    path(
        "document-inbox-restore/",
        RestoreFromTrashView.as_view(),
        name="document-inbox-restore",
    ),
    path(
        "document-inbox-delete/",
        DeleteFromTrashView.as_view(),
        name="document-inbox-delete",
    ),
    path(
        "document-update-latest/",
        UpdateLatestDocumentFileAPIView.as_view(),
        name="document-update",
    ),
    path(
        "document-view-details/",
        DocumentPreiviewView.as_view(),
        name="document-view-details",
    ),
    path(
        "contacts/",
        OrganisationContactsListCreateAPIView.as_view(),
        name="contacts",
    ),
    path(
        "contacts/<str:id>/",
        OrganisationContactsDetailAPIView.as_view(),
        name="contacts-details",
    ),
    path(
        "document-activity-history/",
        ActivityLogView.as_view(),
        name="activity-log",
    ),
    path(
        "document-activity-history-download/",
        ActivityLogDownloadView.as_view(),
        name="activity-log-download",
    ),
    path("one_time_token/", OneTimeJWTManageAPIView.as_view(), name="one_time_token"),
    path(
        "retrieve/<str:reference_id>/documents/",
        DocumentRetrieveViewApi.as_view(),
        name="document-retriever",
    ),
    path(
        "document-signer-preview/",
        DocumentPreviewSignerView.as_view(),
        name="document-signer-preview",
    ),
    path(
        "document-reminder/",
        DocumentReminderView.as_view(),
        name="document-reminder",
    ),
    # path(
    #     "preview/<str:reference_id>/documents/",
    #     DocumentDownloadPreviewOneTimeApi.as_view(),
    #     name="document-preview",
    # ),
    path(
        "preview/<str:reference_id>/documents/",
        DocumentPreviewRetrieveViewApi.as_view(),
        name="document-preview",
    ),
    path(
        "sign/<str:reference_id>/documents/",
        DocumentSignerUpdateView.as_view(),
        name="document-sign",
    ),
    path(
        "signer-edit/",
        SignerUpdateManagementAPIView.as_view(),
        name="signer-edit",
    ),
    path(
        "user-document-templates/",
        UserDocumentTemplateView.as_view(),
        name="user-document-templates",
    ),
    path(
        "user-document-templates/<str:reference_id>/",
        UserDocumentTemplateDetailView.as_view(),
        name="user-document-template-detail",
    ),
    path(
        "system-document-templates/<str:reference_id>/",
        SystemDocumentTemplateDetailView.as_view(),
        name="system-document-template-detail",
    ),
    path(
        "document-preview-with-activity/",
        DocumentPreviewWithActivityView.as_view(),
        name="document-preview-with-activity",
    ),
    path(
        "verification-download/",
        DocumentVerificationDetailsView.as_view(),
        name="document-verification",
    ),
    path("document-counts/", DocumentCountsAPIView.as_view(), name="document-counts"),
    path('documents/resend-signed/', ResendSignedDocumentAPIView.as_view(),
         name='resend-signed-document'),
    # Card Management URLs
    path('cards/', CardManagementAPIView.as_view(), name='card-list-create'),
    path('cards/<str:card_id>/', CardManagementAPIView.as_view(), name='card-detail'),
    path('setup-intent/', SetupIntentAPIView.as_view(), name='setup-intent'),
    path('directory-documents/<uuid:pk>/', DirectoryDocumentDeleteView.as_view(),
         name='directory-document-delete'),
]
