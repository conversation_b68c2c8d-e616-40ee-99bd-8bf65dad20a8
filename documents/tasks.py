import io
import os
import smtplib
import logging
from datetime import timedelta
from cryptography.fernet import Fernet
from email.mime.image import MIMEImage
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from django.core.files import File
from celery import shared_task
from django.conf import settings
from django.db.models import Q
from django.core.files.base import ContentFile
from django.template.loader import render_to_string
from django.db import transaction
from django.utils import timezone
from pdf2image import convert_from_bytes
from PyPDF2 import PdfReader, PdfWriter
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from django.utils.translation import gettext as _

from .models import UserDocument, OrganizationDocument, ActivityLog
from .serializers.document_serializers import UserDocumentSerializer
from accounts.tasks import (
    load_html_template, send_background_email,
)
from accounts.services.mobile_authentication.mobile_clients import send_info_via_sms

cipher_suite = Fernet(settings.ENCRYPTION_KEY)


logger = logging.getLogger("app")


NOTIFICATION_WINDOWS = [
    ("24_hours", timedelta(hours=24)),
    ("1_week", timedelta(days=7)),
    ("1_month", timedelta(days=30)),
]


@shared_task
def send_document_expiry_email(sender, receiver, subject, html):
    # Create message container - the correct MIME type is multipart/alternative.
    msg = MIMEMultipart("alternative")
    msg["From"] = sender
    msg["To"] = receiver
    msg["Subject"] = subject
    # Attach HTML content to the email
    part2 = MIMEText(html, "html")
    msg.attach(part2)
    smtpObj = None

    try:
        file_path = None
        image_info = [
            "logo.png",
            "document-expiry.gif",
            "facebook2x.png",
            "instagram2x.png",
            "linkedin2x.png",
            "twitter2x.png",
        ]
        # image = 'logo.png'
        for image in image_info:
            file_path = os.path.join("documents", "images", image)
            with open(file_path, "rb") as f:  # Open in binary read mode!
                img = MIMEImage(f.read())
                img.add_header(
                    "Content-ID", "<{name}>".format(name=image)
                )  # Use correct ID as logo.png
                img.add_header("Content-Disposition", "inline", filename=image)
                msg.attach(img)
    except FileNotFoundError:
        print(f"Error: Image file not found at {file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")
    try:
        logger.info(f"Sending email to {receiver} with subject '{subject}'")
        # Establish an SMTP connection
        smtpObj = smtplib.SMTP("smtp.gmail.com", 587)
        smtpObj.starttls()  # Initiate TLS connection
        smtpObj.login(os.getenv("EMAIL_HOST_USER"), os.getenv("EMAIL_HOST_PASSWORD"))
        smtpObj.sendmail(sender, receiver, msg.as_string())
        logger.info(f"Email sent successfully to {receiver}")
    except smtplib.SMTPException as e:
        logger.error(f"Error: unable to send email. Error message: {str(e)}")
        print(f"Error: unable to send email. Error message: {str(e)}")
    finally:
        smtpObj.quit()


@shared_task
def check_expired_documents():
    """Check for expired documents and send notifications"""
    current_time = timezone.now()

    try:
        # Get expired documents that haven't been notified
        expired_documents = UserDocument.objects.filter(
            expiration_date__lte=current_time,
            is_expired=False,  # Documents that haven't been notified
        ).select_related("owner", "created_by")

        for document in expired_documents:
            recipients = []
            if document.owner and document.owner.email:
                recipients.append(document.owner)

            if (
                document.created_by
                and document.created_by.email
                and document.created_by != document.owner
            ):
                recipients.append(document.created_by)

            for recipient in recipients:
                try:
                    with transaction.atomic():
                        # Send email notification
                        subject = "Document Expired"
                        template_path = os.path.join(
                            "documents", "email", "document_exp.html"
                        )
                        document.is_expired = True
                        document.save()
                        # html_template = load_html_template(template_path)
                        
                        var_to_render = {
                            "greeting_text": _("Dear"),
                            "user": _(recipient.get_full_name()),
                            "document_title": _(document.title),
                            "document_name": _("Document Name"),
                            "document_expiry_date": document.expiration_date.strftime("%B %d, %Y"),
                            "document_details": _("Document Details"),
                            "document_expiry_date_label": _("Document Link Expiry"),
                            "button_name": _("Login In to Skrivly"),
                            "login_url": os.getenv('FE_BASE_URL')
                        }
                        
                        html_message = render_to_string(template_path, context=var_to_render)
                        
                        sender = settings.DEFAULT_FROM_EMAIL
                        receiver = recipient.get_decrypted_email()
                        subject = "Document Expired"
                        send_document_expiry_email.delay(
                            sender, receiver, subject, html_message
                        )

                        # Mark document as notified
                        document.meta_data = {
                            "expiration_notified": True,
                            "notification_sent_at": current_time.isoformat(),
                        }
                        document.save()

                        logger.info(
                            "Sent document expiration notification",
                            extra={
                                "document_id": str(document.id),
                                "owner_id": str(document.owner.id),
                                "created_by_id": str(document.owner.id),
                                "expiration_date": str(document.expiration_date),
                            },
                        )
                except Exception as e:
                    logger.error(
                        "Failed to process expired document",
                        extra={"document_id": str(document.id), "error": str(e)},
                        exc_info=True,
                    )
                    continue

        return True

    except Exception as e:
        logger.error(
            "Error in check_expired_documents task",
            extra={"error": str(e)},
            exc_info=True,
        )
        return False


@shared_task
def generate_document_thumbnail_task(document_id):
    try:
        # Import here to avoid circular imports
        from documents.models import UserDocument
        from documents.views.document_views import get_document_or_404, download_and_decrypt_from_s3

        instance = UserDocument.objects.get(id=document_id)

        # Get encryption components and decrypt file
        str_date_joined = instance.owner.date_joined.isoformat()
        str_created_obj = instance.created_at.isoformat()
        s3_url = instance.document_file.name
        final_str_datetime = str_date_joined + "/" + str_created_obj
        decrypted_data = download_and_decrypt_from_s3(s3_url, final_str_datetime)

        # Convert PDF to image and get first page
        pdf_bytes = io.BytesIO(decrypted_data)
        images = convert_from_bytes(pdf_bytes.getvalue())

        if images:
            first_page = images[0]
            thumb_buffer = io.BytesIO()
            first_page.save(thumb_buffer, format='JPEG')
            thumb_buffer.seek(0)

            # Simple filename for thumbnail
            filename = f"{instance.id}_thumbnail.jpg"

            # Direct save to model without triggering signals multiple times
            from django.db import transaction

            # Save the file manually without triggering multiple signals
            instance.thumbnail_file.save(
                filename,
                ContentFile(thumb_buffer.getvalue()),
                save=False  # Don't save yet
            )

            # Update the specific field in the database without triggering save signals
            instance.__class__.objects.filter(pk=instance.pk).update(
                thumbnail_file=instance.thumbnail_file)

            logger.info(
                "Thumbnail generated successfully",
                extra={'document_id': str(instance.id)}
            )
        else:
            logger.warning(
                "No pages found in PDF for thumbnail generation",
                extra={'document_id': str(instance.id)}
            )

    except Exception as e:
        logger.error(
            "Error processing document for thumbnail",
            extra={
                'document_id': str(instance.id),
                'error': str(e)
            },
            exc_info=True
        )


@shared_task
def send_document_for_digital_sign(doc_reference_id, organization_doc_reference_id, signers_data, language, message,
                                   download_pdf, expiry_date, device_data, ip_address, user_id, agreement_start_date,
                                   agreement_end_date):
    """Send Document for Digital Sign"""
    user_document = UserDocument.objects.filter(
        reference_id=doc_reference_id
    ).first()

    # Update the agreement start ad
    fields_to_update = {
        "expiration_date": expiry_date,
        "agreement_start_date": agreement_start_date,
        "agreement_end_date": agreement_end_date,
    }
    updated = False
    for field, value in fields_to_update.items():
        if value and value is not None:
            setattr(user_document, field, value)
            updated = True
    if updated:
        user_document.save()

    # print(f"\n\nUser Document Find : {doc_reference_id} \n {user_document}")
    document_organization = OrganizationDocument.objects.filter(
        encrypted_reference_id=organization_doc_reference_id,
        user_document=user_document,
    ).first()
    document_response = document_organization.user_document

    from documents.views.document_views import get_signer_or_404, download_and_decrypt_from_s3

    # Get encryption components and decrypt file
    str_date_joined = user_document.owner.date_joined.isoformat()
    str_created_obj = user_document.created_at.isoformat()
    s3_url = user_document.latest_document_file.name
    final_str_datetime = str_date_joined + "/" + str_created_obj
    decrypted_data = download_and_decrypt_from_s3(s3_url, final_str_datetime)

    # Convert PDF to image and get first page
    pdf_bytes = io.BytesIO(decrypted_data)

    # Add footer to every page of the PDF
    pdf_reader = PdfReader(pdf_bytes)
    pdf_writer = PdfWriter()

    # Create footer template
    footer_buffer = io.BytesIO()
    c = canvas.Canvas(footer_buffer, pagesize=letter)
    c.setFont("Helvetica", 6)  # Reduced font size to 6
    footer_text = f"Skrivly Transaction Id: {user_document.transaction_id}"

    # Get page width to center the text
    page_width = letter[0]
    text_width = c.stringWidth(footer_text, "Helvetica", 6)
    x_position = (page_width - text_width) / 2

    # Position at 10 points from bottom (very bottom of page)
    c.drawString(x_position, 10, footer_text)
    c.save()

    # Create footer PDF
    footer_buffer.seek(0)
    footer_pdf = PdfReader(footer_buffer)
    footer_page = footer_pdf.pages[0]

    # Add footer to each page
    for page_num in range(len(pdf_reader.pages)):
        page = pdf_reader.pages[page_num]
        page.merge_page(footer_page)
        pdf_writer.add_page(page)

    # Save the modified PDF
    output_buffer = io.BytesIO()
    pdf_writer.write(output_buffer)
    output_buffer.seek(0)
    pdf_bytes = output_buffer
    if pdf_bytes:
        logger.info(f"PDF Generated with Transaction ID ")
    else:
        logger.info(f"PDF Failed to be Generated with Transaction ID ")

    # Generate a unique filename for S3
    file_name = f"{user_document.transaction_id}_document.pdf"
    new_file_name = f"https://esign-docs.s3.eu-central-1.amazonaws.com/{file_name}"

    # Save the file locally temporarily
    with open(file_name, "wb") as f:
        f.write(pdf_bytes.getvalue())
    logger.info(f"PDF saved locally as {file_name}")

    from .views.document_views import update_document_password
    with open(file_name, "rb") as f:
        django_file = File(f, name=file_name)
        file_data = django_file.read()
        encrypted_data = cipher_suite.encrypt(file_data)
        document_file = ContentFile(encrypted_data, name=new_file_name)

        logger.info("Encrypted document file for UserDocument")

        # Only update latest_document_file, preserve document_file
        user_document.latest_document_file = document_file
        user_document.save()

        update_data = update_document_password(
            user_document.owner, user_document, update=True
        )
        data = UserDocumentSerializer(update_data).data
        print(f"data {data}")

    # Clean up temporary file
    if os.path.exists(file_name):
        os.remove(file_name)
        logger.info(f"Temporary file {file_name} removed")

    user_document.refresh_from_db()
    with transaction.atomic():
        for signer_data in signers_data:
            reference_signer_id = signer_data.get("reference_signer_id", None)
            if reference_signer_id:
                signer = get_signer_or_404(reference_signer_id, document_response)

                send_background_email.delay(
                    email=signer.get_decrypted_email(),
                    receiver_name=signer.get_decrypted_name(),
                    reference_signer_id=signer.reference_signer_id,
                    org_doc_reference_id=organization_doc_reference_id,
                    user_id=user_id,
                    document=document_response.reference_id,
                    language=language,
                    message=message,
                )
                # Log the activity
                if signer.phone_number:
                    phone = signer.get_decrypted_phone_number()

                    sender = None
                    if document_organization and document_organization.organization:
                        org = document_organization.organization
                        org_name = org.get_decrypted_name()
                        if org_name and org_name.strip():
                            sender = org_name.strip()

                    if not sender:  # fallback if no valid org name
                        org_owner = org.get_organization_owner()
                        if org_owner:
                            sender = org_owner.get_full_name()


                    if not sender:
                        sender = "Skrivly"  # or any default brand name

                    send_info_via_sms(
                        receiver_name=signer.get_decrypted_name(),
                        email=signer.get_decrypted_email(),
                        phone_number=phone,
                        sender_name=sender,
                    )

                messages = []
                if signer.email:
                    message = f"Sent to email ({signer.get_decrypted_email()})"
                    messages.append(message)

                if signer.phone_number:
                    messages.append(
                        f"phone number ({signer.get_decrypted_phone_number()})")

                if messages:
                    messages_str = " and ".join(messages)
                    print("messages_str", messages_str)

                activity_log = ActivityLog.objects.create(
                    document_refrence_id=signer.document.reference_id,
                    event_type="Sent",
                    device_data=device_data,
                    ip_address=(ip_address if ip_address else None),
                    title=signer.document.title,
                    email=signer.email,
                    category="Document Sent",
                    message=message,
                )
                if signer.phone_number:
                    activity_log.phone_number = signer.phone_number
                    activity_log.save()
                logger.info(f"Sent email for signer {reference_signer_id}.")

    user_document.status = "sent"
    user_document.save()
    logger.info(
        "\n\n--------------------------- Skrivly Transaction ID Creation End -------------------------\n\n")


@shared_task
def send_agreement_expiry_notifications():
    now = timezone.now()

    for notif_type, delta in NOTIFICATION_WINDOWS:
        # Find documents where the notification is due but not sent
        sent_key = f'notifications_status__agreement_notifications__{notif_type}__sent'
        isnull_key = f'notifications_status__agreement_notifications__{notif_type}__sent__isnull'

        docs = UserDocument.objects.filter(
            is_agreement=True,
            agreement_end_date__gt=now,  # Not expired
        ).filter(
            Q(**{sent_key: False}) |
            Q(**{isnull_key: True}) |
            Q(notifications_status__agreement_notifications__isnull=True) |
            Q(notifications_status__isnull=True)
        ).select_related('owner', 'created_by').prefetch_related("signers")
        docs = UserDocument.objects.filter(
            is_agreement=True,
            agreement_end_date__gt=now,  # Not expired
        ).select_related('owner', 'created_by').prefetch_related("signers")

        for document in docs:
            notifications = document.notifications_status or {}
            agreement_notif = notifications.get("agreement_notifications", {})
            try:
                if agreement_notif.get(notif_type, {}).get("sent"):
                    continue

                target_time = now + delta

                tolerance = timedelta(hours=8)

                # if now < document.agreement_end_date <= now+delta:
                if target_time-tolerance <= document.agreement_end_date <= target_time + tolerance:
                    recipients = []
                    if document.owner and document.owner.email:
                        recipients.append(document.owner)

                    if (
                        document.created_by
                        and document.created_by.email
                        and document.created_by != document.owner
                    ):
                        recipients.append(document.created_by)

                    # Get all signers for the document and include them as recipients
                    signers = document.signers.all()
                    for signer in signers:
                        if signer.email and signer not in recipients:
                            recipients.append(signer)

                    for recipient in recipients:
                        # Send email notification
                        subject = f"Agreement Expiry Notification: {notif_type.capitalize()} - {document.title}"
                        template_path = os.path.join(
                            "documents", "email", "agreement_expiry.html"
                        )
                        document.is_expired = True
                        document.save()
                        # html_template = load_html_template(template_path)

                        document_exp_title = ""
                        agr_exp_notification = ""
                        agr_msg_1 = ""
                        agr_msg_2 = ""

                        if notif_type == "24_hours":
                            document_exp_title = "Your document expires tomorrow"
                            agr_exp_notification = "This is your final reminder — the document below will expire tomorrow"
                            agr_msg_1 = "If this contract needs to be extended, renewed, or replaced — we recommend handling it before the deadline."
                            agr_msg_2 = "Log in to your Skrivly Dashboard to view or manage your documents."
                        elif notif_type == "1_week":
                            document_exp_title = "Your document will expire in 7 days"
                            agr_exp_notification = "This is a friendly heads-up that your document is set to expire in 7 days"
                            agr_msg_1 = "If this contract needs to be extended, renewed, or replaced — we recommend handling it before the deadline."
                            agr_msg_2 = "You can manage or download the document anytime by logging into your Skrivly account."
                        else:
                            document_exp_title = "Your document will expire in 30 days"
                            agr_exp_notification = "Just a quick reminder from Skrivly — the following document is set to expire in 30 days"
                            agr_msg_1 = "If any action or renewal is needed, now’s a great time to review it and make necessary changes."
                            agr_msg_2 = "Signed securely via Skrivly – your trusted digital signature provider."


                        context = {
                            "document_end_title_text": _(document_exp_title),
                            "document_greeting": _("Hi"),
                            "user_first_name": recipient.get_decrypted_name() if hasattr(recipient, "get_decrypted_name") else recipient.first_name,
                            "agreement_exp_reminder": _(agr_exp_notification),
                            "document_title": _(document.title),
                            "document_id": document.id,
                            "agreement_end_date": document.agreement_end_date.strftime("%B %d, %Y"),
                            "agreement_exp_msg_1": _(agr_msg_1),
                            "agreement_exp_msg_2": _(agr_msg_2),
                            "login_url": f"{os.getenv('FE_BASE_URL')}/login",
                            "login_button_text": _("Log in to Skrivly")
                        }

                        html_message = render_to_string(template_path, context=context)


                        sender = settings.DEFAULT_FROM_EMAIL
                        receiver = recipient.get_decrypted_email()
                        subject = document_exp_title
                        send_document_expiry_email.delay(
                            sender, receiver, subject, html_message
                        )

                        # Mark document as notified
                        agreement_notif[notif_type] = {
                            "sent": True,
                            "sent_at": now.isoformat(),
                            "failed": False,
                            "fail_reason": None
                        }

                        logger.info(
                            "Sent document expiration notification",
                            extra={
                                "document_id": str(document.id),
                                "owner_id": str(document.owner.id),
                                "created_by_id": str(document.created_by.id),
                                "expiration_date": str(document.agreement_end_date),
                            },
                        )
                        logger.info(
                            f"Sending {notif_type} notification for document {document.id}")
                    break

            except Exception as e:
                logger.error(
                    f"Failed to send {notif_type} notification for document {document.id}: {str(e)}")
                agreement_notif[notif_type] = {
                    "sent": False,
                    "sent_at": None,
                    "failed": True,
                    "fail_reason": str(e)
                }
            notifications["agreement_notifications"] = agreement_notif
            document.notifications_status = notifications
            document.save(update_fields=["notifications_status"])
