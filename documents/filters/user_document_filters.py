from django_filters import rest_framework as filters  # type: ignore

from documents.models import UserDocument


class UserDocumentFilter(filters.FilterSet):
    created_at = filters.DateFromToRangeFilter()

    class Meta:
        model = UserDocument
        fields = {
            "title": ["icontains"],
            # "user": ["exact"],
            "created_at": ["exact", "lt", "gt", "lte", "gte"],
            "updated_at": ["exact", "lt", "gt", "lte", "gte"],
            "status": ["exact"],
        }
