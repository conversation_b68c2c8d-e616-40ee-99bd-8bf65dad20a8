<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta content="width=device-width, initial-scale=1" name="viewport" />
  <title>
    Verification issued by Skrivly
  </title>
  <style>
    body {
      font-family: 'Inter', sans-serif, Arial, sans-serif;
      background-color: white;
      color: #1a202c;
      max-width: 768px;
      margin: 0 auto;
      padding: 24px 16px;
    }

    .center {
      text-align: center;
    }

    .check-icon {
      color: #16a34a;
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }

    h1 {
      font-weight: 800;
      font-size: 1.25rem;
      margin-bottom: 0.25rem;
    }

    h1.main_heading {
      color: #000000;
      margin-bottom: 1.2rem;
      font-size: 1.8rem;
    }

    h1 span {
      font-weight: 800;
    }

    h2 {
      font-weight: 700;
      font-size: 1.125rem;
      margin-bottom: 0.5rem;
    }

    p.small-semibold {
      font-size: 0.75rem;
      font-weight: 600;
      color: #6b6b6b;
      margin-bottom: 1.5rem;
    }

    .verification-details {
      font-size: 0.75rem;
      font-weight: 600;
      color: #4a5568;
      margin-bottom: 1rem;
      text-align: center;
    }

    .verification-details p {
      margin: 0.3rem 0;
      font-weight: 200;
    }

    .verification-details .label {
      font-weight: 700;
      color: #494747;
    }
    .verification-details .label-heading {
      font-size: 1rem;
      margin: 1rem 0;
      font-weight: 700;
      color: #494747;
    }

    .icons-row {
      text-align: center;
      margin-bottom: 1rem;
    }

    .icons-row img {
      width: 40px;
      height: 40px;
      margin: 0 8px;
      vertical-align: middle;
    }

    .verified-by {
      font-size: 1.0rem;
      font-weight: 600;
      color: #000000;
      margin-bottom: 1rem;
      text-align: center;
    }

    table {
      width: 100%;
      font-size: 0.75rem;
      color: #1a202c;
      border: none;
      border-collapse: collapse;
      margin-bottom: 1rem;
    }

    thead {
      background-color: #f7fafc;
      font-weight: 700;
      text-align: left;
    }

    th,
    td {
      /* border: 1px solid #d1d5db; */
      padding: 4px 8px;
    }

    a.email-link {
      color: #1a202c;
      text-decoration: none;
    }

    .badge-row {
      text-align: center;
      margin-bottom: 0.5rem;
    }

    .badge-row img {
      width: 160px;
      height: 160px;
    }

    .footer-text {
      font-size: 0.5625rem;
      color: #4a5568;
      text-align: center;
      margin-bottom: 0.25rem;
    }

    .footer-link {
      color: #1d4ed8;
      text-decoration: none;
      font-size: 0.5625rem;
    }

    .footer-flags {
      display: inline-block;
      vertical-align: middle;
      margin-left: 4px;
      margin-right: 4px;
      width: 16px;
      height: 12px;
    }

    .footer-flags img {
      width: 100%;
      height: 100%;
      vertical-align: middle;
    }

    .footer-flex {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      font-size: 0.5625rem;
      color: #4a5568;
      margin-left: auto;
      margin-right: auto;
      text-align: center;
    }
  </style>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet" />
</head>

<body>
  <div class="center">
    <i class="fas fa-check check-icon" aria-hidden="true"></i>
  </div>
  <h1 class="center main_heading">
    Verification issued by <span>Skrivly.</span>
  </h1>
  <h2 class="center">
    All signatures are verified.
  </h2>
  <p class="small-semibold center">
    This certification uniquely includes a Transaction-ID, serving as evidence of the document’s finalization and
    existence.
  </p>
  <div class="verification-details" role="region" aria-label="Verification Details">
    <p><span class="label-heading">Verification Details</span></p>
    <p><span class="label">Transaction ID:</span> {{ transaction_id }}</p>
    <p><span class="label">Document Title:</span> {{ document_title }}</p>
    <p><span class="label">Completed on:</span> {{ completed_on }}</p>
    <p><span class="label">Number of Signatories:</span> {{ number_of_signatories }}</p>
    <p style="word-break: break-word;"><span class="label">Document Hash:</span> {{ document_hash }}</p>
  </div>
  <div class="icons-row" aria-label="Security and verification icons">
    <img alt="Blue circular icon with a white lock symbol in the center representing security"
      src="{{ gdpr_img }}" width="40" height="40" />
    <img alt="Blue circular icon with a yellow check mark symbol in the center representing verification"
      src="{{ gdpr_img_2 }}" width="40" height="40" />
    <img alt="Blue and white BankID logo icon representing electronic identification"
      src="{{ bank_id_img }}" width="40" height="40" />
  </div>
  <div class="center" style="text-align: center;font-size: 0.75rem;">
    <div class="verified-by" role="region" aria-label="Verified by Skrivly">
      <p><span class="label">Verified by Skrivly</span></p>
    </div>
    <p>This document was securely signed using Skrivly’s trusted infrastructure. All electronic signatures are legally
      binding and verifiable under the EU eIDAS Regulation (910/2014).</p>
  </div>

  <table role="table" aria-label="Signers details">
    <thead>
      <tr>
        <th scope="col">Signer</th>
        <th scope="col">Email</th>
        <th scope="col">Signature Type</th>
        <th scope="col">IP Address</th>
        <th scope="col">Timestamp (UTC)</th>
      </tr>
    </thead>
    <tbody>
      {% for signer in signers %}
      <tr>
        <td>{{ signer.name }}</td>
        <td><a href="mailto:{{ signer.email }}" class="email-link">{{ signer.email }}</a></td>
        <td>{{ signer.signature_type }}</td>
        <td>{{ signer.ip_address }}</td>
        <td>{{ signer.timestamp }}</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  <div class="badge-row">
    <img
      alt="Purple circular badge with yellow stars and a yellow ribbon, text 'Signatures Verified by Skrivly.com' in white and purple"
      src="{{ skrivly_logo }}" width="160" height="120" />
  </div>
  <p class="footer-text">
    Skrivly® is a registered trademark under Swedish law and is authorized as a Trust Service Provider under the EU
    eIDAS Regulation.
  </p>
  <p class="footer-text">(910/2014)</p>
  <p class="footer-text">
    Powered by <strong>Skrivly AB</strong> — Org. No: SE5592436868
  </p>
  <p class="footer-text">
    This Verification is issued with its own Transaction-ID, intended to verify the existence of the completed document.
  </p>
  <p class="footer-flex" role="contentinfo">
    <a href="https://www.skrivly.com" target="_blank" class="footer-link">www.skrivly.com</a>
    <span>— Made in Sweden se</span>
    <span class="footer-flags" aria-label="Swedish flag">
      <img src="{{ sweden_flag }}"
        alt="Small Swedish flag icon with blue and yellow colors" />
    </span>
    <span class="footer-flags" aria-label="European Union flag">
      <img src="{{ european_union_flag }}"
        alt="Small European Union flag icon with blue background and yellow stars" />
    </span>
  </p>
</body>

</html>