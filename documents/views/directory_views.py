from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q
from django.utils import timezone
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
import logging
from rest_framework.exceptions import ValidationError

from accounts.utils.user_utils import get_user_details
from esign.utils.custom_response import api_response
from esign.utils.custom_pagination import CustomPagination
from django_filters import rest_framework as filters

from documents.models import (
    UserDocumentDirectory,
    DirectoryDocument,
    OrganizationDocument, UserDocument
)
from documents.serializers.directory_serializers import (
    UserDocumentDirectorySerializer,
    DirectoryDocumentSerializer
)
from documents.permissions import IsOrganizationMember, IsOrganizationAdmin
from documents.utils.jwt_verifier import JWTAccessTokenAuthentication
from documents.views.document_views import download_and_decrypt_from_s3
from utils_app.logger_utils import log_user_action
from accounts.services.jwt_authentication.authentication import fernet_decrypt

logger = logging.getLogger('app')


class DirectoryFilter(filters.FilterSet):
    # Directory filters
    title = filters.CharFilter(lookup_expr='icontains')
    is_active = filters.BooleanFilter()
    created_at = filters.DateTimeFromToRangeFilter()

    # Document filters
    document_title = filters.CharFilter(method='filter_document_title')
    document_status = filters.CharFilter(method='filter_document_status')
    document_created_at = filters.DateTimeFromToRangeFilter(
        method='filter_document_created_at')

    def filter_document_title(self, queryset, name, value):
        return queryset.filter(documents__document__title__icontains=value).distinct()

    def filter_document_status(self, queryset, name, value):
        return queryset.filter(documents__document__status=value).distinct()

    def filter_document_created_at(self, queryset, name, value):
        if value.start:
            queryset = queryset.filter(documents__document__created_at__gte=value.start)
        if value.stop:
            queryset = queryset.filter(documents__document__created_at__lte=value.stop)
        return queryset.distinct()

    class Meta:
        model = UserDocumentDirectory
        fields = ['title', 'is_active', 'created_at',
                  'document_title', 'document_status', 'document_created_at']


class UserDocumentDirectoryViewSet(viewsets.ModelViewSet):
    serializer_class = UserDocumentDirectorySerializer

    queryset = UserDocumentDirectory.objects.all()
    authentication_classes = [JWTAccessTokenAuthentication]
    pagination_class = CustomPagination
    filter_backends = [filters.DjangoFilterBackend]
    filterset_class = DirectoryFilter

    def check_org_superadmin(self, message="Only organization super admins can perform this action"):
        """Helper method to check if user is org_superadmin"""
        if self.request.user.role != 'org_superadmin':
            raise ValidationError(message)

    def get_queryset(self):
        """Return directories filtered by user's organization"""
        queryset = UserDocumentDirectory.objects.all()
        queryset = queryset.filter(is_active=True)
        queryset = queryset.select_related(
            'created_by', 'updated_by').prefetch_related('documents__document')

        # Filter by user's organization
        if self.request.user.role == 'org_superadmin':
            queryset = queryset.filter(
                organization__in=self.request.user.organizations.all())
        else:
            user_organisation = get_user_details(self.request.user.reference_id)
            org_id = user_organisation.get("organisation_details", {}).get("id")
            queryset = queryset.filter(organization_id=org_id)

        # Search filter
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(title__icontains=search) |
                Q(documents__document__title__icontains=search)
            ).distinct()

        # Active status filter
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        return queryset.order_by('-created_at')

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        # Get document sorting parameters with default as 'added_at'
        doc_sort_field = request.query_params.get('document_sort', '-added_at')

        # Map frontend sort fields to actual model fields
        sort_field_mapping = {
            'created_at': 'added_at',
            '-created_at': '-added_at',
            'title': 'document__title',
            '-title': '-document__title',
            'status': 'document__status',
            '-status': '-document__status'
        }

        # Use mapped field or default to the original if not in mapping
        doc_sort_field = sort_field_mapping.get(doc_sort_field, doc_sort_field)

        # Serialize directories with their documents
        serialized_data = []
        for directory in page:
            directory_data = self.get_serializer(directory, context={
                'request': request,
                'user': request.user,
                'user_role': request.user.role,
                'user_reference_id': request.user.reference_id
            }).data

            # Get and filter documents based on user's organization access
            documents = DirectoryDocument.objects.filter(
                directory=directory,
                is_active=True
            )

            if request.user.role == 'org_superadmin':

                documents = documents.filter(
                    document__organization__in=request.user.organizations.all()
                )
            else:
                user_organisation = get_user_details(request.user.reference_id)
                org_id = user_organisation.get("organisation_details", {}).get("id")
                documents = documents.filter(
                    document__organization_id=org_id
                )

            documents = documents.select_related('document').order_by(doc_sort_field)

            # Apply document filters if any
            doc_title = request.query_params.get('document_title')
            if doc_title:
                documents = documents.filter(document__title__icontains=doc_title)

            doc_status = request.query_params.get('document_status')
            if doc_status:
                documents = documents.filter(document__status=doc_status)

            directory_data['documents'] = DirectoryDocumentSerializer(
                documents,
                many=True,
                context={
                    'request': request,
                    'user': request.user,
                    'user_role': request.user.role,
                    'user_reference_id': request.user.reference_id
                }
            ).data
            serialized_data.append(directory_data)

        return self.paginator.get_paginated_response(
            data=serialized_data,

        )

    def create(self, request, *args, **kwargs):
        try:
            self.check_org_superadmin(
                "Only organization super admins can create directories")
            serializer = self.get_serializer(
                data=request.data,
                context={
                    'request': request,
                    'user': request.user,
                    'user_role': request.user.role,
                    'user_reference_id': request.user.reference_id
                }
            )
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            return api_response(
                action="data_created",
                data=serializer.data,
                status="success"
            )
        except ValidationError as e:
            return api_response(
                action="data_not_created",
                message=str(e),
                status=403
            )
        except Exception as e:
            return api_response(
                action="data_not_created",
                message=str(e)
            )

    def retrieve(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            directory_data = self.get_serializer(
                instance,
                context={
                    'request': request,
                    'user': request.user,
                    'user_role': request.user.role,
                    'user_reference_id': request.user.reference_id
                }
            ).data

            # Get document sorting parameters with default as 'added_at'
            doc_sort_field = request.query_params.get('document_sort', '-added_at')

            # Map frontend sort fields to actual model fields
            sort_field_mapping = {
                'created_at': 'added_at',
                '-created_at': '-added_at',
                'title': 'document__title',
                '-title': '-document__title',
                'status': 'document__status',
                '-status': '-document__status'
            }

            # Use mapped field or default to the original if not in mapping
            doc_sort_field = sort_field_mapping.get(doc_sort_field, doc_sort_field)

            # Get and filter documents
            documents = DirectoryDocument.objects.filter(
                directory=instance,
                is_active=True
            )
            print(f" documents {documents}")
            if request.user.role == 'org_superadmin':
                print(f"Here {[org.id for org in request.user.organizations.all()]}")
                print(documents.values("document__organization_id"))

                documents = documents.filter(
                    document__organization_id__in=[
                        org.id for org in request.user.organizations.all()]
                )

                print(documents.values_list("document__organization", flat=True))
            else:
                documents = documents.filter(
                    document__organization=request.user.organization
                )

            documents = documents.select_related('document').order_by(doc_sort_field)

            doc_title = request.query_params.get('document_title')
            doc_status = request.query_params.get('document_status')

            if doc_title:
                documents = documents.filter(document__title__icontains=doc_title)
            if doc_status:
                documents = documents.filter(document__status=doc_status)

            # Create a paginator instance
            paginator = CustomPagination()
            paginated_documents = paginator.paginate_queryset(documents, request)

            # Serialize the paginated documents
            document_serializer = DirectoryDocumentSerializer(
                paginated_documents,
                many=True,
                context={
                    'request': request,
                    'user': request.user,
                    'user_role': request.user.role,
                    'user_reference_id': request.user.reference_id
                }
            )

            # Add paginated documents to directory data
            directory_data['documents'] = document_serializer.data

            # Get pagination metadata
            pagination_data = {
                'count': paginator.page.paginator.count,
                'next': paginator.get_next_link(),
                'previous': paginator.get_previous_link(),
                'current_page': paginator.page.number,
                'total_pages': paginator.page.paginator.num_pages,
            }

            return api_response(
                action="data_retrieved",
                data={
                    'directory': directory_data,
                    'pagination': pagination_data
                },
                status="success",
                message="Directory retrieved successfully"
            )

        except Exception as e:
            logger.error(
                "Error retrieving directory",
                extra={
                    'directory_id': kwargs.get('pk'),
                    'error': str(e)
                },
                exc_info=True
            )
            return api_response(
                action="data_not_retrieved",
                message=str(e)
            )

    def perform_create(self, serializer):
        try:
            # Get the first organization of the user since a user can belong to multiple organizations
            organization = self.request.user.organizations.first()
            if not organization:
                raise ValidationError("User does not belong to any organization")

            serializer.save(
                organization=organization,
                created_by=self.request.user,
                updated_by=self.request.user,
            )
            log_user_action(
                request=self.request,
                user=self.request.user,
                action='Directory created successfully',
                status='success',
                details={'type': 'directory_created'}
            )
            logger.info(
                "Directory created",
                extra={
                    'user_id': str(self.request.user.id),
                    'organization_id': str(organization.id),
                    'directory_id': str(serializer.instance.id)
                }
            )
        except Exception as e:
            logger.error(
                "Error creating directory",
                extra={
                    'user_id': str(self.request.user.id),
                    'error': str(e)
                },
                exc_info=True
            )
            raise

    def destroy(self, request, *args, **kwargs):
        try:
            self.check_org_superadmin(
                "Only organization super admins can delete directories")
            directory = self.get_object()
            # if directory.is_default:
            #     return api_response(
            #         action="data_not_deleted",
            #         message="Cannot delete the default Agreements directory",
            #         status=403
            #     )

            directory.archive(request.user)
            # directory.delete()

            log_user_action(
                request=request,
                user=request.user,
                action='Directory archived successfully',
                status='success',
                details={'type': 'directory_archived'}
            )

            return api_response(
                action="data_deleted",
                message="Directory archived successfully"
            )
        except ValidationError as e:
            return api_response(
                action="data_not_deleted",
                message=str(e),
                status=403
            )
        except Exception as e:
            logger.error(
                "Error archiving directory",
                extra={
                    'directory_id': kwargs.get('pk'),
                    'error': str(e)
                },
                exc_info=True
            )
            return api_response(
                action="data_not_deleted",
                message=str(e)
            )

    def partial_update(self, request, *args, **kwargs):
        try:
            self.check_org_superadmin(
                "Only organization super admins can update directories")
            instance = self.get_object()
            serializer = self.get_serializer(
                instance,
                data=request.data,
                partial=True,
                context={
                    'request': request,
                    'user': request.user,
                    'user_role': request.user.role,
                    'user_reference_id': request.user.reference_id
                }
            )
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            return api_response(
                action="data_updated",
                data=serializer.data,
                status="success"
            )
        except ValidationError as e:
            return api_response(
                action="data_not_updated",
                message=str(e),
                status=403
            )
        except Exception as e:
            return api_response(
                action="data_not_updated",
                message=str(e)
            )

    @action(detail=True, methods=['post'])
    def add_document(self, request, pk=None):
        try:
            directory = self.get_object()
            encrypted_document_id = request.data.get('organisation_doc_reference_id')

            if not encrypted_document_id:
                return api_response(
                    action="data_not_created",
                    message="organisation_doc_reference_id is required",
                    status=400
                )

            try:
                doc_reference_id = self.request.data.get(
                    "organisation_doc_reference_id", None)
                org_document = OrganizationDocument.objects.filter(
                    encrypted_reference_id=doc_reference_id,
                    organization=directory.organization

                ).first()
                print(f"org_document {org_document}")
            except (TypeError, ValueError) as e:
                logger.error(
                    "Error processing document ID",
                    extra={
                        'encrypted_id': encrypted_document_id,
                        'error': str(e)
                    }
                )
                return api_response(
                    action="data_not_created",
                    message="Invalid document ID format",
                    status=400
                )
            except OrganizationDocument.DoesNotExist:
                return api_response(
                    action="data_not_created",
                    message="Document not found or not accessible",
                    status=404
                )

            # Check if document already exists in any directory
            existing_dir_doc = DirectoryDocument.objects.filter(
                document=org_document,
                is_active=True
            ).first()

            if existing_dir_doc:
                # Move document to new directory
                existing_dir_doc.move_to_directory(directory, request.user)
            else:
                DirectoryDocument.objects.create(
                    directory=directory,
                    document=org_document,
                    added_by=request.user
                )

            log_user_action(
                request=request,
                user=request.user,
                action='Document added to directory successfully',
                status='success',
                details={'type': 'document_added_to_directory'}
            )

            return api_response(
                action="data_created",
                message="Document added to directory successfully",
                status="success"
            )
        except Exception as e:
            logger.error(
                "Error adding document to directory",
                extra={
                    'directory_id': str(pk),
                    'error': str(e)
                },
                exc_info=True
            )
            return api_response(
                action="data_not_created",
                message=str(e)
            )

    @action(detail=True, methods=['post'])
    def remove_document(self, request, pk=None):
        try:
            directory = self.get_object()
            doc_reference_id = request.data.get('organisation_doc_reference_id')

            if not doc_reference_id:
                return api_response(
                    action="data_not_updated",
                    message="organisation_doc_reference_id is required",
                    status=400
                )

            # Get the document using reference_id
            org_document = OrganizationDocument.objects.filter(
                encrypted_reference_id=doc_reference_id,
                organization=directory.organization
            ).first()

            if not org_document:
                return api_response(
                    action="data_not_updated",
                    message="Document not found",
                    status=404
                )

            try:
                dir_doc = DirectoryDocument.objects.get(
                    directory=directory,
                    document=org_document,
                    is_active=True
                )
            except DirectoryDocument.DoesNotExist:
                return api_response(
                    action="data_not_updated",
                    message="Document not found in directory",
                    status=404
                )

            # Mark the current directory association as inactive
            dir_doc.is_active = False
            dir_doc.removed_by = request.user
            dir_doc.removed_at = timezone.now()
            dir_doc.save()

            # Move to default directory if this was the last active directory
            active_dir_docs = DirectoryDocument.objects.filter(
                document=org_document,
                is_active=True
            )

            if not active_dir_docs.exists():
                try:
                    default_directory = UserDocumentDirectory.objects.get(
                        organization=directory.organization,
                        is_default=True
                    )

                    # Don't move to default if we're removing from default
                    if default_directory.id != directory.id:
                        DirectoryDocument.objects.create(
                            directory=default_directory,
                            document=org_document,
                            added_by=request.user
                        )
                        move_message = "Document moved to default directory"
                    else:
                        move_message = "Document removed from default directory"
                except UserDocumentDirectory.DoesNotExist:
                    logger.error(
                        "Default directory not found for organization",
                        extra={
                            'organization_id': str(directory.organization.id)
                        }
                    )
                    move_message = "Document removed (default directory not found)"
            else:
                move_message = "Document removed from directory"

            log_user_action(
                request=request,
                user=request.user,
                action='Document removed from directory successfully',
                status='success',
                details={
                    'type': 'document_removed_from_directory',
                    'directory_id': str(directory.id),
                    'document_id': str(org_document.id)
                }
            )

            return api_response(
                action="data_updated",
                message=move_message,
                status="success"
            )
        except Exception as e:
            logger.error(
                "Error removing document from directory",
                extra={
                    'directory_id': str(pk),
                    'error': str(e)
                },
                exc_info=True
            )
            return api_response(
                action="data_not_updated",
                message=str(e)
            )


def add_document_to_directory(directory, encrypted_document_id, user):
    try:
        # Decrypt the document ID
        document_id = fernet_decrypt(encrypted_document_id.encode())

        # Get the organization document
        org_document = OrganizationDocument.objects.get(
            id=document_id,
            organization=directory.organization
        )
        print(f"org_document {org_document}")
        # Check if document exists in any directory
        existing_dir_doc = DirectoryDocument.objects.filter(
            document=org_document,
            is_active=True
        ).first()

        if existing_dir_doc:
            # Move document to new directory
            existing_dir_doc.move_to_directory(directory, user)
        else:
            # Create new directory association
            DirectoryDocument.objects.create(
                directory=directory,
                document=org_document,
                added_by=user
            )

        return True, "Document added successfully"

    except Exception as e:
        logger.error(
            "Error adding encrypted document to directory",
            extra={
                'directory_id': str(directory.id),
                'encrypted_document_id': encrypted_document_id,
                'error': str(e)
            },
            exc_info=True
        )
        return False, str(e)


def handle_encrypted_document(encrypted_doc_id, directory_id, user):
    try:
        directory = UserDocumentDirectory.objects.get(id=directory_id)
        success, message = add_document_to_directory(directory, encrypted_doc_id, user)

        if success:
            log_user_action(
                user=user,
                action='Document added to directory successfully',
                status='success',
                details={'type': 'document_added_to_directory'}
            )
            return True, message
        return False, message

    except UserDocumentDirectory.DoesNotExist:
        return False, "Directory not found"
