from itertools import count

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.conf import settings
import stripe
from django.utils.translation import gettext_lazy as _
import logging
from documents.utils.jwt_verifier import (
    JWTAccessTokenAuthentication,
)
from accounts.services.jwt_authentication.authentication import fernet_decrypt
from subscriptions.services.payment_handler import PaymentHandler

logger = logging.getLogger("app")


def check_card_exists(customer_id, last4, brand):
    cards = stripe.PaymentMethod.list(
        customer=customer_id,
        type="card"
    )
    cards_count = 0
    for card in cards['data']:
        if (
            card['card']['last4'] == last4 and
            card['card']['brand'].lower() == brand.lower()
        ):
            cards_count += 1
    return cards_count


class SetupIntentAPIView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            stripe.api_key = settings.STRIPE_SECRET_KEY

            # Get or create Stripe customer for the user
            customer = self._get_or_create_customer(request.user)

            # Create SetupIntent for this customer
            setup_intent = stripe.SetupIntent.create(
                customer=customer.id,
                payment_method_types=['card']
            )

            return Response({
                'status': 'success',
                'data': {
                    'client_secret': setup_intent.client_secret,
                    'customer_id': customer.id
                }
            })

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error in setup intent creation: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error in setup intent creation: {str(e)}")
            return Response({
                'status': 'error',
                'message': _('An error occurred while creating the setup intent')
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_or_create_customer(self, user):
        try:
            if not getattr(user, 'stripe_customer_id', None):
                customer = stripe.Customer.create(email=user.email)
                user.stripe_customer_id = customer.id
                user.save()
            else:
                customer = stripe.Customer.retrieve(user.stripe_customer_id)

            return customer

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error in customer creation/retrieval: {str(e)}")
            raise


class CardManagementAPIView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        stripe.api_key = settings.STRIPE_SECRET_KEY

    def post(self, request):
        try:
            payment_method_id = request.data.get('payment_method_id')
            if not payment_method_id:
                return Response({
                    'status': 'error',
                    'message': _('Payment method ID is required')
                }, status=status.HTTP_400_BAD_REQUEST)

            # Retrieve payment method and validate
            try:
                payment_method = stripe.PaymentMethod.retrieve(payment_method_id)
                card = payment_method.card
            except stripe.error.InvalidRequestError:
                return Response({
                    'status': 'error',
                    'message': _('Invalid or unverified card.')
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get or create customer
            customer = self._get_or_create_customer(request.user)

            # Check if card already exists
            if check_card_exists(customer.id, card.last4, card.brand) > 1:
                # Remove any duplicate cards
                PaymentHandler.remove_duplicate_cards(customer.id)

                return Response({
                    'status': 'error',
                    'message': _('This card already exists for this customer.')
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'status': 'success',
                'data': {
                    'id': payment_method.id,
                    'brand': card.brand,
                    'last4': card.last4,
                    'exp_month': card.exp_month,
                    'exp_year': card.exp_year,
                    'is_default': True
                }
            })

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error in card creation: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error in card creation: {str(e)}")
            return Response({
                'status': 'error',
                'message': _('An error occurred while creating the card')
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, card_id):
        """
        Set a card as the default payment method
        """
        try:
            # Get or create customer
            customer = self._get_or_create_customer(request.user)

            # Set as default payment method
            stripe.Customer.modify(
                customer.id,
                invoice_settings={'default_payment_method': card_id}
            )

            # Get the updated payment method to return in response
            payment_method = stripe.PaymentMethod.retrieve(card_id)

            return Response({
                'status': 'success',
                'data': {
                    'id': payment_method.id,
                    'brand': payment_method.card.brand,
                    'last4': payment_method.card.last4,
                    'exp_month': payment_method.card.exp_month,
                    'exp_year': payment_method.card.exp_year,
                    'is_default': True
                }
            })

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error in setting default card: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error in setting default card: {str(e)}")
            return Response({
                'status': 'error',
                'message': _('An error occurred while setting the default card')
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get(self, request, card_id=None):
        """
        Retrieve a specific card or list all cards
        """
        try:
            customer = self._get_or_create_customer(request.user)

            if card_id:
                # Retrieve specific card
                payment_method = stripe.PaymentMethod.retrieve(card_id)
                return Response({
                    'status': 'success',
                    'data': {
                        'id': payment_method.id,
                        'brand': payment_method.card.brand,
                        'last4': payment_method.card.last4,
                        'exp_month': payment_method.card.exp_month,
                        'exp_year': payment_method.card.exp_year
                    }
                })
            else:
                # List all cards
                payment_methods = stripe.PaymentMethod.list(
                    customer=customer.id,
                    type='card'
                )

                cards = [{
                    'id': pm.id,
                    'brand': pm.card.brand,
                    'last4': pm.card.last4,
                    'exp_month': pm.card.exp_month,
                    'exp_year': pm.card.exp_year,
                    'is_default': customer.invoice_settings.default_payment_method == pm.id
                } for pm in payment_methods.data]

                return Response({
                    'status': 'success',
                    'data': cards
                })

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error in card retrieval: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error in card retrieval: {str(e)}")
            return Response({
                'status': 'error',
                'message': _('An error occurred while retrieving the card(s)')
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, card_id):
        """
        Delete a card
        """
        try:
            # Get customer
            customer = self._get_or_create_customer(request.user)

            # Check if this is the default payment method
            if customer.invoice_settings.default_payment_method == card_id:
                return Response({
                    'status': 'error',
                    'message': _('Cannot delete the default payment method. Please set another card as default first.')
                }, status=status.HTTP_400_BAD_REQUEST)

            # Detach payment method from customer
            stripe.PaymentMethod.detach(card_id)

            return Response({
                'status': 'success',
                'message': _('Card deleted successfully')
            })

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error in card deletion: {str(e)}")
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Error in card deletion: {str(e)}")
            return Response({
                'status': 'error',
                'message': _('An error occurred while deleting the card')
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_or_create_customer(self, user):
        """
        Get or create a Stripe customer for the user
        """
        try:
            # Check if user has a Stripe customer ID
            if hasattr(user, 'stripe_customer_id') and user.stripe_customer_id:
                return stripe.Customer.retrieve(user.stripe_customer_id)

            # Decrypt the email before passing to Stripe
            decrypted_email = fernet_decrypt(user.email) if user.email else None

            # Create new customer
            customer = stripe.Customer.create(
                email=decrypted_email,
                name=f"{user.first_name} {user.last_name}".strip() or decrypted_email,
                metadata={'user_id': user.id}
            )

            # Save customer ID to user
            user.stripe_customer_id = customer.id
            user.save()

            return customer

        except Exception as e:
            logger.error(f"Error in customer creation/retrieval: {str(e)}")
            raise
