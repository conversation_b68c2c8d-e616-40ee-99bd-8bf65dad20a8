import logging
from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q
from django.utils import timezone

from accounts.utils.user_utils import get_user_details
from documents.models import DocumentAccessRequest, UserDocument, OrganizationDocument
from documents.serializers.access_request_serializers import DocumentAccessRequestSerializer
from documents.permissions import IsOrganizationMember
from documents.utils.jwt_verifier import JWTAccessTokenAuthentication
from esign.utils.custom_response import api_response
from esign.utils.custom_pagination import CustomPagination
from utils_app.logger_utils import log_user_action

logger = logging.getLogger('app')


class DocumentAccessRequestViewSet(viewsets.ModelViewSet):
    serializer_class = DocumentAccessRequestSerializer
    permission_classes = [IsAuthenticated, IsOrganizationMember]
    authentication_classes = [JWTAccessTokenAuthentication]
    pagination_class = CustomPagination

    def get_queryset(self):
        """
        Return access requests based on query parameter:
        - sent_requests=True returns requests made by the user
        - sent_requests=False returns requests for user's documents (where user is owner or created_by)
        Default is sent_requests=True
        """
        user = self.request.user
        if self.action in ['list', 'retrieve']:
            sent_requests = self.request.query_params.get(
                'sent_requests', 'true').lower() == 'true'
            if sent_requests:
                # Return requests made by the user
                return DocumentAccessRequest.objects.filter(
                    requester=user
                ).distinct().order_by('-created_at')
            else:
                # Return requests for documents where user is owner or created_by
                return DocumentAccessRequest.objects.filter(
                    Q(document__owner=user) | Q(document__created_by=user)
                ).order_by('-created_at')
        return DocumentAccessRequest.objects.all()

    def create(self, request, *args, **kwargs):
        try:
            # Add requester and organization to request data
            request.data['requester'] = request.user.pk
            user_organisation = get_user_details(request.user.reference_id)
            request.data['organization'] = user_organisation.get(
                "organisation_details", {}).get("id")
            # Check if document exists and is accessible
            document_id = request.data.get('document')
            try:
                document = UserDocument.objects.get(id=document_id)
                # Check if there's an active request (pending or approved)
                existing_active_request = DocumentAccessRequest.objects.filter(
                    document=document_id,
                    requester=request.user,
                    status__in=['pending', 'approved']
                ).first()
                if existing_active_request:
                    return api_response(
                        action="data_not_created",
                        message=f"An active access request already exists with status: {existing_active_request.status}",
                        status=400
                    )

                if document.is_global:
                    return api_response(
                        action="data_not_created",
                        message="Cannot request access for global documents",
                        status=400
                    )
                # Create new request regardless of previous rejected/revoked requests
                serializer = self.get_serializer(data=request.data)
                serializer.is_valid(raise_exception=True)
                self.perform_create(serializer)
                log_user_action(
                    request=request,
                    user=request.user,
                    action='New access request created',
                    status='success',
                    details={'document_id': document_id}
                )
                return api_response(
                    action="data_created",
                    data=serializer.data,
                    status="success",
                    message="New access request created successfully"
                )
            except UserDocument.DoesNotExist:
                return api_response(
                    action="data_not_created",
                    message="Document not found",
                    status=404
                )
        except Exception as e:
            logger.error(
                "Error creating access request",
                extra={
                    'user_id': str(request.user.id),
                    'error': str(e)
                },
                exc_info=True
            )
            return api_response(
                action="data_not_created",
                message=str(e)
            )

    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        try:
            access_request = self.get_object()
            # Check if user is document owner or creator
            if not (access_request.document.owner == request.user or access_request.document.created_by == request.user):
                return api_response(
                    action="data_not_updated",
                    message="Not authorized to approve this request",
                    status=403
                )
            # Check if request is pending
            if access_request.status != 'pending':
                return api_response(
                    action="data_not_updated",
                    message=f"Cannot approve request with status: {access_request.status}",
                    status=400
                )
            note = request.data.get('note')
            access_request.approve(request.user, note)
            log_user_action(
                request=request,
                user=request.user,
                action='Access request approved',
                status='success',
                details={'request_id': str(access_request.id)}
            )
            return api_response(
                action="data_updated",
                message="Access request approved successfully",
                status="success"
            )
        except Exception as e:
            logger.error(
                "Error approving access request",
                extra={
                    'request_id': str(pk),
                    'error': str(e)
                },
                exc_info=True
            )
            return api_response(
                action="data_not_updated",
                message=str(e)
            )

    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        try:
            access_request = self.get_object()
            # Check if user is document owner or creator
            if not (access_request.document.owner == request.user or access_request.document.created_by == request.user):
                return api_response(
                    action="data_not_updated",
                    message="Not authorized to reject this request",
                    status=403
                )
            # Check if request is pending
            if access_request.status != 'pending':
                return api_response(
                    action="data_not_updated",
                    message=f"Cannot reject request with status: {access_request.status}",
                    status=400
                )
            note = request.data.get('note', "")
            access_request.reject(request.user, note)
            log_user_action(
                request=request,
                user=request.user,
                action='Access request rejected',
                status='success',
                details={'request_id': str(access_request.id)}
            )
            return api_response(
                action="data_updated",
                message="Access request rejected successfully",
                status="success"
            )
        except Exception as e:
            logger.error(
                "Error rejecting access request",
                extra={
                    'request_id': str(pk),
                    'error': str(e)
                },
                exc_info=True
            )
            return api_response(
                action="data_not_updated",
                message=str(e)
            )

    @action(detail=True, methods=['post'])
    def revoke(self, request, pk=None):
        try:
            access_request = self.get_object()

            # Check if user is document owner
            if not (access_request.document.owner == request.user or access_request.document.created_by == request.user):
                return api_response(
                    action="data_not_updated",
                    message="Not authorized to revoke this access",
                    status=403
                )

            # Check if request is approved (can only revoke approved requests)
            if access_request.status != 'approved':
                return api_response(
                    action="data_not_updated",
                    message=f"Cannot revoke request with status: {access_request.status}",
                    status=400
                )

            # Get note from request data
            note = request.data.get('note', "Document Access Revoked by Owner")

            # Call the model's revoke method
            try:
                access_request.revoke(request.user, note)
            except ValueError as ve:
                return api_response(
                    action="data_not_updated",
                    message=str(ve),
                    status=400
                )

            log_user_action(
                request=request,
                user=request.user,
                action='Access request revoked',
                status='success',
                details={
                    'request_id': str(access_request.id),
                    'document_id': str(access_request.document.id),
                    'note': note
                }
            )

            return api_response(
                action="data_updated",
                data={
                    'id': access_request.id,
                    'status': access_request.status,
                    'revoked_at': access_request.revoked_at,
                    'revoked_by': request.user.id,
                    'revocation_note': note
                },
                message="Access to document revoked successfully",
                status="success"
            )
        except Exception as e:
            logger.error(
                "Error revoking access request",
                extra={
                    'request_id': str(pk),
                    'error': str(e)
                },
                exc_info=True
            )
            return api_response(
                action="data_not_updated",
                message=str(e),
                status=500
            )
