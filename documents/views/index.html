<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Preview</title>
    <style>
        #pdf-canvas {
            border: 1px solid #ddd;
            margin-top: 20px;
            width: 100%;
        }
        .loader {
            font-size: 20px;
            color: #555;
            text-align: center;
            margin-top: 50px;
        }
    </style>
    <!-- Include PDF.js (via CDN) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.10.377/pdf.min.js"></script>
</head>
<body>

    <h2>Document Preview</h2>
    <div class="loader" id="loader">Loading...</div>

    <!-- Canvas for rendering PDF -->
    <canvas id="pdf-canvas"></canvas>

    <script>
        // The reference_id of the document you want to retrieve
        const document_path = '/media/https%3A/esign-docs.s3.amazonaws.com/4994fe8c-3f8f-409f-8f21-59d47a88d8ac.pdf';  // Replace with actual document path
        const base_url = 'http://127.0.0.1:8000';
        const language = 'en';
        const referenceId ='gAAAAABnPF66BCeiJaIoMCkTPHE8N2Ce-Qz13WFcJrNPfzF_Q0OZ7YnfnGcm3-4w85cGtgylgKbIDl4O5F_Hk3mlHF_BsEA0YQuND0kAr6UOXbQihywJwYkVMxSrShHUR_nABlxaNUXA'
        const api_url = `${base_url}/${language}/api/v1/user-documents/retrieve/${referenceId}/documents/`;

        // Authorization Bearer Token (Replace this with your actual token)
        const authToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWZlcmVuY2VfaWQiOiJnQUFBQUFCblBINjJFRXk5YTE3SFN2Zmt2Y2NQUmlQeXZXMlNyRi1GYkFHaE9qa21Sa2pjX2RYUWk5ZHRvVWtmSnFtM3ZiNlYyYlFtbU1Kbl9oa2Jjcl9qMEx2OTB5bnp6Q0xEUC05dXpBQ1R0NFVDZmNzMUM3UFVnU1hranc0UDJwWnQwVEdIeE5zNCIsImV4cCI6MTczMjAxOTY0NiwidHlwZSI6ImFjY2VzcyJ9.hO9UhLhbfEy8wsHNcMOCKWFVooe_WxJvct-j7gJuDbs';  // Replace with actual Bearer Token

        // Function to decode base64 string to binary
        function base64ToUint8Array(base64) {
            const binaryString = atob(base64);
            const length = binaryString.length;
            const uint8Array = new Uint8Array(length);

            for (let i = 0; i < length; i++) {
                uint8Array[i] = binaryString.charCodeAt(i);
            }

            return uint8Array;
        }

        // Function to fetch and render the PDF
        function fetchAndRenderPDF() {
            // Send a POST request with the document_path
            fetch(api_url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`,  // Add Bearer Token to Authorization header
                },
                body: JSON.stringify({ document_path: document_path })
            })
            .then(response => response.json())
            .then(data => {

                if (data.data && data.data.encrypted_file) {
                    const encryptedFileData = data.data.encrypted_file;  // Assuming the file is returned in base64 format

                    // Convert the encrypted base64 data into a binary format
                    const pdfData = base64ToUint8Array(encryptedFileData);

                    // Initialize PDF.js with the fetched data
                    const loadingTask = pdfjsLib.getDocument(pdfData);
                    loadingTask.promise.then(pdf => {
                        console.log('PDF loaded');
                        renderPDFPage(pdf, 1);  // Render the first page of the PDF
                    }).catch(error => {
                        console.error('Error loading PDF:', error);
                    });
                } else {
                    console.error('No document file in response');
                }
            })
            .catch(error => {
                console.error('Error fetching the document:', error);
            });
        }

        // Function to render a specific page of the PDF
        function renderPDFPage(pdf, pageNum) {
            pdf.getPage(pageNum).then(page => {
                console.log('Page loaded');

                const scale = 1.5;  // You can change the scale to zoom in/out
                const viewport = page.getViewport({ scale: scale });

                const canvas = document.getElementById('pdf-canvas');
                const context = canvas.getContext('2d');
                canvas.height = viewport.height;
                canvas.width = viewport.width;

                // Render the page into the canvas
                const renderContext = {
                    canvasContext: context,
                    viewport: viewport
                };
                page.render(renderContext).promise.then(() => {
                    // Hide the loader and show the canvas once the PDF is rendered
                    document.getElementById('loader').style.display = 'none';
                    canvas.style.display = 'block';
                }).catch(error => {
                    console.error('Error rendering page:', error);
                });
            });
        }

        // Call the fetch and render function when the page loads
        window.onload = fetchAndRenderPDF;
    </script>

</body>
