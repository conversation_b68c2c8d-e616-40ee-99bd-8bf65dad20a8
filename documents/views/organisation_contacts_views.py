from django.utils.translation import gettext as _
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from accounts.utils.user_utils import get_user_details
from documents.models.document_models import OrganisationContacts
from documents.serializers.organisation_contacts_serializers import OrganisationContactsSerializer
from documents.utils.jwt_verifier import JWTAccessTokenAuthentication
from esign.utils.custom_pagination import CustomPagination
from esign.utils.custom_response import api_response
from utils_app.logger_utils import log_user_action

import logging

logger = logging.getLogger('app')


class OrganisationContactsListCreateAPIView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    @swagger_auto_schema(
        operation_summary="List Organisation Contacts",
        manual_parameters=[
            openapi.Parameter(
                "search",
                openapi.IN_QUERY,
                description="Search contacts by name or email",
                type=openapi.TYPE_STRING,
            ),
            openapi.Parameter(
                "is_active",
                openapi.IN_QUERY,
                description="Filter by active status",
                type=openapi.TYPE_BOOLEAN,
            ),
        ],
        responses={200: OrganisationContactsSerializer(many=True)}
    )
    def get(self, request):
        try:
            user_organisation = get_user_details(request.user.reference_id)
            org_id = user_organisation.get("organisation_details", {}).get("id")

            queryset = OrganisationContacts.objects.filter(
                organisation_id=org_id
            ).order_by('-id')

            # Apply search filter
            search = request.query_params.get('search')
            if search:
                queryset = queryset.filter(
                    Q(name__icontains=search) |
                    Q(email__icontains=search)
                )

            # Apply active status filter
            is_active = request.query_params.get('is_active')
            if is_active is not None:
                queryset = queryset.filter(is_active=is_active.lower() == 'true')

            # Apply pagination
            paginator = self.pagination_class()
            page = paginator.paginate_queryset(queryset, request)
            serializer = OrganisationContactsSerializer(page, many=True)

            return paginator.get_paginated_response(serializer.data)

        except Exception as e:
            logger.error(
                "Error retrieving organisation contacts",
                extra={
                    'error': str(e),
                    'user_id': request.user.id,
                    'query_params': request.query_params
                },
                exc_info=True
            )
            return api_response(action="data_not_retrieved", message=str(e))

    @swagger_auto_schema(
        operation_summary="Create Organisation Contact",
        request_body=OrganisationContactsSerializer,
        responses={201: OrganisationContactsSerializer()}
    )
    def post(self, request):
        try:
            user_organisation = get_user_details(request.user.reference_id)
            org_id = user_organisation.get("organisation_details", {}).get("id")

            # Check for duplicate contact
            email = request.data.get('email')
            phone = request.data.get('phone_number')
            existing_contact = OrganisationContacts.objects.filter(

                Q(email=email) | Q(phone_number=phone), organisation_id=org_id
            ).first()

            if existing_contact:
                duplicate_field = 'email' if existing_contact.email == email else 'phone_number'
                return api_response(
                    action="validation_error",
                    message=f"Contact with this {duplicate_field} already exists in your organization"
                )

            serializer = OrganisationContactsSerializer(data=request.data)
            if serializer.is_valid():
                serializer.save(organisation_id=org_id)

                # Log the contact creation action
                log_user_action(
                    request=request,
                    user=request.user,
                    action='create_organisation_contact',
                    status='success',
                    details={'contact_id': serializer.instance.id,
                             'contact_name': serializer.instance.name}
                )

                return api_response(
                    action="data_created",
                    data=serializer.data,
                    status="success"
                )
            return api_response(action="validation_error", message=serializer.errors)

        except Exception as e:
            logger.error(
                "Error creating organisation contact",
                extra={
                    'error': str(e),
                    'user_id': request.user.id,
                    'request_data': request.data
                },
                exc_info=True
            )
            return api_response(action="data_not_created", message=str(e))


class OrganisationContactsDetailAPIView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get_object(self, id):
        try:
            return OrganisationContacts.objects.get(id=id)
        except OrganisationContacts.DoesNotExist:
            return None

    @swagger_auto_schema(
        operation_summary="Retrieve Organisation Contact",
        responses={200: OrganisationContactsSerializer()}
    )
    def get(self, request, id):
        try:
            contact = self.get_object(id)
            if not contact:
                return api_response(
                    action="data_not_retrieved",
                    message="Contact not found"
                )

            serializer = OrganisationContactsSerializer(contact)
            return api_response(
                action="data_retrieved",
                data=serializer.data,
                status="success"
            )

        except Exception as e:
            logger.error(f"Error retrieving organisation contact: {e}")
            return api_response(action="data_not_retrieved", message=str(e))

    @swagger_auto_schema(
        operation_summary="Update Organisation Contact",
        request_body=OrganisationContactsSerializer,
        responses={200: OrganisationContactsSerializer()}
    )
    def put(self, request, id):
        try:
            contact = self.get_object(id)
            if not contact:
                return api_response(
                    action="data_not_retrieved",
                    message="Contact not found"
                )

            # Check for duplicate contact excluding current contact
            email = request.data.get('email')
            phone = request.data.get('phone_number')
            existing_contact = OrganisationContacts.objects.filter(

                Q(email=email) | Q(phone_number=phone), organisation_id=contact.organisation.id
            ).exclude(id=id).first()

            if existing_contact:
                duplicate_field = 'email' if existing_contact.email == email else 'phone_number'
                return api_response(
                    action="validation_error",
                    message=f"Contact with this {duplicate_field} already exists in your organization"
                )

            serializer = OrganisationContactsSerializer(contact, data=request.data)
            if serializer.is_valid():
                serializer.save()

                # Log the contact update action
                log_user_action(
                    request=request,
                    user=request.user,
                    action='update_organisation_contact',
                    status='success',
                    details={'contact_id': id,
                             'updated_fields': serializer.validated_data}
                )

                return api_response(
                    action="data_updated",
                    data=serializer.data,
                    status="success"
                )
            return api_response(action="validation_error", message=serializer.errors)

        except Exception as e:
            logger.error(f"Error updating organisation contact: {e}")
            return api_response(action="data_not_updated", message=str(e))

    @swagger_auto_schema(
        operation_summary="Delete Organisation Contact",
        responses={204: "No Content"}
    )
    def delete(self, request, id):
        try:
            contact = self.get_object(id)
            if not contact:
                return api_response(
                    action="data_not_retrieved",
                    message="Contact not found"
                )

            contact.delete()

            # Log the contact deletion action
            log_user_action(
                request=request,
                user=request.user,
                action='delete_organisation_contact',
                status='success',
                details={'contact_id': id}
            )

            return api_response(
                action="data_deleted",
                status="success",
                message="Contact successfully deleted"
            )

        except Exception as e:
            logger.error(f"Error deleting organisation contact: {e}")
            return api_response(action="data_not_deleted", message=str(e))
