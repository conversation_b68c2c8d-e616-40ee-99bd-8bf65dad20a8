from django.db.models import Q
from django.utils.translation import gettext as _
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from documents.utils.jwt_verifier import JWTAccessTokenAuthentication
from esign.utils.custom_pagination import CustomPagination
from esign.utils.custom_response import api_response
from superadmin_dashboard.models import DocumentTemplate, UserDocumentTemplate
from superadmin_dashboard.models.document_templates import TemplateCategory
from superadmin_dashboard.serializers.document_template_serializers import (
    DocumentTemplateSerializer,
    UserDocumentTemplateSerializer,
)
from superadmin_dashboard.serializers.document_template_serializers import (
    TemplateCategorySerializer,
)
from utils_app.logger_utils import log_user_action


class UserTemplateCategoryView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    def get(self, request):
        try:
            if request.user.role not in ["app_admin", "super_admin"]:
                return api_response(
                    action="unauthorized_access", message=_("Unauthorized access")
                )

            queryset = TemplateCategory.objects.all().order_by("-id")

            # Apply search filter
            search = request.query_params.get("search")
            if search:
                queryset = queryset.filter(title__icontains=search)

            # Apply active status filter
            is_active = request.query_params.get("is_active")
            if is_active is not None:
                queryset = queryset.filter(is_active=is_active.lower() == "true")

            paginator = self.pagination_class()
            page = paginator.paginate_queryset(queryset, request)
            serializer = TemplateCategorySerializer(page, many=True)

            return paginator.get_paginated_response(serializer.data)

        except Exception as e:
            return api_response(action="data_not_retrieved", message=str(e))


class UserDocumentTemplateView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    def get(self, request):
        try:

            # Get user document templates
            user_templates = (UserDocumentTemplate.objects.filter(created_by=request.user.reference_id, is_active=True)
                              .order_by("-id"))
            # Get active document templates
            active_templates = DocumentTemplate.objects.filter(is_active=True).order_by(
                "-id"
            )

            # Apply filters
            search = request.query_params.get("search", None)
            if search:
                user_templates = user_templates.filter(
                    Q(title__icontains=search) | Q(category__title__icontains=search)
                )
                active_templates = active_templates.filter(
                    Q(title__icontains=search) | Q(category__title__icontains=search)
                )

            category = request.query_params.get("category", None)
            if category:
                user_templates = user_templates.filter(category_id=category)
                active_templates = active_templates.filter(category_id=category)

            active = request.query_params.get("active", None)
            if active is not None:
                user_templates = user_templates.filter(
                    is_active=active.lower() == "true")

            # Prepare data for both template types
            user_template_data = UserDocumentTemplateSerializer(
                user_templates, many=True, context={"request": request}
            ).data
            active_template_data = DocumentTemplateSerializer(
                active_templates, many=True, context={"request": request}
            ).data

            # Combine the results
            combined_data = {
                "user_templates": user_template_data,
                "active_templates": active_template_data,
            }

            return api_response(
                action="data_retrieved", data=combined_data, status="success"
            )

        except Exception as e:
            return api_response(action="data_not_retrieved", message=str(e))

    def post(self, request):
        try:
            print(f"request.user.reference_id {request.user.reference_id}")
            data = request.data
            data["created_by"] = request.user.reference_id

            serializer = UserDocumentTemplateSerializer(
                data=data, context={"request": request}
            )
            if serializer.is_valid():
                serializer.save()

                # Log the template creation action
                log_user_action(
                    request=request,
                    user=request.user,
                    action='create_user_document_template',
                    status='success',
                    details={'template_id': serializer.instance.id,
                             'template_title': serializer.instance.title}
                )

                return api_response(
                    action="data_created",
                    data=serializer.data,
                    status="success"
                )
            return api_response(action="validation_error", message=serializer.errors)

        except Exception as e:
            return api_response(action="data_not_created", message=str(e))


class UserDocumentTemplateDetailView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, reference_id):
        try:

            try:
                template = UserDocumentTemplate.objects.get(reference_id=reference_id)
            except UserDocumentTemplate.DoesNotExist:
                return api_response(
                    action="data_not_retrieved", message=_("User document template not found")
                )

            serializer = UserDocumentTemplateSerializer(
                template, context={"request": request}
            )
            return api_response(
                action="data_retrieved", data=serializer.data, status="success"
            )

        except Exception as e:
            return api_response(action="data_not_retrieved", message=str(e))

    def put(self, request, reference_id):
        try:

            try:
                template = UserDocumentTemplate.objects.get(reference_id=reference_id)
            except UserDocumentTemplate.DoesNotExist:
                return api_response(
                    action="data_not_retrieved", message=_("User document template not found")
                )

            data = request.data.copy()
            data["updated_by"] = request.user.reference_id

            serializer = UserDocumentTemplateSerializer(
                template, data=data, context={"request": request}, partial=True
            )
            if serializer.is_valid():
                serializer.save()

                # Log the template update action
                log_user_action(
                    request=request,
                    user=request.user,
                    action='update_user_document_template',
                    status='success',
                    details={'template_id': reference_id,
                             'updated_fields': serializer.validated_data}
                )

                return api_response(
                    action="data_updated",
                    data=serializer.data,
                    status="success"
                )
            return api_response(action="validation_error", message=serializer.errors)

        except Exception as e:
            return api_response(action="data_not_updated", message=str(e))

    def delete(self, request, reference_id):
        try:

            try:
                template = UserDocumentTemplate.objects.get(reference_id=reference_id)
            except UserDocumentTemplate.DoesNotExist:
                return api_response(
                    action="data_not_retrieved", message=_("User document template not found")
                )

            # Soft delete by setting active to False
            template.is_active = False
            template.updated_by = request.user.reference_id
            template.save()

            # Log the template deletion action
            log_user_action(
                request=request,
                user=request.user,
                action='delete_user_document_template',
                status='success',
                details={'template_id': reference_id}
            )

            return api_response(
                action="data_deleted",
                message=_("User document template successfully deactivated"),
                status="success",
            )

        except Exception as e:
            return api_response(action="data_not_deleted", message=str(e))


class SystemDocumentTemplateDetailView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, reference_id):
        try:

            try:
                template = DocumentTemplate.objects.get(
                    reference_id=reference_id, is_active=True)
            except DocumentTemplate.DoesNotExist:
                return api_response(
                    action="data_not_retrieved", message=_("System document template not found")
                )

            serializer = DocumentTemplateSerializer(
                template, context={"request": request}
            )
            return api_response(
                action="data_retrieved", data=serializer.data, status="success"
            )

        except Exception as e:
            return api_response(action="data_not_retrieved", message=str(e))
