import base64
import copy
import datetime
import json
import os
import uuid
from datetime import timed<PERSON><PERSON>
from urllib.parse import unquote, urlparse

from django.shortcuts import get_object_or_404
from rest_framework.request import Request

import boto3
from botocore.config import Config
from botocore.exceptions import ClientError
from cryptography.fernet import Fernet
from django.template.loader import render_to_string
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.db import IntegrityError, transaction
from django.db.models import Count, Q
from django.http import Http404, HttpRequest
from django.utils import timezone
from django.utils.timezone import now
from django.utils.translation import gettext as _
from django_filters.rest_framework import DjangoFilterBackend
from dotenv import load_dotenv
from rest_framework import filters as rest_filters
from rest_framework import generics
from rest_framework import serializers
from rest_framework.exceptions import NotFound, ValidationError
from rest_framework.permissions import Is<PERSON><PERSON><PERSON>icated, AllowAny
from rest_framework.response import Response
from rest_framework.throttling import AnonRateThrottle
from rest_framework.views import APIView
from weasyprint import HTML

from accounts.models.organisation import Organization
from accounts.models.otp import OneTimePassword
from accounts.models.users import User
from accounts.services.bankid_authentication.bankid_utils import (
    get_client_ip,
)
from accounts.services.jwt_authentication.authentication import (
    generate_hmac_token,
)
from accounts.services.mobile_authentication.mobile_clients import (
    # send_document_completed_via_sms,
    send_otp_via_sms,
    # send_reminder_link_via_sms
)
from accounts.tasks import send_background_email
from accounts.views.email_auth_views import save_user_details
from ai_documents_app.services.pdf_conversion_service import PDFConversionService
from ai_documents_app.services.thumbnail_generator import ThumbnailGenerator
from documents.models import (
    ActivityLog,
    DocumentSigner,
    OrganizationDocument,
    UserDocument, DirectoryDocument,
)
from documents.models.document_models import DocumentVerificationData
from documents.serializers.document_serializers import (
    ActivityLogSerializer,
    DocumentPreiviewViewSerializer,
    DocumentPreviewSerializer,
    DocumentSignerPreviewSerializer,
    DocumentSignerSerializer,
    FlattenedDocumentSerializer,
    OrganizationDocumentSerializer,
    UserDocumentSerializer,
)
from documents.tasks import send_document_for_digital_sign
from documents.utils.jwt_verifier import (
    JWTAccessTokenAuthentication,
)
from documents.utils.services import generate_one_time_token, get_file_extension
from esign.utils.custom_pagination import CustomPagination
from esign.utils.custom_response import api_response
from subscriptions.services.subscription_feature_service import (
    SubscriptionFeatureManager,
)
from utils_app.logger_utils import log_user_action
import hashlib
import logging
from django.conf import settings
from documents.services.verification_service import DocumentVerificationService
from rest_framework.exceptions import ErrorDetail

load_dotenv()


def get_error_messages(errors):
    # Recursively extract the first string error message
    if isinstance(errors, dict):
        for value in errors.values():
            msg = get_error_messages(value)
            if msg:
                return msg
    elif isinstance(errors, list):
        for item in errors:
            msg = get_error_messages(item)
            if msg:
                return msg
    elif isinstance(errors, str):
        return errors
    elif hasattr(errors, 'detail'):
        return str(errors.detail)
    else:
        return str(errors)
    return "Unknown validation error"


# Encryption setup
cipher_suite = Fernet(settings.ENCRYPTION_KEY)
kms_key_id = os.getenv("KMS_kEY_ID")
s3_key_id = os.getenv("ACCESS_KEY")
s3_bucket_name = os.getenv("BUCKET_NAME")
secret_key = os.getenv("SECRET")
region = os.getenv("REGION")

logger = logging.getLogger("app")
# Initialize the S3 client and KMS
try:
    logger.info("Initializing S3 client...")
    s3_client = boto3.client(
        "s3",
        aws_access_key_id=s3_key_id,
        aws_secret_access_key=secret_key,
        region_name=region,
        config=Config(
            signature_version='s3v4',
            s3={'addressing_style': 'virtual'},
            retries={'max_attempts': 3}
        )
    )
    # Test the connection
    s3_client.list_buckets()
    logger.info("S3 client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize S3 client: {str(e)}", exc_info=True)
    raise

try:
    logger.info("Initializing KMS client...")
    kms_client = boto3.client(
        "kms",
        aws_access_key_id=s3_key_id,
        aws_secret_access_key=secret_key,
        region_name=region,
    )
    logger.info("KMS client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize KMS client: {str(e)}", exc_info=True)
    raise


def determine_access_from_device(device_data):
    """
    Determine access type based solely on the device field.

    :param device_data: dict containing device information
    :return: str, 'phone_number' or 'email'
    """
    device_type = device_data.get("device", "").lower()

    if "phone" in device_type or "smartphone" in device_type:
        return "phone_number"
    elif "laptop" in device_type or "desktop" in device_type or "tablet" in device_type:
        return "email"
    else:
        return None


def document_terminate(queryset):
    queryset = queryset.filter(
        user_document__is_deleted=False
    ).distinct()  # Assuming 'awaiting_signatures' is your status
    # Filter to ensure all signers have status "approved"
    queryset = queryset.annotate(
        signed_signers_count=Count(
            "user_document__signers", filter=Q(user_document__signers__status="signed")
        )
    ).filter(signed_signers_count=Count("user_document__signers"))
    if queryset:
        return True
    else:
        return False


def document_withdraw(queryset):
    queryset = queryset.filter(
        user_document__status="sent", user_document__is_deleted=False
    ).distinct()
    if queryset:
        return True
    else:
        return False


def check_is_verified(email=None, phone_number=None):
    if email:
        hmac_email = generate_hmac_token(email)
        otp_obj = OneTimePassword.objects.filter(email=hmac_email).first()
        if otp_obj:
            return otp_obj.is_download, "email", otp_obj
        else:
            return False, "", None
    if phone_number:
        hmac_number = generate_hmac_token(phone_number)
        otp_obj = OneTimePassword.objects.filter(phone_number=hmac_number).first()
        if otp_obj:
            return otp_obj.is_download, "phone", otp_obj
        else:
            return False, "", None


def decrpyt_from_key(s3_data, final_str_datetime):
    logger.info(
        f"Starting decryption process for data with datetime: {final_str_datetime}"
    )
    hashed_key = hashlib.sha256(final_str_datetime.encode()).digest()
    logger.debug(f"Generated hashed key for" f" decryption: {hashed_key.hex()}")
    # Ensure it's URL-safe and 32 bytes
    fernet_key = base64.urlsafe_b64encode(hashed_key)

    file_cipher_suite = Fernet(fernet_key)
    logger.debug("Fernet cipher suite " "initialized with generated key.")
    decrypted_data = file_cipher_suite.decrypt(s3_data)
    logger.debug("S3 data decrypted successfully.")

    data_updated = cipher_suite.decrypt(decrypted_data)
    logger.debug("Final decryption step completed.")
    # Optionally, save the file locally for inspection (for debugging)
    # with open('downloaded_encrypted_file.bin', 'wb') as f:
    #     f.write(data_updated)
    logger.info("Decryption process completed successfully.")
    return data_updated


def extract_s3_key(s3_url):
    """
    Extract the correct S3 key from various URL formats
    """
    logger.info(f"Extracting S3 key from URL: {s3_url}")
    try:
        decoded_url = unquote(s3_url)

        # Handle s3:// protocol
        if decoded_url.startswith("s3://"):
            parts = decoded_url.replace("s3://", "").split("/", 1)
            return parts[1] if len(parts) > 1 else ""

        # Handle https:// protocol
        if decoded_url.startswith("https://"):
            parsed_url = urlparse(decoded_url)
            path = parsed_url.path
            # Remove leading slash if present
            return path.lstrip("/")

        # Handle /media/ prefix
        if decoded_url.startswith("/media/"):
            # Extract the actual key after any S3 URL components
            parts = decoded_url.split("esign-docs.s3.amazonaws.com/")
            if len(parts) > 1:
                return parts[1]
            return decoded_url.replace("/media/", "")

        # Handle /documents/ prefix
        if decoded_url.startswith('/documents/') or decoded_url.startswith('documents/'):
            return decoded_url.split('documents/')[1]

        # If no special prefix, return as is
        return decoded_url
    except Exception as e:
        logger.error(f"Error extracting S3 key from URL {s3_url}: {str(e)}")
        raise ValueError(f"Invalid S3 URL format: {s3_url}")


def decrypt_file(encrypted_data, encrypted_key):
    """
    Decrypt a file using envelope encryption

    :param encrypted_data: The encrypted file data
    :param encrypted_key: The encrypted data key
    :return: The decrypted file data

    """
    logger.info("Starting the file decryption process.")
    try:
        # Decrypt the data key using KMS
        logger.debug("Decrypting the data key using KMS.")
        response = kms_client.decrypt(CiphertextBlob=encrypted_key)
        data_key = response["Plaintext"]
        logger.debug("Data key decrypted successfully.")

        # Convert the binary key to base64 string as required by Fernet
        key_base64 = base64.b64encode(data_key)
        logger.debug("Converted the data key to base64 format for Fernet.")
        f = Fernet(key_base64)

        # Decrypt the file data
        logger.debug("Decrypting the file data using Fernet.")
        decrypted_data = f.decrypt(encrypted_data)
        logger.info("File decryption successful.")
        return decrypted_data

    except Exception as e:
        logger.error(f"Error occurred in decrypt_file: {str(e)}")
        print(f"Error in decrypt_file: {str(e)}")
        raise


def download_and_decrypt_from_s3(s3_url, final_str_datetime):
    """
    Download and decrypt a file from S3 using envelope encryption.
    Retrieves the encrypted key from metadata.

    :param s3_url: The S3 URL of the file to download and decrypt
    :return: The decrypted file data
    """
    logger.info("Starting the process of downloading and decrypting file from S3.")
    try:
        # Extract the key from the S3 URL
        logger.debug("Extracting S3 key from the URL.")
        key = extract_s3_key(s3_url)
        logger.debug(f"Extracted S3 key: {key}")

        # Verify the key is not empty
        if not key:
            raise ValueError("Empty S3 key extracted from URL")

        logger.debug(f"Fetching object from S3 bucket {s3_bucket_name} with key {key}.")

        try:
            # Get the object and its metadata
            response = s3_client.get_object(
                Bucket=s3_bucket_name,
                Key=key
            )
        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_message = e.response['Error']['Message']
            logger.error(f"S3 ClientError: {error_code} - {error_message}")
            if error_code == 'NoSuchKey':
                raise ValueError(f"File not found in S3: {key}")
            elif error_code == 'AccessDenied':
                raise ValueError(f"Access denied to S3 bucket: {s3_bucket_name}")
            else:
                raise

        # Get the encrypted data and encrypted key
        try:
            encrypted_data = response["Body"].read()
            if not encrypted_data:
                raise ValueError("Empty file data received from S3")
        except Exception as e:
            logger.error(f"Error reading file data from S3: {str(e)}")
            raise ValueError("Failed to read file data from S3")

        encrypted_key_b64 = response["Metadata"].get("encrypted_key")
        if not encrypted_key_b64:
            logger.error("No encrypted key found in metadata.")
            raise ValueError("No encrypted key found in metadata")

        # Decode the encrypted key from base64
        try:
            logger.debug("Decoding the encrypted key from base64.")
            encrypted_key = base64.b64decode(encrypted_key_b64)
        except Exception as e:
            logger.error(f"Error decoding encrypted key: {str(e)}")
            raise ValueError("Invalid encrypted key format")

        # Decrypt the file
        try:
            logger.debug("Decrypting the file using the decrypted key.")
            decrypted_data = decrypt_file(encrypted_data, encrypted_key)
        except Exception as e:
            logger.error(f"Error in first decryption step: {str(e)}")
            raise ValueError("Failed to decrypt file with KMS key")

        try:
            logger.debug(
                f"""Decrypting the file further
                 using final_str_datetime: {final_str_datetime}."""
            )
            decrypted_data_second = decrpyt_from_key(decrypted_data, final_str_datetime)
        except Exception as e:
            logger.error(f"Error in second decryption step: {str(e)}")
            raise ValueError("Failed to decrypt file with datetime key")

        logger.info("File successfully downloaded and decrypted.")
        return decrypted_data_second

    except Exception as e:
        logger.error(f"Error in download_and_decrypt_from_s3: {str(e)}", exc_info=True)
        raise


def generate_data_key():
    """
    Generate a new data key using KMS
    """
    logger.info("Starting the process of generating a new data key using KMS.")
    response = kms_client.generate_data_key(KeyId=kms_key_id, KeySpec="AES_256")
    logger.info("Data key successfully generated.")
    return {
        "plaintext": response["Plaintext"],
        "ciphertext": response["CiphertextBlob"],
    }


def encrypt_file(file_data):
    """
    Encrypt a file using envelope encryption

    :param file_data: The raw file data to encrypt
    :return: dict containing encrypted data and encrypted key
    """
    logger.info("Starting the file encryption process using envelope encryption.")
    try:
        # Generate a new data key
        logger.debug("Generating a new data key for encryption.")
        data_key = generate_data_key()

        # Create a Fernet instance with the plaintext data key
        # Convert the binary key to base64 string as required by Fernet
        key_base64 = base64.b64encode(data_key["plaintext"])
        f = Fernet(key_base64)

        # Encrypt the file data
        logger.debug("Encrypting the file data using the generated key.")
        encrypted_data = f.encrypt(file_data)
        logger.info("File encryption successful.")
        # Return both the encrypted data and the encrypted key
        return {
            "encrypted_data": encrypted_data,
            "encrypted_key": data_key["ciphertext"],
        }
    except Exception as e:
        logger.error(f"Error in encrypt_file: {str(e)}")
        print(f"Error in encrypt_file: {str(e)}")
        raise


def encrypt_and_upload_to_s3(file_data, file_name):
    """
    Encrypt file using envelope encryption and upload to S3.
    Stores the encrypted data key as metadata.

    :param file_data: The raw file data to encrypt and upload
    :param file_name: The file name to save as in S3
    :return: tuple(s3_url, encrypted_key)
    """
    logger.info(f"Starting encryption and upload process for file: {file_name}")
    try:
        # First, encrypt the file using envelope encryption
        logger.debug(f"Encrypting the file data for {file_name}")
        encryption_result = encrypt_file(file_data)
        encrypted_data = encryption_result["encrypted_data"]
        encrypted_key = encryption_result["encrypted_key"]

        # Convert encrypted key to base64 for metadata storage
        encrypted_key_b64 = base64.b64encode(encrypted_key).decode("utf-8")

        # Upload to S3 with the encrypted key in metadata
        logger.debug(f"Uploading encrypted file to S3 with key: {file_name}")
        try:
            response = s3_client.put_object(
                Bucket=s3_bucket_name,
                Key=file_name,
                Body=encrypted_data,
                Metadata={"encrypted_key": encrypted_key_b64},
                ServerSideEncryption='AES256'
            )
            logger.info(f"S3 upload response: {response}")
        except Exception as e:
            logger.error(f"Failed to upload file to S3: {str(e)}", exc_info=True)
            raise

        # Generate the S3 URL
        s3_url = f"https://{s3_bucket_name}.s3.{region}.amazonaws.com/{file_name}"
        logger.info(f"File successfully uploaded to S3: {s3_url}")

        # Verify the upload
        try:
            s3_client.head_object(Bucket=s3_bucket_name, Key=file_name)
            logger.info("Successfully verified file upload to S3")
        except Exception as e:
            logger.error(f"Failed to verify file upload: {str(e)}", exc_info=True)
            raise

        return s3_url, encrypted_key

    except Exception as e:
        logger.error(
            f"Error in encrypt_and_upload_to_s3 for file {file_name}: {str(e)}",
            exc_info=True
        )
        raise


def get_document_or_404(reference_id):
    try:
        logger.info(
            f"Attempting to retrieve document with reference_id: {reference_id}"
        )
        document = UserDocument.objects.get(reference_id=reference_id)
        logger.info(f"Document found: {document}")
        return document
    except UserDocument.DoesNotExist:
        logger.warning(f"Document not found with reference_id: {reference_id}")
        return api_response(
            action="data_not_retrieved", message=_("Document not found.")
        )


def get_signer_or_404(reference_signer_id, document):
    try:
        logger.info(
            f"""Attempting to retrieve signer with reference_signer_id:
            {reference_signer_id} for document: {document}"""
        )
        signer = DocumentSigner.objects.get(
            reference_signer_id=reference_signer_id, document=document
        )
        logger.info(f"Signer found: {signer.id}")
        return signer
    except DocumentSigner.DoesNotExist:
        logger.warning("Signer not found with reference_signer_id for document")
        return api_response(action="data_not_retrieved", message=_("Signer not found."))


def filter_by_email(plain_email, document):
    logger.info(
        f"Filtering signers for document {document.id} with email: {plain_email}"
    )
    signers = DocumentSigner.objects.filter(document=document)
    resp_list = list()
    for signer in signers:
        decrypted_email = cipher_suite.decrypt(signer.email.encode()).decode()
        logger.debug(f"Decrypted email for signer {signer.id}: {decrypted_email}")
        if decrypted_email == plain_email:
            resp_list.append(decrypted_email)
        # return api_response(
        #     action="data_not_updated",
        #     message=_("This email address is already assigned to this document"),
        # )
    if not resp_list:
        logger.warning(
            f"""No signers found with email: {plain_email}.
            Setting document status to 'draft'."""
        )
        document.status = "draft"
        document.save()
    return resp_list


def update_document_status_if_all_approved(document_id):
    logger.info(f"Checking approval status for all signers of document {document_id}")
    user_document = UserDocument.objects.prefetch_related("signers").get(id=document_id)
    signers = user_document.signers.all()

    # Check if all signers have the status "approved"
    if all(signer.status == "signed" for signer in signers):
        # Change the document status to "sent"
        logger.info(
            f"""All signers have approved the document {document_id}.
              Changing document status to 'sent'."""
        )
        user_document.status = "sent"
        user_document.save()

        # Update proposal status if the document is linked to a proposal
        if hasattr(user_document, 'proposal') and user_document.proposal:
            try:
                proposal = user_document.proposal
                proposal.proposal_status = "sent"
                proposal.save()
                logger.info(
                    f"Updated proposal status to 'sent' for proposal {proposal.proposal_id}",
                    extra={
                        'proposal_id': proposal.proposal_id,
                        'document_id': str(user_document.id),
                        'proposal_title': proposal.proposal_title
                    }
                )
            except Exception as e:
                logger.error(
                    f"Error updating proposal status: {str(e)}",
                    extra={
                        'proposal_id': getattr(proposal, 'proposal_id', 'unknown'),
                        'document_id': str(user_document.id),
                        'error': str(e)
                    },
                    exc_info=True
                )

        print("Document status changed to 'sent'.")
    else:
        logger.warning(f"Not all signers have approved the document {document_id}.")
        print("Not all signers have approved the document.")


def check_all_status_send_mail(signer=None, ip_address=None, organization_doc_reference_id=None, user_id=None, document=None, device_data=None):
    """
    Checks if all signers have signed the document. If so, sends email and SMS notifications
    to signers and the document owner.  Logs activity.
    Considers the case when a signer is also the document owner, sending only one notification in that case.
    Ensures the owner only gets notified once, even within the signers loop.
    """

    messages = []  # Messages for signer-related activity log
    messages_user = []  # Messages for document owner-related activity log

    # Always use the document argument for signers
    signers = DocumentSigner.objects.filter(document=document)
    all_signed = all(s.status == "signed" for s in signers)

    # Check if the document owner is also a signer
    is_owner_also_signer = False
    if document.created_by and document.created_by.email:
        is_owner_also_signer = any(
            signer.get_decrypted_email() == document.created_by.get_decrypted_email() for signer in signers
        )

    owner_notified = False  # Flag to track if the owner has already been notified

    if all_signed:
        # Update proposal status if the document is linked to a proposal
        if hasattr(document, 'proposal') and document.proposal:
            try:
                proposal = document.proposal
                proposal.proposal_status = "signed"
                proposal.save()
                logger.info(
                    f"Updated proposal status to 'sent' for proposal {proposal.proposal_id}",
                    extra={
                        'proposal_id': proposal.proposal_id,
                        'document_id': str(document.id),
                        'proposal_title': proposal.proposal_title
                    }
                )
            except Exception as e:
                logger.error(
                    f"Error updating proposal status: {str(e)}",
                    extra={
                        'proposal_id': getattr(proposal, 'proposal_id', 'unknown'),
                        'document_id': str(document.id),
                        'error': str(e)
                    },
                    exc_info=True
                )

        # Only now, after all signers have signed, set is_agreement = True if not already set
        if not document.is_agreement:
            document.is_agreement = True
            document.save(update_fields=["is_agreement"])

        # Create a document info dictionary with necessary fields
        document_info = {
            "id": document.id,
            "reference_id": document.reference_id,
            "title": document.get_decrypted_title()
            if hasattr(document, "get_decrypted_title")
            else "",
            "is_agreement": document.is_agreement,
            "agreement_start_date": document.agreement_start_date,
            "agreement_end_date": document.agreement_end_date,
            # Add any other necessary document fields
        }

        pdf_attachment = None

        # Iterate through signers to send notifications
        for signer in signers:
            # Prepare messages for the activity log.
            feature_manager = SubscriptionFeatureManager(document.created_by)
            messages = []
            if signer.signer_type == "sweden_bank_id":
                result = feature_manager.update_feature_usage(
                    feature_key="bankid_signs")
            else:
                result = feature_manager.update_feature_usage(feature_key="esigns")

            # Send email if available
            if signer.email:
                send_background_email.delay(
                    email=signer.get_decrypted_email(),
                    receiver_name=signer.get_decrypted_name(),
                    signer_name=signer.get_decrypted_name(),
                    reference_signer_id=signer.reference_signer_id,
                    org_doc_reference_id=organization_doc_reference_id,
                    user_id=user_id,
                    document=document_info,  # Pass the dictionary instead of document object
                    status="approved",
                    pdf_attachment=pdf_attachment,
                )
                messages.append(f"Sent email to ({signer.get_decrypted_email()})")

            # Send SMS if available
            # if signer.phone_number:
            #     send_document_completed_via_sms(
            #         receiver_name=signer.get_decrypted_name(),
            #         reference_signer_id=signer.reference_signer_id,
            #         org_doc_reference_id=organization_doc_reference_id,
            #         user_id=user_id,
            #         document=document_info,  # Pass the dictionary here too
            #         phone_number=signer.get_decrypted_phone_number(),
            #     )
            #     messages.append(f"Sent SMS to ({signer.get_decrypted_phone_number()})")

            message_str = " and ".join(messages)

            # Create ActivityLog entry for each signer.
            ActivityLog.objects.create(
                document_refrence_id=signer.document.reference_id,
                event_type="Completed",
                title=signer.document.title,
                email=signer.email if signer.email else None,
                ip_address=ip_address if ip_address else None,
                phone_number=signer.phone_number if signer.phone_number else None,
                device_data=device_data,
                category="Document Completed",
                # More descriptive message
                message=f"Document Completed: {message_str}",
            )

            # Send notification to document owner if NOT a signer AND not already notified
            if document.created_by.email and not is_owner_also_signer and not owner_notified:
                messages_user = []
                if document.created_by.first_name and document.created_by.last_name:
                    receiver_name = (
                        f"{document.created_by.first_name} {document.created_by.last_name}"
                    )
                else:
                    receiver_name = document.created_by.get_decrypted_email()

                messages_user.append(
                    f"Sent email to ({document.created_by.get_decrypted_email()})"
                )
                send_background_email.delay(
                    email=document.created_by.get_decrypted_email(),
                    receiver_name=receiver_name,
                    signer_name=signer.get_decrypted_name(),
                    reference_signer_id=signer.reference_signer_id,
                    org_doc_reference_id=organization_doc_reference_id,
                    user_id=user_id,
                    document=document_info,  # Pass the dictionary here as well
                    status="approved_owner",
                    pdf_attachment=pdf_attachment,
                )

                # if document.created_by.phone_number and not is_owner_also_signer and not owner_notified:
                #     send_document_completed_via_sms(
                #         receiver_name=document.created_by.first_name,  # Use name if available
                #         reference_signer_id=signer.reference_signer_id,
                #         org_doc_reference_id=organization_doc_reference_id,
                #         user_id=user_id,
                #         document=document_info,  # Pass the dictionary here too
                #         phone_number=document.created_by.get_decrypted_phone_number(),
                #         status="approved_owner",
                #     )
                #     messages_user.append(
                #         f"Sent SMS to ({document.created_by.get_decrypted_phone_number()})"
                #     )

                message_str_user = " and ".join(messages_user)

                # Log the activity for document owner
                ActivityLog.objects.create(
                    document_refrence_id=document.reference_id,
                    event_type="Completed",
                    title=document.title,
                    email=document.created_by.email,
                    phone_number=(
                        document.created_by.phone_number
                        if document.created_by.phone_number
                        else None
                    ),
                    ip_address=ip_address if ip_address else None,
                    device_data=device_data,
                    category="Document Completed",
                    # More descriptive message
                    message=f"Document Completed and Sealed: {message_str_user}",
                )

                owner_notified = True  # Set flag so the owner isn't notified again.

    return True, {}


def send_document_delete_mail(document_id, status):
    document_obj = UserDocument.objects.filter(reference_id=document_id).first()
    signers = DocumentSigner.objects.filter(document=document_obj)

    # TODO: If required, send the email to the owner of the document too.
    for signer in signers:
        send_background_email.delay(
            email=signer.get_decrypted_email(),
            document=signer.document.reference_id,
            status=status,
            receiver_name=signer.get_decrypted_name()
        )

    if document_obj.created_by.get_decrypted_email():
        send_background_email.delay(
            email=document_obj.created_by.get_decrypted_email(),
            document=document_obj.reference_id,
            status=status,
            receiver_name=document_obj.created_by.get_full_name()
        )
    return True


def update_document_status_as_approved(signer, ip_address, device_data=None):
    try:
        # Check if the signer object is valid
        if not signer or not hasattr(signer, "document") or not signer.document:
            return api_response(
                action="validation_error",
                message="Invalid signer or missing document attribute.",
            )
        # Log that the function was called with the signer details
        logger.info(
            f"Updating document status as approved for signer: {signer.email}, document: {signer.document.title}"
        )
        if device_data:
            source_of_access = determine_access_from_device(device_data)
            if source_of_access == "phone_number":
                message = f"Document Signed by {signer.get_decrypted_phone_number()} with phone number."
            elif source_of_access == "email":
                message = (
                    f"Document Signed by {signer.get_decrypted_email()} with email."
                )
            else:
                message = None

        # Create the ActivityLog entry
        ActivityLog.objects.create(
            document_refrence_id=signer.document.reference_id,
            event_type="Approved",
            title=signer.document.title,
            email=signer.email,
            ip_address=(ip_address if ip_address else None),
            phone_number=signer.phone_number if signer.phone_number else None,
            signer_type=signer.signer_type,
            category="Document Signed",
            device_data=device_data,
            message=message,
        )

        # Log that the ActivityLog was successfully created
        logger.info(
            f"ActivityLog created for document: {signer.document.title} with reference ID: {signer.document.reference_id}"
        )

    except ValidationError as e:
        # Log validation error
        return api_response(action="validation_error", message="Validation error")
    except Exception as e:
        # Log any unexpected errors
        return api_response(
            action="server_error", message="Unexpected error while approving documents"
        )


def update_document_password(user, document, update=False):
    if update:
        document_file = document.latest_document_file
    else:
        document_file = document.document_file
    logger.info(f"Starting password update for document {document.id} and user {user}")
    str_date_joined = user.date_joined.isoformat()
    str_created_obj = document.created_at.isoformat()
    final_str_datetime = str_date_joined + "/" + str_created_obj
    logger.debug(
        f"Generated final datetime string for encryption: {final_str_datetime}"
    )
    print(f"final_str_datetime {final_str_datetime}")
    old_file = str(document_file)
    hashed_key = hashlib.sha256(final_str_datetime.encode()).digest()
    logger.debug(f"Generated SHA-256 hash for Fernet key: {hashed_key.hex()}")
    # Ensure it's URL-safe and 32 bytes
    fernet_key = base64.urlsafe_b64encode(hashed_key)
    logger.debug(f"Generated Fernet key (base64 URL-safe): {fernet_key.decode()}")

    file_cipher_suite = Fernet(fernet_key)
    encrypted_data = file_cipher_suite.encrypt(document_file.read())
    logger.info(f"Encrypted the file {old_file} successfully")

    filename = str(document_file.name)
    new_file_name = get_file_extension(filename)
    # new_file_name = f"{new_file_name}_{datetime.datetime.now().isoformat()}"
    logger.info(f"Generated new file name for encrypted file: {new_file_name}")

    # Encrypt file using AWS S3 + KMS (3rd level encryption)
    encrypted_file_path, _ = encrypt_and_upload_to_s3(encrypted_data, new_file_name)
    logger.info(f"Uploaded the encrypted file to S3 at path: {encrypted_file_path}")

    # os.remove(os.path.join(settings.BASE_DIR, "media", old_file))
    # # document.document_file = ContentFile(encrypted_data, new_file_name)
    # logger.info(f"Removed the old file {old_file} from local storage")

    # Update the document instance field
    if update:
        document.latest_document_file = encrypted_file_path
    else:
        document.document_file = encrypted_file_path
    # encrypt_password = fernet_encrypt(final_str_datetime).decode()
    document.document_password = fernet_key.decode()
    document.save()
    logger.info(f"Document {document.id} password and file updated successfully")
    return document


def update_html_password(user, document, update=False):
    """
    Update HTML file password and upload to S3 with user-specific encryption.
    Similar to update_document_password but for HTML files.
    """
    if update:
        html_file = document.latest_html_file
    else:
        html_file = document.html_file

    if not html_file:
        logger.warning(f"No HTML file found for document {document.id}")
        return document

    logger.info(
        f"Starting HTML password update for document {document.id} and user {user}")
    str_date_joined = user.date_joined.isoformat()
    str_created_obj = document.created_at.isoformat()
    final_str_datetime = str_date_joined + "/" + str_created_obj
    logger.debug(
        f"Generated final datetime string for HTML encryption: {final_str_datetime}"
    )

    old_file = str(html_file)
    hashed_key = hashlib.sha256(final_str_datetime.encode()).digest()
    logger.debug(f"Generated SHA-256 hash for HTML Fernet key: {hashed_key.hex()}")
    # Ensure it's URL-safe and 32 bytes
    fernet_key = base64.urlsafe_b64encode(hashed_key)
    logger.debug(f"Generated HTML Fernet key (base64 URL-safe): {fernet_key.decode()}")

    file_cipher_suite = Fernet(fernet_key)
    encrypted_data = file_cipher_suite.encrypt(html_file.read())
    logger.info(f"Encrypted the HTML file {old_file} successfully")

    filename = str(html_file.name)
    new_file_name = get_file_extension(filename)
    logger.info(f"Generated new file name for encrypted HTML file: {new_file_name}")

    # Encrypt file using AWS S3 + KMS (3rd level encryption)
    encrypted_file_path, _ = encrypt_and_upload_to_s3(encrypted_data, new_file_name)
    logger.info(
        f"Uploaded the encrypted HTML file to S3 at path: {encrypted_file_path}")

    # Update the document instance field
    if update:
        document.latest_html_file = encrypted_file_path
    else:
        document.html_file = encrypted_file_path

    # Store HTML password separately
    document.html_password = fernet_key.decode()
    document.save()
    logger.info(f"Document {document.id} HTML password and file updated successfully")
    return document


def check_document_status(user_document: UserDocument):
    """
    Checks the status of a user document to determine if it is available for actions such as signing.

    Args:
        user_document (UserDocument): The user document instance to check.

    Returns:
        tuple: (bool, str) where the boolean indicates if the document is available,
               and the string provides a message if not available.

    Notes:
        - If the document is deleted and withdrawn, returns a withdrawn message.
        - If the document is deleted but not withdrawn, returns a deleted message.
        - Otherwise, returns True and an empty message.
    """
    # Suggestion: A more descriptive method name could be `get_document_availability_status`
    if user_document.is_deleted:
        if user_document.status == "withdrawn":
            return False, 'This document has been withdrawn by the sender and cannot be signed.'
        else:
            return False, 'This document does not exist and has been deleted.'

    current_time = timezone.now()
    agreement_end_date = user_document.agreement_end_date
    if user_document.is_agreement and agreement_end_date and agreement_end_date <= current_time:
        return False, "This agreement has expired and can no longer be previewed."

    return True, ''


class UserDocumentListCreateAPIView(generics.ListCreateAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = UserDocumentSerializer

    # Add filter backends for search and filtering
    filter_backends = [DjangoFilterBackend, rest_filters.OrderingFilter]
    ordering_fields = ["created_at"]
    # Configure pagination
    pagination_class = CustomPagination

    # def get_encrypted_title(self, search_term):
    #     try:
    #         logger.debug(f"Encrypted search term: {search_term} -> {search_term}")
    #         return cipher_suite.encrypt(search_term.encode()).decode()
    #     except Exception as e:
    #         logger.error(f"Error encrypting search term {search_term}: {str(e)}")
    #         return search_term

    def get_queryset(self):
        document_organization_reference_id = self.request.query_params.get(
            "document_organisation_reference_id"
        )

        logger.info("Fetching documents for user...")

        # Query OrganizationDocument model filtered by the provided reference ID
        queryset = OrganizationDocument.objects.all()

        if not document_organization_reference_id:
            return api_response(
                action="data_not_retrieved",
                message="document_organization_reference_id is not there",
            )

        queryset = queryset.filter(
            encrypted_reference_id=document_organization_reference_id
        )

        # elif document_organisation_reference_id is None:
        #     queryset = queryset.filter(organization__name="General")

        search_term = self.request.query_params.get("search", None)
        status = self.request.query_params.get("status", None)
        if search_term or status:
            logger.info(f"Search term provided: {search_term}")
            matching_docs = []
            # Decrypt and search within related UserDocument titles
            for org_doc in queryset:
                try:
                    # Access the related UserDocument
                    user_document = org_doc.user_document

                    # Decrypt the title
                    try:
                        decrypted_title = user_document.title
                    except Exception as e:
                        logger.warning(
                            f"""Error decrypting title for
                            UserDocument {user_document.id}: {str(e)}"""
                        )
                        continue

                    # Case-insensitive search for the decrypted title
                    if search_term:
                        if search_term.lower() in decrypted_title.lower():
                            matching_docs.append(user_document.id)
                            logger.info(
                                f"Document {user_document.id} matches the search term."
                            )

                    if status in user_document.status:
                        matching_docs.append(user_document.id)
                        logger.info(
                            f"Document {user_document.id} matches the status term."
                        )

                except Exception as e:
                    logger.error(
                        f"""Error processing document for UserDocument
                          {org_doc.user_document.id}: {e}"""
                    )

            # Filter queryset to matching documents
            queryset = queryset.filter(user_document__id__in=matching_docs)
            logger.info(
                f"Found {len(matching_docs)} documents matching the search term."
            )

            queryset = queryset.select_related("user_document")

        return queryset

    def create(self, request, *args, **kwargs):
        try:
            ip_address = get_client_ip(request)
            org_id = request.data.get("encrypted_reference_id")
            device = request.data.get("device_data", "")

            # Log S3 configuration
            logger.info("S3 Configuration Check:")
            logger.info(
                f"AWS_ACCESS_KEY_ID: {'Set' if os.getenv('ACCESS_KEY') else 'Not Set'}")
            logger.info(
                f"AWS_SECRET_ACCESS_KEY: {'Set' if os.getenv('SECRET') else 'Not Set'}")
            logger.info(f"AWS_STORAGE_BUCKET_NAME: {os.getenv('BUCKET_NAME')}")
            logger.info(f"AWS_S3_REGION_NAME: {os.getenv('REGION')}")

            # Simple feature check
            feature_manager = SubscriptionFeatureManager(request.user)
            agreement_info = feature_manager.check_feature_availability("agreements")

            if not agreement_info.get("unlimited") and not agreement_info.get("available"):
                return api_response(
                    action="validation_error",
                    message=agreement_info.get("message"),
                    data=agreement_info,
                )

            if device:
                device_data = json.loads(device)
            else:
                device_data = None

            if not org_id:
                logger.error(
                    "encrypted_reference_id (org_id) is missing in the request data."
                )
                return api_response(
                    action="validation_error",
                    message=_("encrypted_reference_id is required."),
                )

            # Ensure document file is provided
            if not request.data.get("document_file"):
                logger.warning("Document file is required but not provided.")
                return api_response(
                    action="validation_error",
                    message=_("Document file is required."),
                )
            # Ensure document file is provided
            if not request.data.get("latest_document_file"):
                logger.warning("Updated Document file is required but not provided.")
                return api_response(
                    action="validation_error",
                    message=_("Document file is required."),
                )

            # Add user to the validated data for serializer

            html_content = request.data.get("content")

            # Determine if content is AI generated based on presence of content
            ai_generated = bool(html_content and html_content.strip())

            data = copy.copy(request.data)
            data["user"] = request.user.id

            # Handle proposal_id for proposal documents
            proposal_id = request.data.get("proposal_id")

            # Only create HTML file if there's actual content
            if ai_generated:
                html_file = ContentFile(html_content.encode(
                    "utf-8"), name="document.html")
                data["html_file"] = html_file
                data["latest_html_file"] = html_file
            else:
                # Don't include HTML file fields if no content
                data.pop("html_file", None)
                data.pop("latest_html_file", None)

            # Ensure HTML file is provided only if ai_generated is True
            if ai_generated and not data.get("html_file"):
                logger.warning("HTML file is required but not provided.")
                return api_response(
                    action="validation_error",
                    message=_("HTML file is required."),
                )

            # Ensure latest HTML file is provided only if ai_generated is True
            if ai_generated and not data.get("latest_html_file"):
                logger.warning("Updated HTML file is required but not provided.")
                return api_response(
                    action="validation_error",
                    message=_("Updated HTML file is required."),
                )

            try:
                organization = Organization.objects.get(
                    reference_id=org_id, is_active=True, is_primary=True)
                serializer = self.get_serializer(
                    data=data, context={**self.get_serializer_context(), 'organization': organization, 'ai_generated': ai_generated})
                if serializer.is_valid():
                    self.perform_create(serializer)
                    edit_document = serializer.instance
                else:
                    logger.error(
                        f"Serializer validation failed: {serializer.errors}")
                    return api_response(
                        action="validation_error",
                        message=get_error_messages(serializer.errors),
                        data=serializer.errors,
                    )

                # Log document creation success
                logger.info(
                    f"Document created successfully with ID: {edit_document.id}")

            except ValueError as e:
                # Handle proposal linking errors
                logger.error(f"Proposal linking error: {str(e)}", exc_info=True)
                return api_response(
                    action="validation_error",
                    message=str(e),
                )
            except Exception as e:
                logger.error(f"Error during document creation: {str(e)}", exc_info=True)
                raise

            # Get the size of the 'document_file' in bytes
            try:
                document_file_size = edit_document.document_file.size
                latest_document_file_size = edit_document.file_size

                # Log file sizes
                logger.info(f"Document file size: {document_file_size} bytes")
                logger.info(
                    f"Latest document file size: {latest_document_file_size} bytes")

            except Exception as e:
                logger.error(f"Error getting file sizes: {str(e)}", exc_info=True)
                raise

            # Convert to MB
            document_file_size_mb = latest_document_file_size / (1024 * 1024)  # in MB
            storage_info = feature_manager.check_feature_availability(
                "storage")  # in MB

            if float(storage_info.get("remaining")) < float(document_file_size_mb):
                return api_response(
                    action="validation_error",
                    message="File Size Exceeds",
                    data=storage_info,
                )

            result = feature_manager.update_feature_usage(
                feature_key="storage", increment=document_file_size_mb)
            if not result.get("success"):
                return api_response(
                    action="validation_error",
                    message=result,
                    data=result,
                )

            # Log the document creation action
            log_user_action(
                request=request,
                user=request.user,
                action="document_creation",
                status="success",
                details={
                    "document_id": edit_document.id,
                    "document_title": edit_document.title,
                },
            )

            try:
                organization = Organization.objects.get(
                    reference_id=org_id, is_active=True, is_primary=True
                )
                # After creating the UserDocument, create an associated OrganizationDocument
                organization_document = OrganizationDocument.objects.create(
                    user_document=edit_document,
                    organization=organization,
                )
                logger.info(
                    f"Organization document created with ID: {organization_document.id}")

            except Exception as e:
                logger.error(
                    f"Error creating organization document: {str(e)}", exc_info=True)
                raise

            # Generate thumbnail for the document
            try:
                # Get the document file from S3
                document_file = edit_document.document_file

                if document_file:
                    thumbnail_generator = ThumbnailGenerator()
                    edit_document.thumbnail_url = thumbnail_generator.generate_pdf_thumbnail
                    logger.info("Thumbnail generation completed successfully")

            except Exception as e:
                logger.error(
                    f"Error generating thumbnail for document {edit_document.id}: {str(e)}",
                    exc_info=True
                )
                # Continue with document creation even if thumbnail generation fails

            ActivityLog.objects.create(
                document_refrence_id=edit_document.reference_id,
                event_type="Create",
                title=edit_document.title,
                device_data=device_data,
                ip_address=(ip_address if ip_address else None),
                category="Document Creation",
                message="Document was created.",
                user=edit_document.created_by.reference_id,
            )

            logger.info(
                f"Created document {edit_document.id} for user {request.user.reference_id}"
            )

            try:
                update_data = update_document_password(
                    edit_document.owner, edit_document)
                update_data_latest = update_document_password(
                    edit_document.owner, edit_document, update=True
                )
                logger.info(
                    f"Updated document {edit_document.id} PDF password after creation.")

                # Update HTML passwords
                if html_content:
                    update_html_password(
                        edit_document.owner, edit_document)
                    update_html_password(
                        edit_document.owner, edit_document, update=True
                    )
                logger.info(
                    f"Updated document {edit_document.id} HTML password after creation.")
            except Exception as e:
                logger.error(
                    f"Error updating document passwords: {str(e)}", exc_info=True)
                raise

            response_data = self.get_serializer(update_data).data
            response_data[
                "organization_document_encrypted_reference_id"
            ] = organization_document.encrypted_reference_id

            # Update count after successful creation
            result = feature_manager.update_feature_usage(feature_key="agreements")
            response_data["result"] = result
            response_data["limit_info"] = result

            if not result.get("success"):
                raise api_response(action="validation_error", message=result)

            return api_response(
                action="data_created",
                data=response_data,
                status="success",
                message="Document Created Successfully",
            )

        except Exception as e:
            logger.error(
                f"Unexpected error in document creation: {str(e)}", exc_info=True)
            return api_response(
                action="data_not_created",
                message=str(e),
                status=500
            )

    def patch(self, request, reference_id, *args, **kwargs):
        try:
            logger.info("PATCH request received. Updating existing document.")

            if not reference_id:
                return api_response(
                    action="data_not_retrieved",
                    message="document_reference_id is required",
                )

            # Resolve the target document strictly via document_reference_id
            user_document = UserDocument.objects.filter(
                reference_id=reference_id
            ).first()
            if not user_document:
                return api_response(
                    action="data_not_retrieved",
                    message="UserDocument not found for provided document_reference_id",
                )

            # Check if document is in "sent" status and has been signed by any signer
            if user_document.status == "sent":
                # Check if any signer has signed the document
                has_signed_signer = DocumentSigner.objects.filter(
                    document=user_document,
                    status="signed"
                ).exists()
                
                if has_signed_signer:
                    return api_response(
                        action="validation_error",
                        message="Cannot edit document. Document is in 'sent' status and has been signed by one or more signers.",
                        status=400
                    )

            # Try to fetch an organization for validate_title context (optional)
            org_doc = (
                OrganizationDocument.objects.filter(user_document=user_document)
                .select_related("organization")
                .first()
            )

            # Handle different document types with dynamic update logic
            update_result = self._handle_document_type_update(
                request, user_document, org_doc
            )

            if not update_result.get('success'):
                return update_result.get('response')

            edit_document = update_result.get('document')
            data = update_result.get('data')

            # Update document status to "sent" when document is updated
            if edit_document.status != "sent":
                edit_document.status = "sent"
                edit_document.save(update_fields=['status'])
                logger.info(f"Updated document {edit_document.reference_id} status to 'sent' after update")

            # Update encrypted file passwords if needed
            try:
                update_data = update_document_password(
                    edit_document.owner, edit_document)
                update_data_latest = update_document_password(
                    edit_document.owner, edit_document, update=True
                )
                logger.info(
                    f"Updated document {edit_document.id} PDF password after update.")

                # Update HTML passwords if HTML content was provided
                html_content = request.data.get("content")
                if html_content:
                    update_html_password(
                        edit_document.owner, edit_document)
                    update_html_password(
                        edit_document.owner, edit_document, update=True
                    )
                    logger.info(
                        f"Updated document {edit_document.id} HTML password after update.")
            except Exception as e:
                logger.error(
                    f"Error updating document passwords: {str(e)}", exc_info=True)
                raise

            response_data = self.get_serializer(edit_document).data
            response_data[
                "organization_document_encrypted_reference_id"
            ] = org_doc.encrypted_reference_id

            # Activity log
            try:
                ip_address = get_client_ip(request)
                ActivityLog.objects.create(
                    document_refrence_id=edit_document.reference_id,
                    event_type="Update",
                    title=edit_document.title,
                    device_data=None,
                    ip_address=(ip_address if ip_address else None),
                    category="Document Update",
                    message="Document was updated.",
                    user=getattr(edit_document.updated_by, "reference_id", None),
                )
            except Exception as log_err:
                logger.warning(f"Failed to create activity log for update: {log_err}")

            # Resend sign emails if document has signers and is in "sent" status
            try:
                if edit_document.status == "sent":
                    signers = DocumentSigner.objects.filter(document=edit_document)
                    if signers.exists():
                        from documents.tasks import send_background_email
                        
                        # Get organization document for reference
                        org_doc = OrganizationDocument.objects.filter(
                            user_document=edit_document
                        ).first()
                        
                        if org_doc:
                            # Get user language preference (default to English)
                            language = getattr(request.user, 'language', 'en') or 'en'
                            
                            # Get custom message if provided, otherwise use default
                            message = request.data.get('message', '')
                            
                            # Determine email subject based on document type
                            if edit_document.document_type == 'proposal':
                                email_subject = "Updated Proposal Awaiting Your Signature | Skrivly"
                            else:
                                email_subject = "Updated Document Awaiting Your Signature | Skrivly"
                            
                            # Resend emails to all signers
                            for signer in signers:
                                send_background_email.delay(
                                    email=signer.get_decrypted_email(),
                                    receiver_name=signer.get_decrypted_name(),
                                    reference_signer_id=signer.reference_signer_id,
                                    org_doc_reference_id=org_doc.encrypted_reference_id,
                                    user_id=str(request.user.id),
                                    document=edit_document.reference_id,
                                    language=language,
                                    message=message,
                                    email_subject=email_subject,
                                )
                            
                            logger.info(f"Resent sign emails for document {edit_document.reference_id} to {signers.count()} signers")
                        
            except Exception as email_err:
                logger.warning(f"Failed to resend sign emails after document update: {email_err}")

            return api_response(
                action="data_updated",
                status="success",
                data=response_data,
                message="Document Updated Successfully",
            )
        except ValidationError as e:
            return api_response(action="validation_error", data=e.detail)
        except Exception as e:
            logger.error(
                f"Unexpected error during document update: {str(e)}", exc_info=True)
            return api_response(action="server_error", message=str(e))

    def _handle_document_type_update(self, request, user_document, org_doc):
        """
        Dynamic handler for different document type updates.
        This method can be extended for future document types.
        """
        document_type = user_document.document_type

        if document_type == 'proposal':
            return self._handle_proposal_document_update(request, user_document, org_doc)
        else:
            # Default behavior for other document types (e.g., agreement, contract, etc.)
            return self._update_document_content(request, user_document, org_doc, is_proposal=False)

    def _handle_proposal_document_update(self, request, user_document, org_doc):
        """
        Handle proposal document updates with special logic:
        1. Verify the document is linked to a proposal
        2. Update the same UserDocument with new files/content
        """
        try:
            # Verify this document is linked to a proposal
            if not hasattr(user_document, 'proposal') or not user_document.proposal:
                return {
                    'success': False,
                    'response': api_response(
                        action="validation_error",
                        message="This document is not linked to a proposal"
                    )
                }

            proposal = user_document.proposal
            logger.info(
                f"Updating proposal document for proposal: {proposal.proposal_id}")

            # Use the same content update method for proposal documents
            return self._update_document_content(
                request, user_document, org_doc, is_proposal=True
            )

        except Exception as e:
            logger.error(f"Error in proposal document update: {str(e)}", exc_info=True)
            return {
                'success': False,
                'response': api_response(
                    action="server_error",
                    message=f"Error updating proposal document: {str(e)}"
                )
            }

    def _prepare_serializer_context(self, org_doc):
        """
        Prepare common serializer context with organization.
        """
        context = {**self.get_serializer_context()}
        if org_doc and hasattr(org_doc, "organization"):
            context["organization"] = org_doc.organization
        return context

    def _prepare_document_data(self, request, document_type=None):
        """
        Prepare common document data for update.
        """
        data = copy.copy(request.data)
        data["user"] = request.user.id
        if document_type:
            data["document_type"] = document_type

        # Handle HTML content if provided
        html_content = request.data.get("content")
        if html_content:
            html_file = ContentFile(html_content.encode("utf-8"), name="document.html")
            data["html_file"] = html_file
            data["latest_html_file"] = html_file

        return data

    def _update_document_content(self, request, user_document, org_doc, is_proposal=False):
        """
        Update document content (HTML, title, PDF files, etc.) for the same document.
        This is a shared method for both proposal and non-proposal documents.
        """
        try:
            # Prepare document data for update
            data = self._prepare_document_data(request)

            # Prepare serializer context
            context = self._prepare_serializer_context(org_doc)

            serializer = self.get_serializer(
                user_document, data=data, partial=True, context=context
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()

            document_type = "proposal" if is_proposal else "document"
            logger.info(f"Updated {document_type} content: {user_document.id}")

            return {
                'success': True,
                'document': serializer.instance,
                'data': serializer.data
            }

        except Exception as e:
            document_type = "proposal" if is_proposal else "document"
            logger.error(
                f"Error updating {document_type} content: {str(e)}", exc_info=True)
            return {
                'success': False,
                'response': api_response(
                    action="server_error",
                    message=f"Error updating {document_type} content: {str(e)}"
                )
            }

    def post(self, request, *args, **kwargs):
        try:
            logger.info("POST request received. Creating new document.")
            return self.create(request, *args, **kwargs)
        except ValidationError as e:
            logger.error(
                f"Validation error occurred during document creation: {str(e)}"
            )
            return api_response(action="validation_error", data=e.detail)
        except IntegrityError:
            logger.error("Database integrity error occurred during document creation.")
            return api_response(
                action="data_not_created", message=_("Database integrity error.")
            )
        except Exception as e:
            logger.error(
                f"Unexpected error occurred during document creation: {str(e)}"
            )
            return api_response(action="data_not_created", message=str(e))

    def get(self, request, *args, **kwargs):
        try:
            logger.info(
                "Fetching document list",
                extra={
                    "user_id": str(request.user.id),
                    "filters": request.query_params,
                },
            )
            return super().get(request, *args, **kwargs)
        except Exception as e:
            logger.error(
                "Error fetching document list",
                extra={"user_id": str(request.user.id), "error": str(e)},
                exc_info=True,
            )
            raise

    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class UpdateLatestDocumentFileAPIView(APIView):

    def patch(self, request, *args, **kwargs):
        """
        Update the latest_document_file for a UserDocument using reference_id from path parameters.
        """
        try:
            # Retrieve reference_id from kwargs
            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id", None
            )
            document_reference_id = request.query_params.get(
                "document_reference_id", None
            )
            if not organisation_doc_reference_id:
                return api_response(
                    action="data_not_retrieved",
                    message="document_organisation_reference_id is not there",
                )

            user_document = UserDocument.objects.filter(
                reference_id=document_reference_id
            ).first()
            document_organisation = OrganizationDocument.objects.filter(
                encrypted_reference_id=organisation_doc_reference_id,
                user_document=user_document,
            ).first()

            print("document_organisation", document_organisation)
            logger.info(
                f"PATCH request received for reference_id:{organisation_doc_reference_id}"
            )

            # Check if document is in "sent" status and has been signed by any signer
            if user_document and user_document.status == "sent":
                # Check if any signer has signed the document
                has_signed_signer = DocumentSigner.objects.filter(
                    document=user_document,
                    status="signed"
                ).exists()
                
                if has_signed_signer:
                    return api_response(
                        action="validation_error",
                        message="Cannot edit document. Document is in 'sent' status and has been signed by one or more signers.",
                        status=400
            )

            # Retrieve the document using reference_id
            latest_document_file = request.FILES.get("latest_document_file")
            if not latest_document_file:
                logger.warning("No 'latest_document_file' provided in the request.")
                return api_response(action="data_not_retrieved")
            file_name = str(latest_document_file.name)
            new_file_name = get_file_extension(file_name)
            file_data = latest_document_file.read()
            encrypted_data = cipher_suite.encrypt(file_data)
            document_file = ContentFile(encrypted_data, new_file_name)
            print(settings.ENCRYPTION_KEY)

            logger.info("Encrypted document file for UserDocument")

            # Update the latest_document_file
            user_document.latest_document_file = document_file
            user_document.save()
            update_data = update_document_password(
                user_document.owner, user_document, update=True
            )
            print(update_data)

            logger.info(
                f"Updated latest_document_file for document {organisation_doc_reference_id}."
            )

            return api_response(
                action="data_updated",
                status="success",
            )
        except Exception as e:
            logger.error(f"Error updating latest_document_file: {str(e)}")
            return api_response(
                action="data_not_updated",
                message="Failed to update latest document file",
                status=500,
            )


# Retrieve, update, and delete a single document
class UserDocumentRetrieveUpdateDestroyAPIView(generics.RetrieveUpdateDestroyAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = UserDocumentSerializer

    # pagination_class = CustomPagination
    # filter_backends = [
    #     DjangoFilterBackend,
    #     rest_filters.SearchFilter,
    #     rest_filters.OrderingFilter,
    # ]
    # search_fields = [
    #     "title",
    # ]
    # filterset_class = UserDocumentFilter
    # ordering_fields = ["created_at", "updated_at"]

    # def get_object_list(self):
    #     queryset = UserDocument.objects.filter(user=self.request.user.reference_id)
    #     page = self.paginate_queryset(queryset)
    #     if page is not None:
    #         serializer = self.get_serializer(page, many=True)
    #         return self.get_paginated_response(serializer.data)
    #     serializer = self.get_serializer(queryset, many=True)
    #     return api_response(
    #         action="data_retrieved",
    #         status="success",
    #         data=serializer.data,
    #     )

    def get_only_object(self):
        reference_id = self.kwargs.get("reference_id")
        # document = get_document_or_404(reference_id)
        # Accessing OrganizationDocument related to the UserDocument
        organisation_doc_reference_id = self.request.query_params.get(
            "organisation_doc_reference_id", None
        )

        if not organisation_doc_reference_id or reference_id:
            return api_response(
                action="data_not_retrieved",
                message="document_organisation_refer_id or reference_id not there",
            )

        user_document = UserDocument.objects.filter(reference_id=reference_id).first()

        document_organisation = OrganizationDocument.objects.filter(
            encrypted_reference_id=organisation_doc_reference_id,
            user_document=user_document,
        ).first()
        if not isinstance(user_document, Response):
            logger.info(f"Document {reference_id} retrieved successfully.")
            return api_response(
                data=self.get_serializer(document_organisation.user_document).data,
                action="data_retrieved",
                status="success",
            )
        return api_response(
            action="data_not_retrieved",
        )

    def get_object(self):
        # reference_id = self.kwargs.get("reference_id")
        reference_id = self.kwargs.get("reference_id")
        organisation_doc_reference_id = self.request.query_params.get(
            "organisation_doc_reference_id", None
        )

        if not organisation_doc_reference_id or not reference_id:
            return api_response(
                action="data_not_retrieved",
                message="document_organisation_refer_id or reference_id not there",
            )
        user_document = UserDocument.objects.filter(reference_id=reference_id).first()

        organization_document = OrganizationDocument.objects.filter(
            encrypted_reference_id=organisation_doc_reference_id,
            user_document=user_document,
        ).first()

        return organization_document.user_document, organization_document

    def get(self, request, *args, **kwargs):
        try:
            logger.info("GET request received for document")
            # reference_id = kwargs.get("reference_id")
            # if reference_id:
            return self.get_only_object()
            # return self.get_object_list()
        except NotFound as e:
            logger.error(f"Document not found: {str(e)}")
            return api_response(action="data_not_retrieved", message=str(e))
        except Http404 as e:
            logger.error(f"Document not found: {str(e)}")
            return api_response(action="data_not_retrieved", message=str(e))
        except Exception as e:
            logger.error(f"Unexpected error while retrieving document {str(e)}")
            return api_response(action="server_error", message=str(e))

    def put(self, request, *args, **kwargs):
        try:
            logger.info("PUT request received for document")
            instance, _ = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            logger.info("Document updated successfully.")
            return api_response(
                action="data_updated",
                status="success",
                data=serializer.data,
            )
        except NotFound as e:
            logger.error(f"Document not found during update: {str(e)}")
            return api_response(action="data_not_retrieved", message=str(e))
        except Http404 as e:
            logger.error(f"Document not found during update: {str(e)}")
            return api_response(action="data_not_retrieved", message=str(e))
        except Exception as e:
            logger.error(f"Unexpected error while updating document: {str(e)}")
            return api_response(action="server_error", message=str(e))

    def patch(self, request, *args, **kwargs):
        """
        Handles PATCH request for partial updates to the document.
        This allows updating only a subset of fields.
        """
        try:
            logger.info("PATCH request received for document")
            instance, organization_document = self.get_object()
            # The `partial=True` flag allows partial updates, i.e., not all fields need to be provided.
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            # Perform the update
            self.perform_update(serializer)
            edit_document = serializer.instance
            # update_data = update_document_password(request.user, edit_document)
            update_data_latest = update_document_password(
                edit_document.user, edit_document, update=True
            )
            logger.info(f"Updated document {edit_document.id} password after creation.")
            response_data = self.get_serializer(update_data_latest).data
            response_data[
                "organization_document_encrypted_reference_id"
            ] = organization_document.encrypted_reference_id
            logger.info("Document partially updated successfully.")
            return api_response(
                action="data_updated",
                status="success",
                data=response_data,
            )

        except NotFound as e:
            logger.error(f"Document not found during patch: {str(e)}")
            return api_response(action="data_not_retrieved", message=str(e))
        except Http404 as e:
            logger.error(f"Document not found during patch: {str(e)}")
            return api_response(action="data_not_retrieved", message=str(e))
        except Exception as e:
            logger.error(
                f"Unexpected error while partially updating document: {str(e)}"
            )
            return api_response(action="server_error", message=str(e))

    def delete(self, request, *args, **kwargs):
        try:
            logger.info("DELETE request received for document")
            obj = self.get_object()
            user_document = getattr(obj, "user_document", None) or obj
            if user_document.status != "withdrawn":
                return api_response(
                    action="data_not_deleted",
                    status="failed",
                    message="Document must be withdrawn before deletion."
                )
            user_document.move_to_delete(request.user)
            logger.info(
                f"Document {kwargs.get('organization_document_reference_id')}"
                f" deleted successfully."
            )
            return api_response(
                action="data_deleted",
                message=_("Document successfully deleted."),
                status="success",
            )
        except NotFound as e:
            logger.error(
                f"Document {kwargs.get('organization_document_reference_id')}"
                f" not found during delete: {str(e)}"
            )
            return api_response(action="data_not_retrieved", message=str(e))
        except Http404 as e:
            logger.error(
                f"Document {kwargs.get('organization_document_reference_id')}"
                f" not found during delete: {str(e)}"
            )
            return api_response(action="data_not_retrieved", message=str(e))
        except Exception as e:
            logger.error(
                f"Unexpected error while deleting document"
                f" {kwargs.get('organization_document_reference_id')}: {str(e)}"
            )
            return api_response(action="server_error", message=str(e))


class DocumentSignerManageAPIView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def initialize_request(self, request, *args, **kwargs) -> Request:
        # Override authentication/permissions only for PATCH method
        if request.method == 'PATCH':
            self.authentication_classes = []
            self.permission_classes = [AllowAny]
        return super().initialize_request(request, *args, **kwargs)

    def get(self, request, reference_signer_id=None):
        try:
            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id")
            doc_reference_id = request.query_params.get("doc_reference_id")

            if not organisation_doc_reference_id or not doc_reference_id:
                return api_response(action="data_not_retrieved", message="Missing reference IDs")

            user_document = UserDocument.objects.filter(
                reference_id=doc_reference_id).first()
            document_organisation = OrganizationDocument.objects.filter(
                encrypted_reference_id=organisation_doc_reference_id,
                user_document=user_document,
            ).first()
            document_response = document_organisation.user_document

            if reference_signer_id:
                signer = get_signer_or_404(reference_signer_id, document_response)
                return api_response(action="data_retrieved", status="success", data=DocumentSignerSerializer(signer).data)

            signers = DocumentSigner.objects.filter(document=document_response)
            return api_response(action="data_retrieved", status="success", data=DocumentSignerSerializer(signers, many=True).data)

        except (NotFound, Http404) as e:
            return api_response(action="data_not_retrieved", message=str(e))
        except Exception as e:
            return api_response(action="server_error", message=str(e))

    def post(self, request):
        try:
            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id")
            doc_reference_id = request.query_params.get("doc_reference_id")

            if not organisation_doc_reference_id:
                return api_response(action="data_not_retrieved", message="Missing organisation_doc_reference_id")

            user_document = UserDocument.objects.filter(
                reference_id=doc_reference_id).first()
            document_organisation = OrganizationDocument.objects.filter(
                encrypted_reference_id=organisation_doc_reference_id,
                user_document=user_document,
            ).first()
            document_response = document_organisation.user_document

            signers_data = request.data
            responses, exist_resp = [], []

            with transaction.atomic():
                for signer_data in signers_data:
                    if filter_by_email(signer_data.get("email"), document_response):
                        exist_resp.append(
                            f"{signer_data.get('email')} already assigned")
                        continue

                    signer_data["document"] = document_response.id
                    signer_data["is_name_editable"] = signer_data.get(
                        "is_name_editable", False)

                    signer_data["hide_security_number"] = signer_data.get(
                        "hide_security_number", False
                    )
                    signer_data["show_org_name"] = signer_data.get(
                        "show_name", False
                    )

                    serializer = DocumentSignerSerializer(data=signer_data)
                    serializer.is_valid(raise_exception=True)
                    signer_instance = serializer.save()
                    responses.append(DocumentSignerSerializer(signer_instance).data)

            log_user_action(
                request=request,
                user=request.user,
                action="Document signer created successfully.",
                status="success",
                details={"type": "document_signer_created"},
            )

            return api_response(action="data_created", status="success", data={"created": responses, "existed": exist_resp})

        except NotFound as e:
            return api_response(action="data_not_retrieved", message=str(e))
        except ValidationError as e:
            return api_response(action="validation_error", data=e.detail)
        except IntegrityError:
            return api_response(action="data_not_created", message=_("Database integrity error."))
        except Exception as e:
            return api_response(action="server_error", message=str(e))

    def patch(self, request, reference_signer_id):
        try:
            with transaction.atomic():
                doc_reference_id = request.query_params.get("doc_reference_id")
                document_response = get_document_or_404(doc_reference_id)
                signer = get_signer_or_404(reference_signer_id, document_response)

                if signer.status == "signed":
                    return api_response(action="validation_error", message="Already signed", status=400)

                ip_address = get_client_ip(request)
                device_data = request.data.get("device_data", {})

                signer_data = request.data.copy()

                # Verify the verification token
                if signer_data.get("status") == "signed":
                    verification_token = request.query_params.get("verification_token")
                    if not verification_token or not DocumentVerificationService.verify_token(
                        document_response, signer, verification_token
                    ):
                        signer.status = "pending"
                        signer.save()
                        return api_response(action="validation_error", message="Invalid token", status=403)

                signer_data["document"] = document_response.id
                signer_data["ip_address"] = ip_address

                # Sweden BankID metadata
                bank_id_data = signer_data.get("bank_id_data")
                is_bankid_signature = False
                if bank_id_data:
                    is_bankid_signature = True
                    # If it's a string, parse it to dict
                    if isinstance(bank_id_data, str):
                        try:
                            bank_id_data = json.loads(bank_id_data)
                        except Exception:
                            pass  # If it's not a valid JSON string, keep as is
                    signer_data["bank_id_meta_data"] = bank_id_data
                    # Remove the original key to avoid serializer errors
                    signer_data.pop("bank_id_data", None)

                    # Validate BankID token availability before allowing signature
                    if is_bankid_signature:
                        try:
                            # Get the document owner's organization
                            document_owner = document_response.created_by
                            if not document_owner:
                                return api_response(
                                    action="validation_error",
                                    message="Document owner not found",
                                    status=400
                                )

                            subscription_feature = SubscriptionFeatureManager(
                                document_owner)
                            organization = subscription_feature.get_organization()

                            if not organization:
                                return api_response(
                                    action="validation_error",
                                    message="Organization not found for BankID signature",
                                    status=400
                                )

                            # Check if organization has remaining BankID tokens
                            if organization.bankid_tokens_remaining <= 0:
                                return api_response(
                                    action="insufficient_tokens",
                                    message="No BankID tokens remaining. Please purchase more tokens to continue with BankID signatures.",
                                    status=400
                                )

                        except Exception as e:
                            logger.error(
                                f"Error validating BankID tokens: {str(e)}", exc_info=True)
                            return api_response(
                                action="validation_error",
                                message="Error validating BankID tokens",
                                status=400
                            )

                # check if signature image is present before signing the document.
                if not signer_data.get("signature_image"):
                    logger.info(f"Signature image not found for signer {signer.id}")
                    return api_response(action="validation_error", message="Signature image not found", status=400)

                serializer = DocumentSignerSerializer(
                    signer, data=signer_data, partial=True)
                serializer.is_valid(raise_exception=True)
                serializer.save()

                if serializer.data["status"] == "signed":
                    update_document_status_as_approved(signer, ip_address, device_data)

                    # Consume BankID token after successful signature
                    if is_bankid_signature:
                        try:
                            # Get the document owner's organization
                            document_owner = document_response.created_by
                            if document_owner:
                                subscription_feature = SubscriptionFeatureManager(
                                    document_owner)
                                organization = subscription_feature.get_organization()

                                if organization and organization.bankid_tokens_remaining > 0:
                                    # Consume one BankID token
                                    organization.bankid_tokens_remaining -= 1
                                    organization.bankid_tokens_used += 1
                                    organization.save(
                                        update_fields=['bankid_tokens_remaining', 'bankid_tokens_used'])

                                    logger.info(
                                        f"BankID token consumed for organization {organization.id}. Remaining: {organization.bankid_tokens_remaining}, Used: {organization.bankid_tokens_used}")

                                    # Log BankID usage tracking
                                    from subscriptions.models.subscription_masters import BankIDUsageTracking
                                    organization_subscription = subscription_feature.get_organisation_subscription()
                                    if organization_subscription:
                                        BankIDUsageTracking.objects.create(
                                            user_subscription=organization_subscription,
                                            organization=organization,
                                            document_ref_id=document_response.reference_id,
                                            signer_ref_id=signer.reference_id,
                                            document_name=document_response.title,
                                            signer_name=signer.get_decrypted_name(),
                                            signer_email=signer.get_decrypted_email(),
                                            usage_status='completed',
                                            tokens_consumed=1,
                                            completed_at=timezone.now(),
                                            ip_address=ip_address,
                                            user_agent=device_data.get(
                                                'user_agent', ''),
                                            session_id=device_data.get(
                                                'session_id', ''),
                                            initiated_by=document_owner
                                        )

                        except Exception as e:
                            logger.error(
                                f"Error consuming BankID token: {str(e)}", exc_info=True)
                            # Don't fail the signature process for token consumption errors

                # Get organisation_doc_reference_id from document_response
                organisation_doc_reference_id = None

                try:
                    org_doc = OrganizationDocument.objects.get(
                        user_document=document_response)
                    organisation_doc_reference_id = org_doc.encrypted_reference_id
                except OrganizationDocument.DoesNotExist:
                    logger.warning(
                        f"No organization document found for document {document_response.id}")

                # Get user_id from document owner
                user_id = document_response.created_by.reference_id if document_response.created_by else None
                status_signers, result = check_all_status_send_mail(
                    signer=signer,
                    ip_address=ip_address,
                    organization_doc_reference_id=organisation_doc_reference_id,
                    user_id=user_id,
                    document=document_response,
                    device_data=device_data,
                )

                if not status_signers:
                    return api_response(action="validation_error", message=result.get("error"), data=result)

                ActivityLog.objects.create(
                    document_refrence_id=document_response.reference_id,
                    event_type="Signed",
                    title=document_response.title,
                    device_data=device_data,
                    email=signer.email,
                    ip_address=(ip_address if ip_address else None),
                    phone_number=signer.phone_number if signer.phone_number else None,
                    category="Document Signed",
                    message=f"Document signed by {signer.get_decrypted_email()}",
                )

                log_user_action(
                    request=request,
                    user=request.user,
                    action="Document signed successfully.",
                    status="success",
                    details={"type": "document_signer_updated"},
                )

                return api_response(action="data_updated", status="success", data=serializer.data)

        except Exception as e:
            return api_response(action="server_error", message=str(e))

    def delete(self, request, reference_signer_id):
        try:
            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id")
            doc_reference_id = request.query_params.get("doc_reference_id")

            if not organisation_doc_reference_id:
                return api_response(action="data_not_retrieved", message="Missing organisation_doc_reference_id")

            user_document = UserDocument.objects.filter(
                reference_id=doc_reference_id).first()
            document_organisation = OrganizationDocument.objects.filter(
                encrypted_reference_id=organisation_doc_reference_id
            ).first()
            document_response = document_organisation.user_document

            signer = get_signer_or_404(reference_signer_id, document_response)

            with transaction.atomic():
                signer.delete()

            return api_response(action="data_deleted", status="success")

        except NotFound as e:
            return api_response(action="data_not_retrieved", message=str(e))
        except IntegrityError:
            return api_response(action="data_not_deleted", message=_("Database integrity error."))
        except Exception as e:
            return api_response(action="server_error", message=str(e))


class SignerUpdateManagementAPIView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Delete all signers for a document and add new signers.
        """
        organisation_doc_reference_id = request.query_params.get(
            "organisation_doc_reference_id"
        )
        doc_reference_id = request.query_params.get("doc_reference_id")

        if not organisation_doc_reference_id or not doc_reference_id:
            return api_response(
                action="data_not_retrieved",
                message="Missing organisation_doc_reference_id or doc_reference_id",
            )

        user_document = UserDocument.objects.filter(
            reference_id=doc_reference_id,
            is_deleted=False
        ).first()
        document_organisation = OrganizationDocument.objects.filter(
            encrypted_reference_id=organisation_doc_reference_id,
            user_document=user_document,
        ).first()

        if not document_organisation:
            return api_response(
                action="data_not_retrieved", message="Document organisation not found"
            )

        document_response = document_organisation.user_document
        signers_data = request.data

        if not signers_data:
            return api_response(
                action="data_not_updated", message="No signers data provided"
            )

        # Delete all existing signers for the document
        DocumentSigner.objects.filter(document=document_response).delete()
        logger.info(
            f"Deleted all signers for document {document_response.reference_id}"
        )

        responses = []
        errors = []

        with transaction.atomic():
            for signer_data in signers_data:
                signer_data[
                    "document"
                ] = document_response.id  # Add document ID to new signer
                signer_data["show_org_name"] = signer_data.get(
                    "show_name", False
                )
                serializer = DocumentSignerSerializer(data=signer_data)

                if serializer.is_valid():
                    new_signer = serializer.save()
                    responses.append(DocumentSignerSerializer(new_signer).data)
                else:
                    errors.append(
                        f"Failed to create signer with data {signer_data}: {serializer.errors}"
                    )

        if errors:
            return api_response(
                action="data_not_updated",
                message="Errors encountered during deletion and creation",
                data=errors,
            )

        return api_response(action="data_updated", status="success", data=responses)


class OneTimeJWTManageAPIView(generics.GenericAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        print(request.user)
        logger.info(f"Generating one-time JWT token for user: {request.user}")
        return api_response(
            data={"token": generate_one_time_token(request.user)},
            action="data_created",
            status="success",
        )


class DocumentRetrieveViewApi(generics.GenericAPIView):
    # authentication_classes = [OneTimeAcessJWTTokenAuthentication]
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, reference_id):
        logger.info(
            f"Starting document retrieval process for reference_id: {reference_id} by user: {request.user}"
        )
        # Retrieve user data
        user = request.user
        # flag, type_, otp_obj = check_is_verified(
        #     user.get_decrypted_email(), user.get_decrypted_phone_number()
        # )
        # str_date_joined = user.date_joined.isoformat()
        document_response = get_document_or_404(reference_id)
        str_date_joined = document_response.owner.date_joined.isoformat()
        str_created_obj = document_response.created_at.isoformat()
        s3_url = document_response.latest_document_file.name
        final_str_datetime = str_date_joined + "/" + str_created_obj
        logger.debug(f"Generated final string for decryption: {final_str_datetime}")
        # If download_pdf is True, follow the document retrieval flow
        # if document_response.download_pdf or (
        #     not document_response.download_pdf and flag
        # ):
        # if document_response.download_pdf:
        logger.info(
            f"Attempting to download and decrypt the document data from S3 for reference_id: {reference_id}"
        )
        # if otp_obj:
        #     otp_obj.is_download = False
        #     otp_obj.save()
        decrypted_data = download_and_decrypt_from_s3(s3_url, final_str_datetime)
        logger.debug(f"Decrypted document data for reference_id: {reference_id}.")
        # Base64 encode the encrypted file data
        encoded_file_data = base64.b64encode(decrypted_data).decode("utf-8")
        logger.debug(
            f"Base64 encoded document data for reference_id: {reference_id}."
        )

        # Check if HTML file exists before processing
        html_text = None
        if document_response.html_file and document_response.html_file.name:
            html_name = document_response.html_file.name
            logger.info(f"Processing HTML file: {html_name}")

            # Download and decrypt HTML
            html_bytes = download_and_decrypt_from_s3(html_name, final_str_datetime)

            try:
                html_text = html_bytes.decode("utf-8")
            except UnicodeDecodeError:
                html_text = None
        else:
            logger.info(f"No HTML file found for document {reference_id}")

        context = {
            "encrypted_file": encoded_file_data,
            "file_name": document_response.latest_document_file.name,
            "reference_id": reference_id,
            "html_file": document_response.html_file.name if document_response.html_file else None,
            "html_content": html_text,
        }
        logger.info(
            f"Document successfully retrieved and processed for reference_id: {reference_id}."
        )
        log_user_action(
            request=request,
            user=request.user,
            action="Document retrieved successfully.",
            status="success",
            details={"type": "document_retrieved"},
        )
        return api_response(
            data=context,
            action="data_retrieved",
            status="success",
        )

        # else:
        #     # If download_pdf is False, proceed with the verification logic
        #     logger.info(
        #         f"download_pdf is False for reference_id: {reference_id}. Initiating verification process."
        #     )
        #     # Check if user has both email and phone_number
        #     email = user.get_decrypted_email()
        #     receiver_name = user.first_name + " " + user.last_name
        #     phone_number = user.get_decrypted_phone_number()

        #     otp = generate_otp()
        #     if not otp:
        #         logger.error("OTP generation failed for email: %s", email)
        #         return api_response(
        #             action="data_not_created",
        #             message=_("Failed to generate OTP. Please try again."),
        #             status="failed",
        #         )

        #     try:
        #         # if (email and phone_number) or (email and not phone_number):
        #         if email:
        #             otp_user, created = OneTimePassword.objects.update_or_create(
        #                 email=generate_hmac_token(email),
        #                 defaults={
        #                     "otp_code": otp,
        #                     # "is_download": False,
        #                     # 'expiration_time': timezone.now() + timezone.timedelta(minutes=10)
        #                 },
        #             )
        #             # if created:
        #             send_background_email.delay(email, receiver_name=receiver_name, otp=otp)
        #         # if not email and phone_number:
        #         if phone_number:
        #             otp_user, created = OneTimePassword.objects.update_or_create(
        #                 phone_number=generate_hmac_token(phone_number),
        #                 defaults={
        #                     "otp_code": otp,
        #                     # "is_download": False,
        #                     # 'expiration_time': timezone.now() + timezone.timedelta(minutes=10)
        #                 },
        #             )
        #             # if created:
        #             send_otp_via_sms(
        #                 receiver_name=receiver_name, phone_number=phone_number, otp=otp
        #             )

        #         return api_response(
        #             data={
        #                 "email": email,
        #                 "phone_number": phone_number,
        #                 # "otp": otp,
        #                 "otp_sended": True,
        #                 # "type": type_,
        #                 # "is_existing_user": False,  # Indicates a new user
        #                 # "profile_updated": profile_updated
        #             },
        #             action="data_created",
        #             status="success",
        #             message=_("An OTP to has been sent for verification."),
        #         )

        #     except Exception as e:
        #         logger.error("Error sending OTP email to %s: %s", email, str(e))
        #         return api_response(
        #             action="data_not_created",
        #             message=_("Failed to send OTP email. Please try again."),
        #             status="failed",
        #         )


def send_background_email_verification(email):
    # Your logic for sending verification email
    pass


def send_sms_verification(phone_number):
    # Your logic for sending verification SMS
    pass


# class DocumentRetrieveViewApi(generics.GenericAPIView):
#     # authentication_classes = [OneTimeAcessJWTTokenAuthentication]
#     authentication_classes = [JWTAccessTokenAuthentication]
#     permission_classes = [IsAuthenticated]

#     def post(self, request, reference_id):
#         logger.info(
#             f"Starting document"
#             f" retrieval process for reference_id: {reference_id}"
#             f" by user: {request.user}"
#         )
#         str_date_joined = request.user.date_joined.isoformat()
#         document_response = get_document_or_404(reference_id)
#         str_created_obj = document_response.created_at.isoformat()
#         s3_url = document_response.latest_document_file.name
#         # s3_url = request.data["document_path"]
#         final_str_datetime = str_date_joined + "/" + str_created_obj
#         logger.debug(f"Generated final string for decryption: {final_str_datetime}")

#         logger.info(
#             f"Attempting to download and "
#             f"decrypt the document data from"
#             f" S3 for reference_id: {reference_id}"
#         )
#         decrypted_data = download_and_decrypt_from_s3(s3_url, final_str_datetime)
#         logger.debug(f"Decrypted document data " f"for reference_id: {reference_id}.")
#         # Base64 encode the encrypted file data
#         encoded_file_data = base64.b64encode(decrypted_data).decode("utf-8")
#         logger.debug(
#             f"Base64 encoded document " f"data for reference_id: {reference_id}."
#         )
#         context = {
#             "encrypted_file": encoded_file_data,
#             "file_name": document_response.latest_document_file.name,
#             "reference_id": reference_id,
#         }
#         logger.info(
#             f"Document successfully retrieved and"
#             f" processed for reference_id: {reference_id}."
#         )
#         return api_response(
#             data=context,
#             action="data_retrieved",
#             status="success",
#         )


class DocumentSignerUpdateView(generics.UpdateAPIView):
    """
    Update the signer for a given document.
    Uses the standard UpdateAPIView with minimal customization.
    """

    # authentication_classes = [OneTimeAcessJWTTokenAuthentication]
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    serializer_class = DocumentSignerSerializer

    def get_object(self):
        """
        Retrieve the document organisation based on the query params.
        This method will fetch the document organisation if the given reference ID matches.
        """
        organisation_doc_reference_id = self.request.query_params.get(
            "organisation_doc_reference_id", None
        )
        doc_reference_id = self.request.query_params.get("doc_reference_id", None)

        if not organisation_doc_reference_id or not doc_reference_id:
            raise ValidationError(
                "Both 'organisation_doc_reference_id' and 'doc_reference_id' must be provided."
            )

        try:
            user_document = UserDocument.objects.get(reference_id=doc_reference_id)
            document_organisation = OrganizationDocument.objects.get(
                encrypted_reference_id=organisation_doc_reference_id,
                user_document=user_document,
            )
        except OrganizationDocument.DoesNotExist:
            raise NotFound(
                "Document organisation not found with the provided reference ID."
            )
        except UserDocument.DoesNotExist:
            raise NotFound(
                "User document not found with the provided document reference ID."
            )

        return document_organisation

    def patch(self, request, *args, **kwargs):
        """
        Handle the PATCH request to update the signer data.
        """
        try:
            ip_address = get_client_ip(request)
            # Retrieve the document organisation
            document_organisation = self.get_object()

            # TODO: Unable to Understand, what need to be used, user_document.created_by or user_document.owner
            feature_manager = SubscriptionFeatureManager(
                document_organisation.user_document.created_by
            )
            # can_create, limit_info = feature_manager.can_create_feature("signs")
            # if not can_create:
            #     return api_response(
            #         action="validation_error",
            #         message=limit_info.get("error"),
            #         data=limit_info,
            #     )

            signer_data = request.data
            signer_data[
                "document"
            ] = document_organisation.user_document.id  # Ensure document association

            # Check if the signer exists
            signer = get_signer_or_404(
                kwargs["reference_signer_id"], document_organisation.user_document
            )
            if signer.signer_type == "sweden_bank_id":
                can_create, limit_info = feature_manager.check_feature_availability(
                    "bankid_signs")
            else:
                can_create, limit_info = feature_manager.check_feature_availability(
                    "signs")

            if not can_create:
                return api_response(
                    action="validation_error",
                    message=limit_info.get("error"),
                    data=limit_info,
                )
            # Use the standard serializer to update the signer
            serializer = self.get_serializer(signer, data=signer_data, partial=True)
            serializer.is_valid(raise_exception=True)
            updated_signer = serializer.save()

            # Check if the signer status is approved and update the document status
            if updated_signer.status == "signed":
                update_document_status_as_approved(updated_signer, ip_address)

            # Optionally, check the status of all signers and send notifications
            status_signers, result = check_all_status_send_mail(
                signer=updated_signer,
                ip_address=ip_address,
                organization_doc_reference_id=request.query_params.get(
                    "organisation_doc_reference_id"),
                user_id=request.user.reference_id,
                document=updated_signer.document,
                device_data=request.data.get("device_data")
            )
            if not status_signers:
                return api_response(
                    action="validation_error", message=result.get("error"), data=result
                )
            log_user_action(
                request=request,
                user=request.user,
                action="Document signed successfully.",
                status="success",
                details={"type": "document_signer_updated"},
            )
            # Return the updated signer data in the response
            response_data = serializer.data
            response_data["result"] = result
            response_data["limit_info"] = limit_info
            return api_response(
                action="data_updated", status="success", data=response_data
            )

        except ValidationError as e:
            logger.error(f"Validation error: {e}")
            return api_response(action="validation_error", message=str(e))
        except NotFound as e:
            logger.error(f"Document or signer not found: {e}")
            return api_response(action="data_not_retrieved", message=str(e))
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return api_response(action="server_error", message=str(e))


class SendSignerEmailAPIView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Send Document Signer Email"""
        ip_address = get_client_ip(request)
        organisation_doc_reference_id = None
        user_id = request.user.reference_id

        try:
            message = request.data.get("message", None)
            language = request.query_params.get("language", None)
            download_pdf = request.query_params.get("download_pdf", False)
            # is_global = request.query_params.get("is_global", False)
            expiry_date = request.query_params.get("expiry_date", None)
            agreement_start_date = request.query_params.get(
                "agreement_start_date", None)
            agreement_end_date = request.query_params.get("agreement_expiry_date", None)

            logger.info(
                f"Agreement End Date Value from the frontend: {agreement_end_date}")
            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id", None
            )
            doc_reference_id = request.query_params.get("doc_reference_id", None)
            device_data = request.data.get("device_data", None)
            if not organisation_doc_reference_id:
                return api_response(
                    action="data_not_retrieved",
                    message="document_organisation_reference_id is not there",
                )

            def parse_date(date_str):
                if not date_str:
                    return None
                try:
                    # Try parsing ISO format first
                    from dateutil.parser import parse
                    return parse(date_str)
                except Exception:
                    # Fallback to common datetime formats if needed
                    pass
                return None

            # expiry_dt = parse_date(expiry_date)
            agreement_start_dt = parse_date(agreement_start_date)
            agreement_expiry_dt = parse_date(agreement_end_date)

            if agreement_start_dt and agreement_expiry_dt:
                # and agreement_expiry_dt must be on or after agreement_start_dt
                if agreement_expiry_dt < agreement_start_dt:
                    return api_response(
                        action="validation_error",
                        message="Agreement end date must be after the document expiry date.",
                    )

            # signers
            signers_data = request.data.get("data", None)
            send_document_for_digital_sign.delay(doc_reference_id, organisation_doc_reference_id, signers_data, language, message,
                                                 download_pdf, expiry_date, device_data, ip_address, user_id, agreement_start_date, agreement_end_date)
            log_user_action(
                request=request,
                user=request.user,
                action="Document sent successfully.",
                status="success",
                details={"type": "document_sent"},
            )

            return api_response(
                action="data_retrieved",
                message="Email sent successfully to the signer.",
                status="success",
            )

        except Exception as e:
            logger.error(
                f"Unexpected error during signer creation for document : {str(e)}"
            )
            return api_response(action="server_error", message=str(e))


class ActivityLogView(generics.ListCreateAPIView):
    serializer_class = ActivityLogSerializer
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        # Fetch the logs filtered by documentsignup
        organisation_doc_obj = None
        organisation_doc_reference_id = self.request.query_params.get(
            "organisation_doc_reference_id"
        )
        doc_reference_id = self.request.query_params.get("doc_reference_id")
        if not organisation_doc_reference_id:
            return api_response(
                action="data_not_retrieved",
                message="document_organisation_reference_id is not there",
            )
        if organisation_doc_reference_id:
            organisation_doc_obj = OrganizationDocument.objects.filter(
                encrypted_reference_id=organisation_doc_reference_id,
                user_document__reference_id=doc_reference_id,
            ).first()
            if not organisation_doc_obj:
                logger.error(
                    f"No OrganizationDocument found "
                    f"with reference ID: {organisation_doc_reference_id}"
                )
                # raise ValueError(f"No OrganizationDocument
                # found with reference ID: {organisation_doc_reference_id}")

        queryset = ActivityLog.objects.filter(
            document_refrence_id=organisation_doc_obj.user_document.reference_id
        ).order_by("timestamp")

        return queryset

    def list(self, request, *args, **kwargs):
        """
        Override the list method to customize the response structure
        """

        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)

        # Grouping the logs by date
        response_data = {}

        for activity_log in serializer.data:
            for date, log_data in activity_log.items():
                if date not in response_data:
                    response_data[date] = []

                response_data[date].append(log_data)

        log_user_action(
            request=request,
            user=request.user,
            action="Document activity logs retrieved successfully.",
            status="success",
            details={"type": "document_activity_logs_retrieved"},
        )

        return api_response(
            data=response_data,
            action="data_retrieved",
            status="success",
        )


class DocumentPreviewRetrieveViewApi(generics.GenericAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, reference_id):
        try:
            logger.info(
                f"Starting document retrieval process for reference_id: {reference_id} by user: {request.user}"
            )
            # Retrieve user data
            user = request.user

            # Get document
            document_response = get_document_or_404(reference_id)
            if not document_response or not hasattr(document_response, 'document_file'):
                logger.error(
                    f"Document not found or has no document file: {reference_id}")
                return api_response(
                    action="data_not_retrieved",
                    message="Document not found or has no document file.",
                    status=404
                )

            # The decryption key requires the document owner's join date, not the current user's.
            document_owner = document_response.owner
            if not document_owner:
                logger.error(
                    f"Document {reference_id} has no owner, cannot generate decryption key.")
                return api_response(
                    action="data_not_retrieved",
                    message="Document owner not found.",
                    status=404
                )

            # Override the initially set str_date_joined with the owner's date.
            str_date_joined = document_owner.date_joined.isoformat()

            str_created_obj = document_response.created_at.isoformat()
            s3_url = document_response.document_file.name

            if not s3_url:
                logger.error(f"Document has no S3 URL in document_file: {reference_id}")
                return api_response(
                    action="data_not_retrieved",
                    message="Document has no file URL.",
                    status=404
                )

            final_str_datetime = str_date_joined + "/" + str_created_obj
            logger.debug(f"Generated final string for decryption: {final_str_datetime}")

            try:
                decrypted_data = download_and_decrypt_from_s3(
                    s3_url, final_str_datetime)
                logger.debug(
                    f"Decrypted document data for reference_id: {reference_id}.")
                # Base64 encode the encrypted file data
                encoded_file_data = base64.b64encode(decrypted_data).decode("utf-8")
                logger.debug(
                    f"Base64 encoded document data for reference_id: {reference_id}.")
            except ValueError as e:
                logger.error(f"Error downloading/decrypting document: {str(e)}")
                return api_response(
                    action="data_not_retrieved",
                    message=str(e),
                    status=400
                )
            except Exception as e:
                logger.error(
                    f"Unexpected error downloading/decrypting document: {str(e)}", exc_info=True)
                return api_response(
                    action="server_error",
                    message="Failed to retrieve document. Please try again.",
                    status=500
                )

            # html_name = document_response.html_file.name
            # html_bytes = download_and_decrypt_from_s3(html_name, final_str_datetime)

            # try:
            #     html_text = html_bytes.decode("utf-8")
            # except UnicodeDecodeError:
            #     html_text = None

            html_name = None
            html_text = None
            try:
                if getattr(document_response, 'html_file', None) and document_response.html_file.name:
                    html_name = document_response.html_file.name
                    html_bytes = download_and_decrypt_from_s3(
                        html_name, final_str_datetime)
                    try:
                        html_text = html_bytes.decode("utf-8")
                    except UnicodeDecodeError:
                        html_text = None
                else:
                    logger.info(
                        f"No HTML file attached for reference_id: {reference_id}")
            except ValueError as e:
                logger.warning(f"HTML decrypt issue for {reference_id}: {str(e)}")
            except Exception as e:
                logger.error(
                    f"Unexpected HTML handling error for {reference_id}: {str(e)}", exc_info=True)

            context = {
                "encrypted_file": encoded_file_data,
                "file_name": document_response.document_file.name,
                "reference_id": document_response.reference_id,
                "html_file": html_name,
                "html_content": html_text,
            }
            logger.info(
                f"Document successfully retrieved and processed for reference_id: {document_response.reference_id}."
            )
            log_user_action(
                request=request,
                user=request.user,
                action="Document retrieved successfully.",
                status="success",
                details={"type": "document_retrieved"},
            )
            return api_response(
                data=context,
                action="data_retrieved",
                status="success",
            )
        except Exception as e:
            logger.error(
                f"Error in DocumentPreviewRetrieveViewApi: {str(e)}", exc_info=True)
            return api_response(
                action="server_error",
                message="An error occurred while retrieving the document.",
                status=500
            )


class OptionalJWTAccessTokenAuthentication(JWTAccessTokenAuthentication):
    """
    Custom authentication class that allows both JWT authentication and no authentication.
    Returns None if no Authorization header is present, allowing the view to handle both scenarios.
    """

    def authenticate(self, request: HttpRequest):
        authentication_token = request.META.get("HTTP_AUTHORIZATION")
        if not authentication_token:
            # Return None to indicate no authentication, but don't raise an exception
            return None

        # If Authorization header is present, use the parent class authentication
        return super().authenticate(request)


class DocumentPreviewListCreateView(generics.ListCreateAPIView):
    serializer_class = DocumentPreviewSerializer
    authentication_classes = [OptionalJWTAccessTokenAuthentication]

    def get_queryset(self):
        return DocumentSigner.objects.all()

    def list(self, request, *args, **kwargs):
        message = None
        device_data = None
        is_agreement = False
        ip_address = get_client_ip(request)

        reference_user_id = self.kwargs.get("reference_user_id")
        reference_document_id = self.kwargs.get("reference_document_id")
        reference_signer_id = self.kwargs.get("reference_signer_id")
        organisation_doc_reference_id = self.kwargs.get("reference_org_doc_id")

        device = request.query_params.get("device_data", None)
        if device:
            device_data = json.loads(device)

        # Get verification token from query params
        verification_token = request.query_params.get("verification_token")

        # Get document and signer
        document = UserDocument.objects.get(reference_id=reference_document_id)
        if document and document.is_agreement:
            is_agreement = True

        # Get signer
        signer = DocumentSigner.objects.get(reference_signer_id=reference_signer_id)

        document_status, msg = check_document_status(user_document=document)
        if not document_status:
            return api_response(
                action="data_not_retrieved",
                message=msg,
                status=404
            )

        # Handle authentication based on document type
        if is_agreement:
            # Agreement documents are public - no authentication or verification required
            pass
        else:
            # For non-agreement documents, require verification token or authentication
            if verification_token:
                # If verification token is provided, verify it
                if not DocumentVerificationService.verify_token(document, signer, verification_token):
                    return api_response(
                        action="data_not_retrieved",
                        message="Verification expired or invalid. Please verify again.",
                        status=401
                    )
            else:
                # If no verification token, check if user is authenticated and owns the document
                if not request.user.is_authenticated:
                    return api_response(
                        action="data_not_retrieved",
                        message="Authentication required.",
                        status=401
                    )

                # Only allow preview if user is the document owner
                is_owner = (document.owner == request.user) or (
                    document.created_by == request.user)

                if not is_owner:
                    return api_response(
                        action="data_not_retrieved",
                        message="You don't have permission to access this document.",
                        status=403
                    )

        logger.info(
            f"Accessing Document Preview View with parameters - "
            f"User ID: {reference_user_id}, Document ID: {reference_document_id}, "
            f"Signer ID: {reference_signer_id}"
        )

        signer = (
            DocumentSigner.objects.select_related("document")
            .filter(
                document__reference_id=reference_document_id,
                reference_signer_id=reference_signer_id,
                # document__user__reference_id=reference_user_id,
            ).filter(
                Q(document__owner__reference_id=reference_user_id) | Q(
                    document__created_by__reference_id=reference_user_id)
            )
            .first()
        )

        if not signer:
            logger.warning(
                f"Signer not found for User ID: {reference_user_id}, "
                f"Document ID: {reference_document_id}, "
                f"Signer ID: {reference_signer_id}"
            )
            return Response({"error": "Signer or document not found."}, status=404)

        document = signer.document
        if not document or not document.latest_document_file:
            logger.warning(
                f"Document not found or no file associated for "
                f"Document ID: {reference_document_id}"
            )
            return Response({"error": "Document file not found."}, status=404)

        user_obj = document.owner
        str_date_joined = user_obj.date_joined.isoformat()
        str_created_obj = document.created_at.isoformat()
        s3_url = document.latest_document_file.name
        final_str_datetime = f"{str_date_joined}/{str_created_obj}"

        org_doc = (
            OrganizationDocument.objects.filter(user_document=document).first()
        )
        org_name = org_doc.organization.get_decrypted_name(
        ) if org_doc and signer.show_org_name else None

        try:
            logger.info(
                f"Attempting to download and decrypt document data "
                f"from S3 for Document ID: {reference_document_id}"
            )
            decrypted_data = download_and_decrypt_from_s3(s3_url, final_str_datetime)
            encoded_file_data = base64.b64encode(decrypted_data).decode("utf-8")
            logger.debug(
                f"Decrypted document data for Document ID: {reference_document_id}."
            )
        except Exception as e:
            logger.error(
                f"Failed to decrypt document data for Document ID: {reference_document_id} - {e}"
            )
            return Response({"error": "Failed to retrieve document data."}, status=500)

        if device_data:
            source_of_access = determine_access_from_device(device_data)
            if source_of_access == "phone_number":
                message = f"Document opened by {signer.get_decrypted_phone_number()} with phone number."
            elif source_of_access == "email":
                message = (
                    f"Document opened by {signer.get_decrypted_email()} with email."
                )

        ActivityLog.objects.create(
            document_refrence_id=document.reference_id,
            event_type="Opened",
            title=document.title,
            device_data=device_data,
            email=signer.email,
            ip_address=(ip_address if ip_address else None),
            phone_number=signer.phone_number if signer.phone_number else None,
            category="Document Opened",
            message=message,
        )
        log_user_action(
            request=request,
            user=request.user,  # or document.created_by, depending on your logic
            action="Document opened successfully.",
            status="success",
            details={
                "type": "document_opened",
                "owner": document.owner,
                "created_by_id": document.created_by.id if document.created_by else None,
            }
        )

        context = {
            "reference_user_id": reference_user_id,
            "reference_document_id": reference_document_id,
            "reference_signer_id": reference_signer_id,
            "organisation_doc_reference_id": organisation_doc_reference_id,
            "status": signer.status,
            "show_org_name": signer.show_org_name,
            "org_name": org_name,
            "signer_name": signer.get_decrypted_name(),
            "meta_data": signer.meta_data,
            "signer_type": signer.signer_type,
            "is_name_editable": signer.is_name_editable,
            "document_preview": encoded_file_data,
            "signer_email": signer.get_decrypted_email(),
        }

        return api_response(
            action="data_retrieved",
            data=context,
            status="success",
        )


class DocumentDashboardListView(generics.ListAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = OrganizationDocumentSerializer

    def validate_query_params(self, organisation_reference_id=None):
        """
        Validate and handle query parameters with improved error management.

        Args:
            document_type (str): Type of document to filter
            organisation_doc_reference_id (str, optional):
             Reference ID for organization document

        Returns:
            QuerySet: Filtered organization documents

        Raises:
            ValueError: If required parameters are missing or invalid
        """
        # Handle organization document filtering with more robust logic
        user = self.request.user
        user_role = getattr(user, 'role', None)
        organisation_doc_queryset = None
        if not organisation_reference_id:
            return api_response(
                action="data_not_retrieved",
                message="document_organisation_reference_id is not there",
            )
        # organisation_obj = None
        organization_obj = Organization.objects.filter(
            reference_id=organisation_reference_id
        ).first()
        # if not organization_obj:
        #     return api_response(action="data_not_retrieved", message=str(e))
        queryset = OrganizationDocument.objects.none()
        if user_role == "org_superadmin":
            queryset = OrganizationDocument.objects.filter(
                organization=organization_obj,
                user_document__is_deleted=False,
                user_document__is_expired=False,
            )
        elif user_role == "org_admin":
            member_ids = User.objects.filter(
                organizations=organization_obj, role="org_member").values_list('id', flat=True)
            queryset = OrganizationDocument.objects.filter(
                organization=organization_obj,
                user_document__is_deleted=False,
                user_document__is_expired=False,
            ).filter(
                Q(user_document__created_by=user) | Q(
                    user_document__created_by__in=member_ids)
            )
        elif user_role == "org_member":
            queryset = OrganizationDocument.objects.filter(
                organization=organization_obj,
                user_document__created_by=user,
                user_document__is_deleted=False,
                user_document__is_expired=False,
            )
        else:
            queryset = OrganizationDocument.objects.none()

        # if organization_obj:
        #     organisation_doc_queryset = OrganizationDocument.objects.filter(
        #         organization=organization_obj,
        #         # user_document__user=user,
        #         user_document__is_deleted=False,
        #     ).filter(
        #         Q(user_document__owner=user) | Q(user_document__created_by=user)
        #     )

        #     if not organisation_doc_queryset.exists():
        #         logger.warning(
        #             f"No OrganizationDocument found"
        #             f" with reference ID: {organisation_doc_queryset}"
        #         )
        #         # Option to return empty queryset instead of raising an error
        #         return OrganizationDocument.objects.none()

        return queryset.select_related("user_document")

    def list(self, request, *args, **kwargs):
        """
        Enhanced list view with comprehensive error handling and structured response.
        """
        try:
            # Parsing String to Phone number
            document_type = request.query_params.get("type")

            organisation_reference_id = request.query_params.get(
                "organisation_reference_id"
            )
            # Validate and get initial queryset
            initial_queryset = self.validate_query_params(organisation_reference_id)

            # Calculate additional metrics
            total_signed_documents = (
                initial_queryset
                .exclude(
                    id__in=DirectoryDocument.objects.filter(
                        is_active=True
                    ).values_list('document_id', flat=True)
                )
                .filter(
                    user_document__status="sent",
                    user_document__signers__status="signed",
                )
                .distinct()
                .count()
            )
            total_sent_documents = (
                initial_queryset.filter(
                    user_document__status="sent",
                    # user_document__signers__status="pending", # Commetning to count the total sent documents for signing
                )
                .distinct()
                .count()
            )

            # Count metrics
            # twenty_four_hours_ago = timezone.now() - timedelta(days=1)
            # six_days_ago = timezone.now() - timezone.timedelta(days=6)
            metrics = {
                "awaiting_signatures_count": initial_queryset.filter(
                    user_document__status__in=["sent"],
                    user_document__signers__status="pending",
                )
                .distinct()
                .count(),
                # "sent_last_6_days_count": initial_queryset.filter(
                #     user_document__status="sent",
                #     user_document__created_at__gte=six_days_ago,
                # )
                # .distinct()
                # .count(),
                # "recent_documents_count": initial_queryset.filter(
                #     created_at__gte=twenty_four_hours_ago
                #     # Filter for documents created in the last 24 hours
                # )
                # .distinct()
                # .count(),
                "total_signed_documents": total_signed_documents,
                "total_sent_documents": total_sent_documents,
            }

            context = {
                "results": metrics,
            }

            logger.info(f"Successfully retrieved document data: {context}")
            return api_response(action="data_retrieved", status="success", data=context)

        except ValueError as ve:
            logger.error(f"Validation Error: {ve}", exc_info=True, stack_info=True)
            return api_response(action="validation_error", message=str(ve))
        except Exception as e:
            logger.error(f"Unexpected error in document dashboard: {e}")
            return api_response(
                action="server_error",
                message="An unexpected error occurred while processing your request",
            )


class DocumentListView(generics.ListAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = FlattenedDocumentSerializer
    pagination_class = CustomPagination

    def get_queryset(self):
        search_query = self.request.query_params.get("search", "").lower().strip()
        document_type = self.request.query_params.get("type")
        organisation_reference_id = self.request.query_params.get(
            "organisation_reference_id")

        user = self.request.user
        user_role = getattr(user, 'role', None)
        logger.info(
            f"User Role: '{user_role}' of the user: '{user.get_decrypted_email()}'")
        # Only use the organization specified by organisation_reference_id
        if not organisation_reference_id:
            self._no_org_response = api_response(
                action="data_not_retrieved",
                message="organisation_reference_id is required."
            )
            return OrganizationDocument.objects.none()
        organizations = Organization.objects.filter(
            reference_id=organisation_reference_id)
        if not organizations.exists():
            self._no_org_response = api_response(
                action="data_not_retrieved",
                message="No organization found for the given organisation_reference_id."
            )
            return OrganizationDocument.objects.none()

        # 1. Role-based filtering (base queryset)
        queryset = OrganizationDocument.objects.none()
        if user_role == "org_superadmin":
            queryset = OrganizationDocument.objects.filter(
                organization__in=organizations,
                user_document__is_expired=False,
            ).select_related("user_document").prefetch_related("user_document__signers")
        elif user_role == "org_admin":
            member_ids = User.objects.filter(
                organizations__in=organizations, role="org_member").values_list('id', flat=True)
            # Documents created by org_members in the organization
            org_member_created_docs = OrganizationDocument.objects.filter(
                organization__in=organizations,
                user_document__is_deleted=False,
                user_document__is_expired=False,
                user_document__created_by__in=member_ids
            )

            # Documents created by the org_admin user
            user_created_docs = OrganizationDocument.objects.filter(
                organization__in=organizations,
                user_document__is_deleted=False,
                user_document__is_expired=False,
                user_document__created_by=user
            )
            # Combine both querysets and remove duplicates
            queryset = (org_member_created_docs | user_created_docs).select_related(
                "user_document").prefetch_related("user_document__signers").distinct()
        elif user_role == "org_member":
            queryset = OrganizationDocument.objects.filter(
                organization__in=organizations,
                user_document__created_by=user,
                user_document__is_expired=False,
            ).select_related("user_document").prefetch_related("user_document__signers")
        else:
            queryset = OrganizationDocument.objects.none()

        documents_in_directories = DirectoryDocument.objects.filter(
            is_active=True).values_list('document_id', flat=True)
        queryset = queryset.exclude(id__in=documents_in_directories)

        # 2. Existing document_type/status/search filtering logic (apply on top of role-based queryset)
        if document_type == "inbox":
            queryset = queryset.filter(
                user_document__is_deleted=False,
            ).exclude(
                user_document__document_type='proposal'  # Exclude proposals from inbox
            )
            queryset = (
                queryset.annotate(
                    approved_signers_count=Count(
                        "user_document__signers",
                        filter=Q(user_document__signers__status="signed"),
                    )
                )
                .filter(
                    approved_signers_count=Count("user_document__signers"),
                    user_document__signers__isnull=False,
                )
                .distinct()
            )
            log_user_action(
                request=self.request,
                user=self.request.user,
                action="Document retrieved from inbox successfully.",
                status="success",
                details={"type": "document_retrieved"},
            )
        elif document_type == "sent":
            queryset = queryset.filter(
                user_document__status__in=["sent", "withdrawn"],
                user_document__is_deleted=False,
                user_document__signers__status="pending",
            ).exclude(
                user_document__document_type='proposal'  # Exclude proposals from sent
            ).distinct()
            log_user_action(
                request=self.request,
                user=self.request.user,
                action="Document retrieved from sent successfully.",
                status="success",
                details={"type": "document_retrieved"},
            )
        elif document_type == "draft":
            queryset = queryset.filter(
                user_document__status="draft",
                user_document__is_deleted=False,
            ).exclude(
                user_document__document_type='proposal'  # Exclude proposals from draft
            )
            log_user_action(
                request=self.request,
                action="Document retrieved from draft successfully.",
                status="success",
                details={"type": "document_retrieved"},
            )
        elif document_type == "trash":
            queryset = queryset.filter(
                user_document__status="trash",
                user_document__is_deleted=False,
            ).exclude(
                user_document__document_type='proposal'  # Exclude proposals from trash
            )  # Handle trash status if needed
        elif document_type:
            # If an unknown type is provided, return none
            logger.warning(f"Invalid document type: {document_type}")
            return OrganizationDocument.objects.none()

        # Now apply search filtering if search query exists
        if search_query:
            queryset = queryset.filter(
                Q(user_document__title__icontains=search_query) |
                Q(user_document__signers__name__icontains=search_query) |
                Q(user_document__signers__email__icontains=search_query) |
                Q(user_document__signers__phone_number__icontains=search_query) |
                Q(user_document__status__icontains=search_query)
            )

        return queryset

    def get_paginated_response(self, data):
        pagination = self.paginator.get_paginated_response(data)
        return api_response(
            action="data_retrieved",
            status="success",
            data={
                "results": pagination.data["data"],
                "pagination": {
                    "count": pagination.data["count"],
                    "next": pagination.data["next"],
                    "previous": pagination.data["previous"],
                },
            },
        )

    def list(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())

            # If _no_org_response is set, return it
            if hasattr(self, '_no_org_response'):
                return self._no_org_response
            page = self.paginate_queryset(queryset)

            if page is not None:
                organisation_reference_id = request.query_params.get(
                    "organisation_reference_id")
                organization_obj = Organization.objects.filter(
                    reference_id=organisation_reference_id).first()
                org_superadmin = organization_obj.get_organization_owner() if organization_obj else None
                user_role = getattr(request.user, 'role', None)
                serializer = self.get_serializer(page, many=True, context={**self.get_serializer_context(
                ), 'organization': organization_obj, 'org_superadmin': org_superadmin, 'user_role': user_role})
                try:
                    data = []
                    for item in serializer.data:
                        if item:  # Only include non-empty items
                            data.append(item)

                    # Get sorting parameters
                    sort_by = request.query_params.get("sort_by", "")
                    sort_order = request.query_params.get("sort_order", "asc")

                    # Sort the data if parameters are provided
                    if sort_by and data:
                        reverse = sort_order.lower() == "desc"
                        if sort_by.lower() == "name":
                            data = sorted(
                                data,
                                key=lambda x: x.get("title", "").lower(),
                                reverse=reverse
                            )
                        elif sort_by.lower() == "date":
                            data = sorted(
                                data,
                                key=lambda x: datetime.datetime.strptime(
                                    x.get("created_at", ""), "%d %b %Y"),
                                reverse=reverse
                            )

                    return self.get_paginated_response(data)
                except serializers.SkipField:
                    # Handle skipped fields by returning empty list for that item
                    return self.get_paginated_response([])
                except Exception as e:
                    logger.error(f"Error processing serialized data: {str(e)}")
                    return api_response(
                        action="data_not_retrieved",
                        message="Error processing document data"
                    )

            serializer = self.get_serializer(queryset, many=True)
            return api_response(
                action="data_retrieved",
                status="success",
                data={"results": serializer.data},
            )

        except ValueError as e:
            logger.error(f"Error fetching queryset: {e}")
            return api_response(action="data_not_retrieved", message=str(e))
        except Exception as e:
            logger.exception(f"An unexpected error occurred: {e}")
            return api_response(
                action="server_error", message="An unexpected error occurred."
            )


class DocumentCountsAPIView(generics.ListAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        organisation_reference_id = self.request.query_params.get(
            "organisation_reference_id")
        if not organisation_reference_id:
            return OrganizationDocument.objects.none()
        user = self.request.user
        user_role = getattr(user, 'role', None)
        organization_obj = Organization.objects.filter(
            reference_id=organisation_reference_id).first()
        if not organization_obj:
            return OrganizationDocument.objects.none()

        queryset = OrganizationDocument.objects.none()
        if user_role == "org_superadmin":
            queryset = OrganizationDocument.objects.filter(
                organization=organization_obj,
                user_document__is_deleted=False,
                user_document__is_expired=False,
            )
        elif user_role == "org_admin":
            member_ids = User.objects.filter(
                organizations=organization_obj, role="org_member").values_list('id', flat=True)
            queryset = OrganizationDocument.objects.filter(
                organization=organization_obj,
                user_document__is_deleted=False,
                user_document__is_expired=False,
            ).filter(
                Q(user_document__created_by=user) | Q(
                    user_document__created_by__in=member_ids)
            )
        elif user_role == "org_member":
            queryset = OrganizationDocument.objects.filter(
                organization=organization_obj,
                user_document__created_by=user,
                user_document__is_deleted=False,
                user_document__is_expired=False,
            )
        else:
            queryset = OrganizationDocument.objects.none()

        documents_in_directories = DirectoryDocument.objects.filter(
            is_active=True).values_list('document_id', flat=True)
        queryset = queryset.exclude(id__in=documents_in_directories)
        return queryset

    def get(self, request, *args, **kwargs):
        """
        Returns the counts of documents in inbox, sent, draft, and trash statuses, role-aware.
        """
        organisation_reference_id = self.request.query_params.get(
            "organisation_reference_id"
        )

        if not organisation_reference_id:
            return api_response(
                action="data_not_retrieved",
                message="organisation_reference_id is not provided",
            )

        user = self.request.user
        user_role = getattr(user, 'role', None)
        organization_obj = Organization.objects.filter(
            reference_id=organisation_reference_id
        ).first()

        if not organization_obj:
            return api_response(
                action="data_not_retrieved",
                message="Organization not found",
            )

        queryset = self.get_queryset()

        # Calculate the counts using annotation and aggregation
        document_counts = {
            "inbox_count": queryset.annotate(
                approved_signers_count=Count(
                    "user_document__signers",
                    filter=Q(user_document__signers__status="signed"),
                )
            )
            .filter(
                approved_signers_count=Count("user_document__signers"),
                user_document__signers__isnull=False,
            )
            .exclude(
                user_document__document_type='proposal'  # Exclude proposals from inbox
            )
            .distinct()
            .count(),
            "sent_count": queryset.filter(
                user_document__status__in=["sent", "withdrawn"], user_document__signers__status="pending"
            )
            .exclude(
                user_document__document_type='proposal'  # Exclude proposals from sent
            )
            .distinct()
            .count(),
            "draft_count": queryset.filter(user_document__status="draft")
            .exclude(
                user_document__document_type='proposal'  # Exclude proposals from draft
            )
            .distinct()
            .count(),
            "trash_count": queryset.filter(user_document__status="trash")
            .exclude(
                user_document__document_type='proposal'  # Exclude proposals from trash
            )
            .distinct()
            .count(),
        }

        log_user_action(
            request=self.request,
            user=self.request.user,
            action="Document counts retrieved successfully.",
            status="success",
            details={"type": "document_counts_retrieved"},
        )

        return api_response(
            action="data_retrieved",
            status="success",
            data=document_counts,
        )


class DocumentPreviewSignerView(generics.ListAPIView):
    serializer_class = DocumentSignerPreviewSerializer

    def get_queryset(self):
        # Get query params
        # Retrieve reference_id from kwargs

        organisation_doc_reference_id = self.request.query_params.get(
            "organisation_doc_reference_id", None
        )
        document_reference_id = self.request.query_params.get("doc_reference_id", None)
        if not organisation_doc_reference_id:
            return api_response(
                action="data_not_retrieved",
                message="document_organisation_reference_id is not there",
            )

        user_document = UserDocument.objects.filter(
            reference_id=document_reference_id
        ).first()

        queryset = (
            OrganizationDocument.objects.filter(
                encrypted_reference_id=organisation_doc_reference_id,
                user_document=user_document,
            )
            .select_related("user_document")
            .prefetch_related("user_document__signers")
        )

        # Apply the document_type filter if provided
        # user_document__signers__isnull=False

        queryset = queryset.filter(
            user_document__status="sent", user_document__is_deleted=False
        )

        return queryset

    def list(self, request, *args, **kwargs):
        try:
            verification_token = request.query_params.get("verification_token")
            reference_document_id = request.query_params.get("doc_reference_id")
            reference_signer_id = request.query_params.get("reference_signer_id")

            if not all([verification_token, reference_document_id, reference_signer_id]):
                return api_response(
                    action="data_not_retrieved",
                    message="Required parameters missing",
                    status=400,
                )

            # Get document and signer
            document = UserDocument.objects.get(reference_id=reference_document_id)
            signer = DocumentSigner.objects.get(reference_signer_id=reference_signer_id)

            # Verify token using database
            if not DocumentVerificationService.verify_token(document, signer, verification_token):
                return api_response(
                    action="data_not_retrieved",
                    message="Verification expired or invalid. Please verify again.",
                    status=403,
                )

            queryset = self.filter_queryset(self.get_queryset())
            serializer = self.get_serializer(
                queryset, many=True, context={'request': request})

            try:
                user_obj = document.owner
                str_date_joined = user_obj.date_joined.isoformat()
                str_created_obj = document.created_at.isoformat()
                s3_url = document.latest_document_file.name
                final_str_datetime = f"{str_date_joined}/{str_created_obj}"
                logger.info(
                    f"Attempting to download and decrypt document data "
                    f"from S3 for Document ID: {reference_document_id}"
                )
                decrypted_data = download_and_decrypt_from_s3(
                    s3_url, final_str_datetime)
                encoded_file_data = base64.b64encode(decrypted_data).decode("utf-8")
                logger.debug(
                    f"Decrypted document data for Document ID: {reference_document_id}."
                )
            except Exception as e:
                logger.error(
                    f"Failed to decrypt document data for Document ID: {reference_document_id} - {e}"
                )
                return Response({"error": "Failed to retrieve document data."}, status=500)

            signers = [
                signer
                for item in serializer.data
                for signer in item["signers"]
            ]

            return api_response(
                action="data_retrieved",
                status="success",
                data={
                    "signers": signers,
                    "document_preview": encoded_file_data
                },
            )
        except ValueError as e:
            logger.error(f"Error fetching queryset: {e}")
            return api_response(action="data_not_retrieved", message=str(e))
        except Exception as e:
            logger.exception(f"An unexpected error occurred: {e}")
            return api_response(
                action="server_error", message="An unexpected error occurred."
            )


class DocumentTrashView(generics.UpdateAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def update(self, request, *args, **kwargs):
        try:
            queryset = None
            doc_reference_id = request.query_params.get("doc_reference_id", None)
            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id", None
            )

            if not doc_reference_id:
                return api_response(status="failed", message="No document IDs provided")
            if organisation_doc_reference_id:
                user_document = UserDocument.objects.filter(
                    reference_id=doc_reference_id
                ).first()
                queryset = OrganizationDocument.objects.filter(
                    encrypted_reference_id=organisation_doc_reference_id,
                    user_document=user_document,
                ).select_related("user_document")

            # Using transaction.atomic() to ensure atomicity
            with transaction.atomic():
                # Find the document
                document = queryset.filter(
                    user_document__reference_id=doc_reference_id,
                    # user=request.user.reference_id,
                    user_document__status__in=["draft", "sent"],
                    user_document__is_deleted=False,
                    # Only move certain statuses to trash
                )

                if not document.exists():
                    return api_response(
                        action="data_not_retrieved",
                        message="No valid document found to move to trash",
                    )

                document = document.first()  # Get the first document

                # Move to trash using custom method
                document.user_document.move_to_trash()

                logger.info(
                    f"Moved {document.user_document.title}" f" document to trash"
                )

            # Success response after transaction is committed
            return api_response(
                action="data_deleted",
                status="success",
                message=f"{document.user_document.title} "
                f"document(s) moved to trash",
            )

        except Exception as e:
            # Log and return an error response
            logger.error(f"Error moving document to trash: {e}")
            return api_response(
                action="data_not_updated",
                message="An error occurred while moving the document to trash",
            )


class RestoreFromTrashView(generics.UpdateAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def update(self, request, *args, **kwargs):
        try:
            doc_reference_id = request.query_params.get("document_reference_id", None)
            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id", None
            )
            if not doc_reference_id:
                return api_response(status="failed", message="No document IDs provided")

            if organisation_doc_reference_id:
                user_document = UserDocument.objects.filter(
                    reference_id=doc_reference_id
                ).first()
                queryset = OrganizationDocument.objects.filter(
                    encrypted_reference_id=organisation_doc_reference_id,
                    user_document=user_document,
                ).select_related("user_document")

                # Using transaction.atomic() to ensure atomicity
            with transaction.atomic():
                # Find the document
                document = queryset.filter(
                    user_document__reference_id=doc_reference_id,
                    # user=request.user.reference_id,
                    user_document__status="trash",
                    # Only move certain statuses to draft
                    user_document__is_deleted=False,
                )

                if not document.exists():
                    return api_response(
                        action="data_not_retrieved",
                        message="No valid document found to move to previous status",
                    )

                document = document.first()  # Get the first document

                # Move to trash using custom method
                document.user_document.restore_from_trash()

                logger.info(
                    f"Moved {document.user_document.title} "
                    f"document move to previous status"
                )

            # Log the document restoration action
            log_user_action(
                request=request,
                user=request.user,
                action="document_restoration",
                status="success",
                details={
                    "document_id": document.user_document.id,
                    "document_title": document.user_document.title,
                },
            )

            # Success response after transaction is committed
            return api_response(
                action="data_updated",
                status="success",
                message=f"{document.user_document.title} "
                f"document(s) move to previous status",
            )

        except Exception as e:
            # Log and return an error response
            logger.error(f"Error moving document to previous status: {e}")
            return api_response(
                action="data_not_updated",
                message="An error occurred while moving"
                        " the document to previous status",
            )


class DeleteFromTrashView(generics.DestroyAPIView):
    """
    View to permanently delete documents from trash
    """

    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def destroy(self, request, *args, **kwargs):
        try:
            # Get query params
            doc_reference_id = request.query_params.get("document_reference_id")
            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id"
            )
            status = request.query_params.get("status")

            # Validate required document reference id
            if not doc_reference_id:
                return api_response(status="failed", message="No document provided")

            # Initialize the document variable to prevent reference errors
            document = None
            # If organisation document reference ID is provided, filter accordingly
            if organisation_doc_reference_id:
                user_document = UserDocument.objects.filter(
                    reference_id=doc_reference_id
                ).first()
                queryset = OrganizationDocument.objects.filter(
                    encrypted_reference_id=organisation_doc_reference_id,
                    user_document=user_document,
                ).select_related("user_document")

            if status == "document_terminate":
                value = document_terminate(queryset)

                if not value:
                    return api_response(
                        action="data_not_retrieved",
                        message="No valid documents found",
                    )

                document = queryset.filter(
                    user_document__reference_id=doc_reference_id,
                    user_document__is_deleted=False,
                )

                if document.exists():
                    document = document.first()  # Get the document instance
                    document.user_document.move_to_delete()  # Mark the document as deleted
                    send_document_delete_mail(
                        document.user_document.reference_id, "Terminate"
                    )
                    document.save()

                    # Send email after the document is successfully deleted

                    log_user_action(
                        request=request,
                        user=request.user,
                        action="Document terminated successfully.",
                        status="success",
                        details={"type": "document_deleted"},
                    )
                    return api_response(
                        action="data_deleted",
                        status="success",
                        message="Document terminated and permanently deleted.",
                    )
                else:
                    return api_response(
                        action="data_not_retrieved",
                        message="No valid documents found",
                    )
            elif status == "document_withdraw":  # Handle document withdrawal
                value = document_withdraw(queryset)

                if not value:
                    return api_response(
                        action="data_not_retrieved",
                        message="No valid documents found",
                    )

                document = queryset.filter(
                    user_document__reference_id=doc_reference_id,
                    user_document__is_deleted=False,
                )

                if document.exists():
                    document = document.first()  # Get the document instance
                    # Mark the document as deleted and status to withdrawn
                    document.user_document.withdraw_document(request.user)
                    send_document_delete_mail(
                        document.user_document.reference_id, "Withdrawn"
                    )
                    document.save()

                    # Send email after the document is successfully deleted
                    log_user_action(
                        request=request,
                        user=request.user,
                        action="Document withdrawn successfully.",
                        status="success",
                        details={"type": "document_deleted"},
                    )
                    return api_response(
                        action="data_deleted",
                        status="success",
                        message="Document withdrawn.",
                    )
                else:
                    return api_response(
                        action="data_not_retrieved",
                        message="No valid documents found",
                    )
            # Default action (if no specific status is provided)
            else:
                document = queryset.filter(
                    user_document__reference_id=doc_reference_id,
                    user_document__is_deleted=False,
                )

                if document.exists():
                    document = document.first()
                    document.user_document.move_to_delete(request.user)
                    document.save()
                    log_user_action(
                        request=request,
                        user=request.user,
                        action="Document deleted successfully.",
                        status="success",
                        details={"type": "document_deleted"},
                    )
                    return api_response(
                        action="data_deleted",
                        status="success",
                        message="Document permanently deleted.",
                    )
            return api_response(
                action="data_not_deleted",
                status="failed",
                message="No valid action performed on the document."
            )
        except Exception as e:
            logger.error(f"Error permanently deleting documents: {e}")
            return api_response(
                action="data_not_deleted",
                message="An error occurred while deleting documents",
            )


class DocumentPreiviewView(generics.ListAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = DocumentPreiviewViewSerializer

    def get_queryset(self):
        doc_reference_id = self.request.query_params.get("doc_reference_id", None)
        organisation_doc_reference_id = self.request.query_params.get(
            "organisation_doc_reference_id", None
        )
        if not doc_reference_id or not organisation_doc_reference_id:
            return api_response(
                action="data_not_retrieved",
                message="doc_reference_id or organisation_doc_reference_id not provided",
            )

        user_document = UserDocument.objects.filter(
            reference_id=doc_reference_id
        ).first()
        if not user_document:
            return api_response(
                action="data_not_retrieved",
                message="Document not found",
            )
        queryset = (
            OrganizationDocument.objects.filter(
                encrypted_reference_id=organisation_doc_reference_id,
                user_document=user_document,
            )
            .select_related("user_document")
            .prefetch_related("user_document__signers")
        )
        return queryset

    def list(self, request, *args, **kwargs):
        context = {"request": request}
        queryset = self.filter_queryset(self.get_queryset())
        if not queryset:
            return api_response(
                action="data_not_retrieved", message="No documents found"
            )
        serializer = self.get_serializer(queryset, many=True, context=context)
        return api_response(
            action="data_retrieved",
            status="success",
            data={"results": serializer.data},
        )


def remove_nulls(data):
    """Recursively remove null values from a dictionary or list."""
    if isinstance(data, dict):
        return {k: remove_nulls(v) for k, v in data.items() if v is not None}
    elif isinstance(data, list):
        return [remove_nulls(item) for item in data]
    else:
        return data


class ActivityLogDownloadView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        try:

            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id"
            )
            doc_reference_id = request.query_params.get("doc_reference_id")

            if not organisation_doc_reference_id or not doc_reference_id:
                return api_response(
                    action="data_not_retrieved",
                    message="Required parameters are missing",
                )

            user_document = UserDocument.objects.filter(
                reference_id=doc_reference_id
            ).first()
            if not user_document:
                return api_response(
                    action="data_not_retrieved",
                    message=f"No UserDocument found with reference ID: {doc_reference_id}",
                )

            organisation_doc_obj = OrganizationDocument.objects.filter(
                encrypted_reference_id=organisation_doc_reference_id,
                user_document=user_document,
            ).first()
            if not organisation_doc_obj:
                return api_response(
                    action="data_not_retrieved",
                    message=f"No OrganizationDocument found with reference ID: {organisation_doc_reference_id}",
                )

            queryset = ActivityLog.objects.filter(
                document_refrence_id=organisation_doc_obj.user_document.reference_id
            ).order_by("timestamp")

            serializer = ActivityLogSerializer(queryset, many=True)
            activities = remove_nulls(serializer.data)

            processed_activities = []
            if activities:
                for activity_group in activities:
                    for date, activity in activity_group.items():
                        processed_activities.append(
                            {
                                "date": date,
                                "time": activity.get("time", ""),
                                "event_type": activity.get("event_type", ""),
                                "message": activity.get("message", ""),
                            }
                        )

            html_content = generate_html_template(
                user_document.title, processed_activities
            )

            # Generate filename
            current_timestamp = now().strftime("%Y%m%d_%H%M%S")
            filename = f"activity_logs/{organisation_doc_reference_id}_{current_timestamp}.pdf"

            # Convert HTML to PDF using weasyprint
            pdf_content = HTML(string=html_content).write_pdf()

            # Create directory if it doesn't exist
            directory = os.path.join(settings.MEDIA_ROOT, 'activity_logs')
            os.makedirs(directory, exist_ok=True)

            # Save directly to S3 using default_storage (which is configured for S3)
            with default_storage.open(filename, 'wb') as f:
                f.write(pdf_content)

            # Generate the public URL for the saved PDF
            pdf_url = (
                f"{settings.MEDIA_URL}{filename}"
            )
            log_user_action(
                request=request,
                user=request.user,
                action="Document activity logs downloaded successfully.",
                status="success",
                details={"type": "document_activity_logs_downloaded"},
            )
            # Return the URL of the PDF to the frontend
            return api_response(
                action="data_retrieved",
                status="success",
                data={"pdf_url": pdf_url},
            )

        except Exception as e:
            logger.error(f"Failed to generate activity log PDF: {str(e)}")
            return api_response(
                action="activity_log_generation_failed",
                message="Failed to generate activity log PDF",
                status=500,
            )


def generate_html_template(document_title, activities):
    """Generate the HTML template with activities"""
    activities_html = ""
    for activity in activities:
        activities_html += f"""
            <li class="activity-item">
                <div class="icon {activity['event_type']}"></div>
                <div class="content">
                    <div class="timestamp">{activity['date']} {activity['time']}</div>
                    <div class="description">{activity['message']}</div>
                </div>
            </li>
        """

    return f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Activity Log - {document_title}</title>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet">
        <style>
            @page {{
                size: A4;
                margin: 2cm;
            }}
            body {{
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
            }}
            .container {{
                max-width: 100%;
                margin: 0 auto;
            }}
            .header {{
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #333;
            }}
            .document-title {{
                font-size: 18px;
                color: #666;
                margin-bottom: 30px;
            }}
            .activity-log {{
                list-style: none;
                padding: 0;
            }}
            .activity-item {{
                display: flex;
                margin-bottom: 20px;
                padding-bottom: 20px;
                border-bottom: 1px solid #eee;
            }}
            .icon {{
                width: 40px;
                height: 40px;
                text-align: center;
                line-height: 40px;
                border-radius: 50%;
                margin-right: 15px;
                font-size: 20px;
                color: white;
            }}
            .icon.Create {{
                background-color: #4caf50;
            }}
            .icon.Sent {{
                background-color: #2196f3;
            }}
            .icon.Opened {{
                background-color: #ff9800;
            }}
            .icon.Approved {{
                background-color: #9c27b0;
            }}
            .icon.Completed {{
                background-color: #4CAF50;
            }}
            .content {{
                flex: 1;
            }}
            .timestamp {{
                font-size: 14px;
                color: #888;
                margin-bottom: 5px;
            }}
            .description {{
                font-size: 16px;
            }}

            /* Font Awesome icons */
            .icon.Create:before {{
                content: "\\f00c"; /* Check icon */
                font-family: 'Font Awesome 5 Free';
                font-weight: 900;
            }}
            .icon.Sent:before {{
                content: "\\f1d8"; /* Paper plane icon */
                font-family: 'Font Awesome 5 Free';
                font-weight: 900;
            }}
            .icon.Opened:before {{
                content: "\\f06e"; /* Eye icon */
                font-family: 'Font Awesome 5 Free';
                font-weight: 900;
            }}
            .icon.Approved:before {{
                content: "\\f0ae"; /* Thumbs-up icon */
                font-family: 'Font Awesome 5 Free';
                font-weight: 900;
            }}
            .icon.Completed:before {{
                content: "\\f058"; /* Check-circle icon */
                font-family: 'Font Awesome 5 Free';
                font-weight: 900;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">Activity Log</div>
            <div class="document-title">Document: {document_title}</div>
            <ul class="activity-log">
                {activities_html}
            </ul>
        </div>
    </body>
    </html>
    """


class DocumentVerificationThrottle(AnonRateThrottle):
    rate = "5/hour"


class DocumentVerificationView(APIView):
    authentication_classes = [OptionalJWTAccessTokenAuthentication]  # Add this line

    def post(self, request, reference_user_id, reference_document_id, reference_signer_id):
        """Initiate document verification by sending OTP"""
        try:
            logger.info(
                f"Starting verification process for user: {reference_user_id}, document: {reference_document_id}, signer: {reference_signer_id}")
            owner = request.query_params.get("is_owner", False)
            logger.info(f"Owner verification requested: {owner}")

            # Get the verification token from query params if not authenticated and available.
            verification_token = request.query_params.get("verification_token")

            # Check if already verified using database
            try:
                document = UserDocument.objects.get(reference_id=reference_document_id)
                signer = DocumentSigner.objects.get(
                    reference_signer_id=reference_signer_id)
                logger.info(f"Found document and signer in database")
            except (UserDocument.DoesNotExist, DocumentSigner.DoesNotExist) as e:
                logger.error(f"Document or signer not found: {str(e)}")
                return api_response(
                    action="verification_failed",
                    message="You can't access this document.",
                    status=404
                )
            # Check if the document is deleted or withdrawn and return appropriate error messages
            document_status, msg = check_document_status(user_document=document)
            logger.info(
                f"Document Name is: {document.title}, Document Status: {document_status}, And error message is: {msg}")
            if not document_status:
                return api_response(
                    action="data_not_retrieved",
                    message=msg,
                    status=404
                )

            if document.is_agreement:
                return api_response(
                    action="data_retrieved",
                    message="Document is an agreement",
                    data={"verified": True},
                    status="success",
                )

            # If user is authenticated, return success for document preview.
            if request.user.is_authenticated:
                if (document.owner == request.user) or (document.created_by == request.user):
                    logger.info(f"User is authenticated")
                    return api_response(
                        action="data_retrieved",
                        message="User is authenticated",
                        data={"verified": True},
                        status="success",
                    )
                else:
                    return api_response(
                        action="data_not_retrieved",
                        message="You can't access this document.",
                        status=404
                    )

            verification_data = DocumentVerificationService.get_verification_data(
                document=document,
                signer=signer
            )

            if verification_data and verification_data.is_verified:
                logger.info(
                    f"Document already verified for signer: {reference_signer_id}, verification token: {verification_token} for preview")

                if DocumentVerificationService.verify_token(document, signer, verification_token):
                    logger.info(
                        f"Verification of existing verification token successful")
                    return api_response(
                        action="data_retrieved",
                        message="Document already verified",
                        data={"verified": True},
                        status="success",
                    )

            # If not authenticated and no verification token, generate OTP and send it to the signer.
            signer = (
                DocumentSigner.objects.select_related("document")
                .filter(
                    document__reference_id=reference_document_id,
                    reference_signer_id=reference_signer_id,
                    # document__user__reference_id=reference_user_id,
                    document__created_by__reference_id=reference_user_id,
                )
                .first()
            )

            if not signer:
                logger.error(f"Signer not found with provided IDs")
                return api_response(
                    action="verification_failed",
                    message="Signer not found",
                    status=404,
                    data={"verified": False}

                )

            logger.info(f"Generating OTP for signer: {reference_signer_id}")
            otp = DocumentVerificationService.generate_otp()

            logger.info(f"OTP generated For Document Signature Verification: {otp}")

            # Store OTP in database
            try:
                verification_data = DocumentVerificationService.create_or_update_otp(
                    document=signer.document,
                    signer=signer,
                    otp=otp
                )
                logger.info(
                    f"OTP stored successfully for signer: {reference_signer_id}")
            except Exception as e:
                logger.error(f"Failed to store OTP: {str(e)}")
                raise

            # if signer.get_decrypted_email():
            #     receiver_name = (
            #         f"{signer.document.user.first_name} {signer.document.user.last_name}"
            #         if signer.document.user.first_name and signer.document.user.last_name
            #         else signer.document.user.get_decrypted_email()
            #     )

            phone_number = signer.get_decrypted_phone_number(
            ) if not owner else signer.document.created_by.get_decrypted_phone_number()
            receiver_name = signer.get_decrypted_name(
            ) if not owner else signer.document.created_by.get_full_name()

            if phone_number:
                try:
                    send_otp_via_sms(receiver_name=receiver_name,
                                     phone_number=phone_number, otp=otp)
                    logger.info(f"OTP sent via SMS to {phone_number}")
                except Exception as e:
                    logger.error(f"Failed to send OTP via SMS: {str(e)}")
                    raise

            logger.info(
                f"Verification process completed successfully for signer: {reference_signer_id}")

            return api_response(
                action="data_created",
                message="OTP sent successfully",
                data={
                    "message": "Please check your email/Phone number for OTP",
                    "expires_in": settings.OTP_EXPIRY_SECONDS,
                    "email": signer.get_decrypted_email(),
                    **({"otp": otp} if settings.DEBUG else {}),
                    "phone_number": signer.get_decrypted_phone_number(),
                },
                status="success",
            )

        except Exception as e:
            logger.error(f"Verification initiation failed: {str(e)}", exc_info=True)
            return api_response(
                action="data_not_created",
                message="Failed to initiate verification",
            )

    def put(self, request, reference_user_id, reference_document_id, reference_signer_id):
        """Verify OTP and generate access token"""
        try:
            document = UserDocument.objects.get(reference_id=reference_document_id)
            signer = DocumentSigner.objects.get(reference_signer_id=reference_signer_id)
            provided_otp = request.data.get("otp")
            user = User.objects.get(reference_id=reference_user_id)

            document_status, msg = check_document_status(user_document=document)
            if not document_status:
                return api_response(
                    action="data_not_retrieved",
                    message=msg,
                    status=404
                )

            if not DocumentVerificationService.verify_otp(document, signer, provided_otp):
                return api_response(
                    action="verification_failed",
                    message="Invalid OTP",
                    status=400
                )

            # Generate and store verification token
            verification_token, _ = DocumentVerificationService.create_verification_token(
                document=document,
                signer=signer
            )

            user_token = save_user_details(
                user.get_decrypted_email(), is_onetime=False, request=request)

            log_user_action(
                request=request,
                user=user,
                action="Document verified successfully.",
                status="success",
                details={"type": "document_verified"},
            )

            logger.info(f"Verification successful for signer: {reference_signer_id}")

            return api_response(
                action="data_updated",
                data={
                    "verification_token": verification_token,
                    "token": user_token,
                    "expires_in": settings.TOKEN_EXPIRY_SECONDS,
                },
                status="success",
            )

        except Exception as e:
            logger.error(f"Verification failed: {str(e)}")
            return api_response(
                action="verification_failed",
                message="Verification failed",
                status=400
            )


class DocumentReminderView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        try:
            user_id = request.user.reference_id

            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id"
            )
            doc_reference_id = request.query_params.get("doc_reference_id")
            reference_signer_id = request.data.get("reference_signer_id")  # Optional
            send_all = request.query_params.get(
                "send_all", False
            )  # Flag to send to all pending signers

            user_obj = UserDocument.objects.filter(
                reference_id=doc_reference_id
            ).first()

            org_doc = OrganizationDocument.objects.filter(
                encrypted_reference_id=organisation_doc_reference_id,
                user_document=user_obj,
            ).first()

            document_response = org_doc.user_document

            if not org_doc:
                return api_response(
                    action="data_not_retrieved",
                    message="Organization document not found",
                    status=404,
                )

            # Get document signers query
            signers_query = DocumentSigner.objects.filter(
                document__reference_id=org_doc.user_document.reference_id,
                status="pending",  # Only pending signers
            )

            # Check last reminder time (don't send if reminder sent in last 24 hours)
            time_threshold = timezone.now() - timedelta(hours=24)

            signers_query = signers_query.filter(
                Q(last_reminder_sent__isnull=True)
                | Q(last_reminder_sent__lt=time_threshold)
            )

            if not send_all and reference_signer_id:
                # Send reminder to specific signer
                signers_query = signers_query.filter(
                    reference_signer_id=reference_signer_id
                )

            signers_to_remind = signers_query.select_related("document")

            if not signers_to_remind.exists():
                return api_response(
                    action="data_not_retrieved",
                    message="No eligible signers found for reminder please resend after 24 hours",
                    status=400,
                )

            reminder_results = []
            for signer in signers_to_remind:
                try:
                    # decrypted_phone_number = signer.get_decrypted_phone_number()
                    decrypted_email = signer.get_decrypted_email()
                    signer_name = signer.get_decrypted_name()

                    # Send SMS with verification link
                    # try:
                    #     sms_status = send_reminder_link_via_sms(
                    #         receiver_name=signer.get_decrypted_name(),
                    #         phone=decrypted_phone_number,
                    #         reference_signer_id=signer.reference_signer_id,
                    #         org_doc_reference_id=organisation_doc_reference_id,
                    #         user_id=user_id,
                    #         document=document_response.reference_id,
                    #     )
                    #     sms_status = "success"  # If no exception raised, assume success
                    # except Exception as sms_e:
                    #     logger.error(
                    #         f"Failed to send SMS to {signer.reference_signer_id}: {str(sms_e)}"
                    #     )
                    #     sms_status = f"failed: {str(sms_e)}"

                    # Send reminder email
                    try:
                        send_background_email.delay(
                            email=decrypted_email,
                            receiver_name=signer_name,
                            reference_signer_id=signer.reference_signer_id,
                            org_doc_reference_id=organisation_doc_reference_id,
                            user_id=user_id,
                            document=document_response.reference_id,
                            status="reminder",
                            language=document_response.language,  # Pass the language preference
                        )
                        email_status = (
                            "success"  # If no exception raised, assume success
                        )
                    except Exception as email_e:
                        logger.error(
                            f"Failed to send reminder email to {signer.reference_signer_id}: {str(email_e)}"
                        )
                        email_status = f"failed: {str(email_e)}"

                    # Update last reminder sent time
                    signer.last_reminder_sent = timezone.now()
                    signer.reminder_count = (signer.reminder_count or 0) + 1
                    signer.save()
                    sms_status = None

                    reminder_results.append(
                        {
                            "signer_id": signer.reference_signer_id,
                            "email": decrypted_email,
                            "sms_status": sms_status,
                            "email_status": email_status,
                        }
                    )
                    log_user_action(
                        request=request,
                        user=request.user,
                        action="Document reminder sent successfully.",
                        status="success",
                        details={"type": "document_reminder_sent"},
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to process reminder for {signer.reference_signer_id}: {str(e)}"
                    )
                    reminder_results.append(
                        {
                            "signer_id": signer.reference_signer_id,
                            "status": "failed",
                            "error": str(e),
                        }
                    )

            return api_response(
                action="data_retrieved",
                message="Reminder process completed",
                data=reminder_results,
            )

        except Exception as e:
            logger.error(f"Reminder process failed: {str(e)}")
            return api_response(
                action="server_error",
                message="Failed to process reminder requests",
                status=500,
            )


class DocumentPreviewWithActivityView(generics.ListAPIView):
    authentication_classes = [OptionalJWTAccessTokenAuthentication]

    def get_queryset(self):
        doc_reference_id = self.request.query_params.get("doc_reference_id")
        organisation_doc_reference_id = self.request.query_params.get(
            "organisation_doc_reference_id"
        )

        if not doc_reference_id or not organisation_doc_reference_id:
            return api_response(
                action="data_not_retrieved",
                message="doc_reference_id or organisation_doc_reference_id not provided",
            )

        # Get user document
        user_document = UserDocument.objects.filter(
            reference_id=doc_reference_id
        ).first()
        if not user_document:
            return api_response(
                action="data_not_retrieved",
                message="Document not found",
            )

        # Get organization document
        organisation_doc_obj = (
            OrganizationDocument.objects.filter(
                encrypted_reference_id=organisation_doc_reference_id,
                user_document=user_document,
            )
            .select_related("user_document")
            .prefetch_related("user_document__signers")
            .first()
        )

        if not organisation_doc_obj:
            logger.error(
                f"No OrganizationDocument found with reference ID: {organisation_doc_reference_id}"
            )
            return None

        return {
            "org_doc": organisation_doc_obj,
            "activity_logs": ActivityLog.objects.filter(
                document_refrence_id=organisation_doc_obj.user_document.reference_id
            ).order_by("timestamp"),
        }

    def list(self, request, *args, **kwargs):
        verification_token = request.query_params.get("verification_token")
        reference_signer_id = request.query_params.get("reference_signer_id")
        reference_document_id = request.query_params.get("doc_reference_id")

        if not (reference_signer_id and reference_document_id):
            return api_response(
                action="data_not_retrieved",
                message="Required query parameters missing",
                status=400,
            )

         # If no verification token, check if user is authenticated via JWT
        if not verification_token:
            # Check if user is authenticated using JWT token
            if not request.user.is_authenticated:
                return api_response(
                    action="data_not_retrieved",
                    message="Verification required. Please verify first.",
                    status=401
                )
            # If user is authenticated, continue without verification token
            verification_token = None

        # Get document and signer
        document = UserDocument.objects.get(reference_id=reference_document_id)
        signer = DocumentSigner.objects.get(reference_signer_id=reference_signer_id)

        document_status, msg = check_document_status(user_document=document)
        if not document_status:
            return api_response(
                action="data_not_retrieved",
                message=msg,
                status=404
            )

        # Verify token using database or check user ownership
        if verification_token:
            # If verification token is provided, verify it
            if not DocumentVerificationService.verify_token(document, signer, verification_token):
                return api_response(
                    action="data_not_retrieved",
                    message="Verification expired or invalid. Please verify again.",
                    status=403,
                )
        else:
            # If no verification token, check if user is authenticated and owns the document
            if not request.user.is_authenticated:
                return api_response(
                    action="data_not_retrieved",
                    message="Authentication required.",
                    status=401
                )

            # Only allow preview if user is the document owner
            is_owner = (document.owner == request.user) or (
                document.created_by == request.user)

            if not is_owner:
                return api_response(
                    action="data_not_retrieved",
                    message="You don't have permission to access this document.",
                    status=403
                )

        queryset = self.get_queryset()

        if not queryset:
            return api_response(
                action="data_not_retrieved", message="No documents found"
            )

        organisation = queryset["org_doc"].organization
        superadmin = organisation.get_organization_owner()

        org_superadmin = {
            "id": superadmin.id if superadmin else None,
            "name": f"{superadmin.first_name} {superadmin.last_name}" if superadmin else None,
            "org_name": organisation.get_decrypted_name() if organisation else None,
            "email": superadmin.get_decrypted_email() if superadmin else None,
        }
        # Serialize document preview data
        doc_serializer = DocumentPreiviewViewSerializer(
            queryset["org_doc"], context={
                "request": request, "org_superadmin": org_superadmin}
        )

        # Serialize and process activity logs
        activity_serializer = ActivityLogSerializer(
            queryset["activity_logs"], many=True
        )

        # Group activity logs by date
        activity_data = {}
        for log in activity_serializer.data:
            # Use timestamp instead of created_at
            date = log["timestamp"].split("T")[0] if "timestamp" in log else log.get(
                "created_at", "").split("T")[0]
            if date not in activity_data:
                activity_data[date] = []
            activity_data[date].append(log)

        return api_response(
            action="data_retrieved",
            status="success",
            data={
                "document": doc_serializer.data,
                "activity_logs": activity_data,
            },
        )


class DocumentVerificationDetailsView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id"
            )
            doc_reference_id = request.query_params.get("doc_reference_id")

            if not organisation_doc_reference_id or not doc_reference_id:
                return api_response(
                    action="data_not_retrieved",
                    message="Required parameters are missing",
                )

            # Get the document with related signers
            document = (
                UserDocument.objects.filter(
                    reference_id=doc_reference_id, is_deleted=False
                )
                .prefetch_related("signers")
                .first()
            )

            if not document:
                return api_response(
                    action="data_not_retrieved", message="Document not found"
                )

            # Generate document hash
            document_hash = self.generate_document_hash(document)

            # Get number of signers
            num_signers = document.signers.count()

            # Format the completion date
            completion_date = document.updated_at.strftime("%m/%d/%Y")

            # Get all signers for the document
            signers = list(document.signers.all())

            # Collect all encrypted emails for signers
            # encrypted_emails = [s.email for s in signers if s.email]

            # Query all relevant ActivityLogs in one go
            activity_logs = ActivityLog.objects.filter(
                document_refrence_id=document.reference_id,
                category__iexact="document signed",
            )

            # Build a mapping: encrypted_email -> ActivityLog
            activity_log_map = {log.email: log for log in activity_logs}

            # Signer details:
            signer_list = []
            for s in document.signers.all():
                signer_type = "Simple Electronic Signature (SES)"
                if str(s.signer_type).lower() == "sweden_bank_id":
                    signer_type = "Advanced Electronic Signature (AES)"

                activity_log = activity_log_map.get(s.email)
                signer_data = {
                    "name": s.get_decrypted_name(),
                    "email": s.get_decrypted_email(),
                    "signature_type": signer_type,
                    "ip_address": activity_log.ip_address if activity_log else None,
                    "timestamp": activity_log.timestamp.strftime("%Y-%m-%d %H:%M") if activity_log and activity_log.timestamp else "",
                }
                signer_list.append(signer_data)

            logger.info(f"Document Signers: {signer_list}")
            context = {
                "transaction_id": str(document.transaction_id),
                "document_title": document.get_decrypted_title(),
                "completed_on": completion_date,
                "number_of_signatories": num_signers,
                "document_hash": document_hash,
                "signers": signer_list
            }

            html_content = self.generate_verification_html(context)

            # Generate filename
            current_timestamp = now().strftime("%Y%m%d_%H%M%S")
            filename = f"verifications/{document.transaction_id}_{current_timestamp}.pdf"

            pdf_content = HTML(string=html_content).write_pdf()

            # Create directory if it doesn't exist
            directory = os.path.join(settings.MEDIA_ROOT, 'verifications')
            os.makedirs(directory, exist_ok=True)

            # Save directly to S3 using default_storage (which is configured for S3)
            with default_storage.open(filename, 'wb') as f:
                f.write(pdf_content)

            # Generate the public URL for the saved PDF
            pdf_url = (
                f"{settings.MEDIA_URL}{filename}"
            )
            log_user_action(
                request=request,
                user=request.user,
                action="Document verification document generated successfully.",
                status="success",
                details={"type": "document_verification_document_generated"},
            )

            return api_response(
                action="data_retrieved", status="success", data={"pdf_url": pdf_url}
            )

        except Exception as e:
            logger.error(f"Error generating verification document: {str(e)}")
            return api_response(
                action="verification_generation_failed",
                message="Failed to generate verification document",
                status=500,
            )

    def generate_document_hash(self, document):
        """Generate a unique hash for the document"""
        hash_input = (
            f"{document.transaction_id}{document.created_at}{document.updated_at}"
        )
        hash_obj = hashlib.sha256(hash_input.encode())
        return f"{hash_obj.hexdigest()} - {hash_obj.hexdigest()[:8]}"

    def generate_verification_html(self, context: dict):
        html_template_path = os.path.join(
            settings.BASE_DIR, "documents", "templates", "documents", "email", "signed_document_certificate.html")

        base_path = os.path.join(settings.BASE_DIR, "documents", "images")

        # Function to convert image to base64
        def get_image_base64(image_name):
            try:
                image_path = os.path.join(base_path, image_name)
                with open(image_path, "rb") as img_file:
                    return base64.b64encode(img_file.read()).decode("utf-8")
            except Exception as e:
                logger.error(f"Error loading image {image_name}: {str(e)}")
                return ""

        # Convert images to base64
        images = {
            "gdpr_img": f"data:image/png;base64,{get_image_base64('gdpr-1.png')}",
            "gdpr_img_2": f"data:image/png;base64,{get_image_base64('gdpr-2.png')}",
            "skrivly_logo": f"data:image/png;base64,{get_image_base64('skrivly-logo-1.png')}",
            "european_union_flag": f"data:image/png;base64,{get_image_base64('europe-flag.png')}",
            "sweden_flag": f"data:image/png;base64,{get_image_base64('sweden-flag.png')}",
            "bank_id_img": f"data:image/png;base64,{get_image_base64('bank_id-new-big-logo.png')}"
        }

        context.update(images)
        html_string = render_to_string(html_template_path, context)
        return html_string


class ResendSignedDocumentAPIView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Resend the signed document with HTML template to user's email.
        """
        try:
            # Get required parameters
            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id")
            doc_reference_id = request.query_params.get("doc_reference_id")
            reference_signer_id = request.query_params.get("reference_signer_id")
            send_all = request.query_params.get(
                "send_all", False
            )
            if not organisation_doc_reference_id or not doc_reference_id:
                return api_response(
                    action="data_not_retrieved",
                    message="document_organisation_reference_id or doc_reference_id is missing"
                )

            # Get document details
            user_document = UserDocument.objects.filter(
                reference_id=doc_reference_id
            ).first()

            if not user_document:
                return api_response(
                    action="data_not_found",
                    message="Document not found"
                )

            document_organisation = OrganizationDocument.objects.filter(
                encrypted_reference_id=organisation_doc_reference_id,
                user_document=user_document,
            ).first()

            if not document_organisation:
                return api_response(
                    action="data_not_found",
                    message="Organization document not found"
                )

            # Get document signers
            signers = DocumentSigner.objects.filter(document=user_document)

            if not send_all and reference_signer_id:
                signers = signers.filter(reference_signer_id=reference_signer_id)

            # Check if document is fully signed
            if not all(signer.status == "signed" for signer in signers):
                return api_response(
                    action="validation_error",
                    message="Document is not fully signed by all signers"
                )

            # Use a dictionary to track recipients by email
            recipients = {}
            document_info = {
                "id": user_document.id,
                "reference_id": user_document.reference_id,
                "title": user_document.get_decrypted_title()
                if hasattr(user_document, "get_decrypted_title")
                else "",
                # "owner": user_document.user.get_full_name() if user_document.user else "Unknown"
            }

            # Add document owner
            # if user_document.user and user_document.user.email:
            #     email = user_document.user.get_decrypted_email()
            #     if email not in recipients:
            #         recipients[email] = (
            #             email,
            #             user_document.user.get_full_name() or email,
            #             signers[0].reference_signer_id,
            #             "owner"  # Indicate this recipient as the owner
            #         )

            # Add all signers
            for signer in signers:
                if signer.email:
                    email = signer.get_decrypted_email()
                    if email not in recipients:
                        recipients[email] = (
                            email,
                            signer.get_decrypted_name(),
                            signer.reference_signer_id
                            # "signer"  # Indicate this recipient as a signer
                        )

            # print("recipients", recipients)
            # Send emails
            for email, (email, name, signer_id) in recipients.items():
                send_background_email.delay(
                    email=email,
                    receiver_name=name,
                    reference_signer_id=signer_id,
                    org_doc_reference_id=organisation_doc_reference_id,
                    user_id=user_document.created_by.reference_id,
                    document=document_info,  # Pass the dictionary instead of document object
                    status="resend_signed",
                    # role=role,
                )

            # Log the action
            ActivityLog.objects.create(
                document_refrence_id=user_document.reference_id,
                event_type="Resent",
                title=user_document.title,
                category="Document Resent",
                message=f"Signed document resent to {len(recipients)} recipient(s)",
                user=user_document.created_by.reference_id
            )

            log_user_action(
                request=request,
                user=request.user,
                action="Signed document resent successfully",
                status="success",
                details={"type": "document_resent"}
            )

            return api_response(
                action="data_retrieved",
                status="success",
                message=f"Signed document resent to {len(recipients)} recipient(s)"
            )

        except Exception as e:
            logger.error(f"Error resending signed document: {str(e)}")
            return api_response(
                action="server_error",
                message="Failed to resend signed document"
            )


class UpdateSignerDetailsView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def put(self, request, reference_signer_id):
        """
        Update signer details for a specific signer.

        Required Query Parameters:
        - organisation_doc_reference_id: Reference ID of the organization document
        - doc_reference_id: Reference ID of the document

        Request Body:
        {
            "email": "<EMAIL>",  # Optional
            "phone_number": "+1234567890",  # Optional
        }
        """
        try:
            # Get required query parameters
            organisation_doc_reference_id = request.query_params.get(
                "organisation_doc_reference_id")
            doc_reference_id = request.query_params.get("doc_reference_id")

            if not all([organisation_doc_reference_id, doc_reference_id]):
                return api_response(
                    action="validation_error",
                    message="Missing required parameters: organisation_doc_reference_id or doc_reference_id",
                    status="error"
                )

            # Get the document
            user_document = UserDocument.objects.filter(
                reference_id=doc_reference_id).first()
            if not user_document:
                return api_response(
                    action="data_not_found",
                    message="Document not found",
                    status="error"
                )

            document_organisation = OrganizationDocument.objects.filter(
                encrypted_reference_id=organisation_doc_reference_id,
                user_document=user_document
            ).first()
            if not document_organisation:
                return api_response(
                    action="data_not_found",
                    message="Organization document not found",
                    status="error"
                )

            # Get the signer
            signer = DocumentSigner.objects.filter(
                reference_signer_id=reference_signer_id,
                document=user_document
            ).first()

            if not signer:
                return api_response(
                    action="data_not_found",
                    message="Signer not found",
                    status="error"
                )

            # Check if signer has already signed
            if signer.status == "signed":
                return api_response(
                    action="validation_error",
                    message="Cannot update details of a signer who has already signed",
                    status="error"
                )

            # Store old email for comparison
            old_email = signer.get_decrypted_email()

            # Handle metadata update separately
            meta_data = request.data.get('meta_data')
            if meta_data is not None:
                # Update meta_data directly on the signer object
                signer.meta_data = meta_data
                signer.save()

            # Update other signer details
            serializer = DocumentSignerSerializer(
                signer, data=request.data, partial=True)
            if serializer.is_valid():
                updated_signer = serializer.save()

                # Get the new email after update
                new_email = updated_signer.get_decrypted_email()

                # If email was updated, invalidate old signer access and send new document link
                if old_email != new_email:
                    # Invalidate old signer's verification data
                    DocumentVerificationData.objects.filter(
                        document=user_document,
                        signer=updated_signer
                    ).delete()

                    # Generate new reference_signer_id for the updated signer
                    new_reference_id = str(uuid.uuid4())
                    updated_signer.reference_signer_id = cipher_suite.encrypt(
                        new_reference_id.encode()).decode()
                    updated_signer.save()

                    # Send email to new signer with new reference_signer_id
                    send_background_email.delay(
                        email=new_email,
                        receiver_name=updated_signer.get_decrypted_name(),
                        document={"reference_id": doc_reference_id},
                        reference_signer_id=updated_signer.reference_signer_id,
                        user_id=request.user.reference_id,
                        org_doc_reference_id=organisation_doc_reference_id,
                        status="resend_signed"
                    )
                    logger.info(
                        f"Sent document link to updated signer email: {new_email}")

                return api_response(
                    action="data_updated",
                    message="Signer details updated successfully",
                    data=DocumentSignerSerializer(updated_signer).data,
                    status="success"
                )
            else:
                return api_response(
                    action="validation_error",
                    message="Invalid data provided",
                    data=serializer.errors,
                    status="error"
                )

        except Exception as e:
            logger.error(f"Error updating signer details: {str(e)}", exc_info=True)
            return api_response(
                action="server_error",
                message=f"An error occurred while updating signer details: {str(e)}",
                status="error"
            )


class DirectoryDocumentDeleteView(generics.DestroyAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    queryset = DirectoryDocument.objects.all()

    def get_object(self):
        """
        Override get_object to handle document retrieval with proper permissions
        """
        queryset = DirectoryDocument.objects.all()

        # Filter by user's organization and active documents
        if self.request.user.role == 'org_superadmin':
            queryset = queryset.filter(
                document__organization__in=self.request.user.organizations.all(),
                is_active=True
            )
        else:
            # For regular users, check if they are the owner
            queryset = queryset.filter(
                Q(added_by=self.request.user) |
                Q(document__created_by=self.request.user),
                is_active=True
            )

        # Get the object
        obj = get_object_or_404(queryset, id=self.kwargs['pk'])
        self.check_object_permissions(self.request, obj)
        return obj

    def destroy(self, request, *args, **kwargs):
        try:
            directory_document = self.get_object()

            # Soft delete the document
            directory_document.is_active = False
            directory_document.removed_by = request.user
            directory_document.removed_at = timezone.now()
            directory_document.save()

            # Delete the document
            directory_document.document.user_document.move_to_delete(request.user)

            # Log the action
            log_user_action(
                request=request,
                user=request.user,
                action='Document removed from directory successfully',
                status='success',
                details={
                    'type': 'document_removed_from_directory',
                    'directory_id': str(directory_document.directory.id),
                    'document_id': str(directory_document.id)
                }
            )

            return api_response(
                action="data_deleted",
                message="Document removed successfully",
                status="success"
            )

        except Exception as e:
            logger.error(
                "Error removing document from directory",
                extra={
                    'directory_document_id': kwargs.get('pk'),
                    'error': str(e)
                },
                exc_info=True
            )
            return api_response(
                action="data_not_deleted",
                message=str(e)
            )
