from rest_framework import permissions


class IsOrganizationMember(permissions.BasePermission):
    def has_object_permission(self, request, view, obj):
        return request.user.organizations.filter(id=obj.organization.id).exists()


class IsOrganizationAdmin(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_org_admin

    def has_object_permission(self, request, view, obj):
        return (request.user.is_org_admin and
                request.user.organizations.filter(id=obj.organization.id).exists())
