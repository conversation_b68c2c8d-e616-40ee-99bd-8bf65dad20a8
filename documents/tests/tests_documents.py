from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from documents.models import DocumentSigner, UserDocument


class UserDocumentAPITests(APITestCase):
    fixtures = ["documents/tests/fixtures/documents_fixtures.json"]

    def setUp(self):
        # Load fixtures into test cases
        self.document1 = UserDocument.objects.get(
            pk="7a3baca6-117b-4383-8fb0-fea2394e4879"
        )
        self.document2 = UserDocument.objects.get(
            pk="ad285bd3-c49d-41df-a9ee-d475f12b4426"
        )
        self.signer1 = DocumentSigner.objects.get(pk=1)
        self.signer2 = DocumentSigner.objects.get(pk=2)
        self.client = self.client  # Use Django's test client from APITestCase

    # 1. Happy Path Tests

    def test_list_user_documents(self):
        """Test listing all user documents."""
        url = reverse("document-list-create")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # Two documents from the fixture

    def test_retrieve_user_document(self):
        """Test retrieving a specific user document."""
        url = reverse("document-detail", kwargs={"pk": self.document1.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["title"], "Test Document 1")

    def test_create_user_document_with_file_upload(self):
        """Test creating a new user document with file upload."""
        document_file = SimpleUploadedFile(
            "Test-Document.pdf", b"file_content", content_type="application/pdf"
        )
        data = {
            "user": "testuser",
            "title": "New Document",
            "status": "sent",
            "document_file": document_file,
        }
        url = reverse("document-list-create")
        response = self.client.post(url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Verify the document was created with the file
        self.assertTrue(UserDocument.objects.filter(title="New Document").exists())

    def test_update_user_document_with_file_upload(self):
        """Test updating an existing user document with a new file."""
        document_file = SimpleUploadedFile(
            "Updated-Document.pdf",
            b"updated_file_content",
            content_type="application/pdf",
        )
        data = {"title": "Updated Document with File", "document_file": document_file}
        url = reverse("document-detail", kwargs={"pk": self.document1.id})
        response = self.client.put(url, data, format="multipart")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Verify that the document was updated with the new file
        updated_response = self.client.get(url)
        self.assertEqual(updated_response.data["title"], "Updated Document with File")

    def test_delete_user_document(self):
        """Test deleting a user document."""
        url = reverse("document-detail", kwargs={"pk": self.document1.id})
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        # Verify document no longer exists
        followup_response = self.client.get(url)
        self.assertEqual(followup_response.status_code, status.HTTP_404_NOT_FOUND)

    def test_add_document_signer(self):
        """Test adding a signer to a document."""
        url = reverse("document-signers", kwargs={"document_id": self.document2.id})
        data = {
            "name": "New Signer",
            "email": "<EMAIL>",
            "phone_number": "1122334455",
            "role": "signer",
        }
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        # Verify that the signer was added
        self.assertTrue(
            DocumentSigner.objects.filter(email="<EMAIL>").exists()
        )

    def test_retrieve_document_signers(self):
        """Test retrieving all signers for a document."""
        url = reverse("document-signers", kwargs={"document_id": self.document1.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # Two signers for document1

    def test_retrieve_single_document_signer(self):
        """Test retrieving a single signer by ID."""
        url = reverse(
            "single-signer",
            kwargs={"document_id": self.document1.id, "signer_id": self.signer1.id},
        )
        response = self.client.get(url)
        print(f"\n\ntest_retrieve_single_document_signer> {response.json()}")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["name"], self.signer1.name)

    def test_update_document_signer(self):
        """Test updating a signer for a document."""
        url = reverse(
            "single-signer",
            kwargs={"document_id": self.document1.id, "signer_id": self.signer1.id},
        )
        data = {"name": "Updated Signer"}
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Verify that the signer was updated
        updated_signer = DocumentSigner.objects.get(id=self.signer1.id)
        self.assertEqual(updated_signer.name, "Updated Signer")

    def test_delete_document_signer(self):
        """Test deleting a signer from a document."""
        url = reverse(
            "single-signer",
            kwargs={"document_id": self.document1.id, "signer_id": self.signer1.id},
        )
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        # Verify signer no longer exists
        self.assertFalse(DocumentSigner.objects.filter(id=self.signer1.id).exists())

    # 2. Sad Path Tests

    def test_invalid_user_document_retrieve(self):
        """Test retrieving a non-existent document (invalid ID)."""
        invalid_id = "00000000-0000-0000-0000-000000000000"
        url = reverse("document-detail", kwargs={"pk": invalid_id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_create_document_with_missing_field(self):
        """Test creating a document with missing required fields."""
        data = {
            "user": "testuser",
            "status": "sent",
            # Missing "document_file" and "title"
        }
        url = reverse("document-list-create")
        response = self.client.post(url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_create_document_with_invalid_file_type(self):
        """Test creating a document with an invalid file type."""
        invalid_document_file = SimpleUploadedFile(
            "Test-Document.txt", b"invalid_content", content_type="text/plain"
        )
        data = {
            "user": "testuser",
            "title": "Invalid File Document",
            "status": "sent",
            "document_file": invalid_document_file,
        }
        url = reverse("document-list-create")
        response = self.client.post(url, data, format="multipart")
        # Expecting failure due to invalid file type
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_document_signer_not_found(self):
        """Test updating a non-existent signer (invalid ID)."""
        invalid_signer_id = 999
        url = reverse(
            "single-signer",
            kwargs={"document_id": self.document1.id, "signer_id": invalid_signer_id},
        )
        data = {"name": "Updated Name"}
        response = self.client.put(url, data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_document_signer_not_found(self):
        """Test deleting a non-existent signer (invalid ID)."""
        invalid_signer_id = 999
        url = reverse(
            "single-signer",
            kwargs={"document_id": self.document1.id, "signer_id": invalid_signer_id},
        )
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_delete_non_existent_user_document(self):
        """Test deleting a non-existent document."""
        invalid_id = "00000000-0000-0000-0000-000000000000"
        url = reverse("document-detail", kwargs={"pk": invalid_id})
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
