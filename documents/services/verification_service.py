from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from documents.models.document_models import DocumentVerificationData, UserDocument, DocumentSigner
from django.contrib.auth.hashers import make_password, check_password
import uuid
import secrets
import logging
import random

logger = logging.getLogger("app")


class DocumentVerificationService:
    @staticmethod
    def generate_otp(length: int = 6) -> str:
        """Generate a random OTP of specified length."""
        return "".join([str(random.randint(0, 9)) for _ in range(length)])

    @staticmethod
    def generate_verification_token() -> str:
        """Generate a unique verification token."""
        return f"document_verify_token_{uuid.uuid4().hex}_{secrets.token_urlsafe(16)}"

    @staticmethod
    def create_or_update_otp(document: UserDocument, signer: DocumentSigner, otp: str) -> DocumentVerificationData:
        """Create or update OTP for document verification."""
        try:
            # Ensure OTP_EXPIRY_SECONDS is an integer
            expiry_seconds = int(settings.OTP_EXPIRY_SECONDS)
            verification_data, created = DocumentVerificationData.objects.get_or_create(
                document=document,
                signer=signer,
                defaults={
                    'otp': make_password(otp),
                    'otp_expiry': timezone.now() + timedelta(seconds=expiry_seconds),
                    'is_verified': False
                }
            )

            if not created:
                verification_data.otp = make_password(otp)
                verification_data.otp_expiry = timezone.now() + timedelta(seconds=expiry_seconds)
                verification_data.is_verified = False
                verification_data.save()

            return verification_data
        except Exception as e:
            logger.error(f"Error creating/updating OTP: {str(e)}")
            raise

    @staticmethod
    def verify_otp(document: UserDocument, signer: DocumentSigner, provided_otp: str) -> bool:
        """Verify the provided OTP."""
        try:
            verification_data = DocumentVerificationData.objects.get(
                document=document,
                signer=signer
            )

            if not verification_data.otp or not verification_data.otp_expiry:
                return False

            if timezone.now() > verification_data.otp_expiry:
                return False

            return check_password(provided_otp, verification_data.otp)
        except DocumentVerificationData.DoesNotExist:
            return False
        except Exception as e:
            logger.error(f"Error verifying OTP: {str(e)}")
            return False

    @staticmethod
    def create_verification_token(document: UserDocument, signer: DocumentSigner) -> tuple[str, DocumentVerificationData]:
        """Create a new verification token."""
        try:
            verification_token = DocumentVerificationService.generate_verification_token()
            token_hashed = make_password(verification_token)

            # Ensure TOKEN_EXPIRY_SECONDS is an integer
            expiry_seconds = int(settings.TOKEN_EXPIRY_SECONDS)
            verification_data, created = DocumentVerificationData.objects.get_or_create(
                document=document,
                signer=signer,
                defaults={
                    'verification_token': token_hashed,
                    'token_expiry': timezone.now() + timedelta(seconds=expiry_seconds),
                    'is_verified': True
                }
            )

            if not created:
                verification_data.verification_token = token_hashed
                verification_data.token_expiry = timezone.now() + timedelta(seconds=expiry_seconds)
                verification_data.is_verified = True
                verification_data.save()

            return verification_token, verification_data
        except Exception as e:
            logger.error(f"Error creating verification token: {str(e)}")
            raise

    @staticmethod
    def verify_token(document: UserDocument, signer: DocumentSigner, provided_token: str) -> bool:
        """Verify the provided verification token."""
        try:
            verification_data = DocumentVerificationData.objects.get(
                document=document,
                signer=signer
            )

            if not verification_data.verification_token or not verification_data.token_expiry:
                return False

            if timezone.now() > verification_data.token_expiry:
                return False

            return check_password(provided_token, verification_data.verification_token)
        except DocumentVerificationData.DoesNotExist:
            return False
        except Exception as e:
            logger.error(f"Error verifying token: {str(e)}")
            return False

    @staticmethod
    def get_verification_data(document: UserDocument, signer: DocumentSigner) -> DocumentVerificationData:
        """Get verification data for a document and signer."""
        try:
            return DocumentVerificationData.objects.get(
                document=document,
                signer=signer
            )
        except DocumentVerificationData.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"Error getting verification data: {str(e)}")
            raise
