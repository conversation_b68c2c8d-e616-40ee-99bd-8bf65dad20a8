import uuid
import logging
# import base64
# import tempfile
# import io
# from PIL import Image
# from PyPDF2 import PdfReader
# from pdf2image import convert_from_bytes  # Alternative to convert_from_path
# from django.core.files.base import ContentFile
from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone

from cryptography.fernet import Fernet
from django.conf import settings
# from weasyprint import CSS, HTML
# from weasyprint.text.fonts import FontConfiguration

from accounts.models.organisation import Organization
from accounts.models.users import User
# from ai_documents_app.services.thumbnail_generator import ThumbnailGenerator

# from realtime.signals import document_created, signer_added

# from realtime.services.send_notification import send_document_deleted_notification

# Encryption setup using the same key as in the models
cipher_suite = Fernet(settings.ENCRYPTION_KEY)

logger = logging.getLogger('app')


def fernet_decrypt(ciphertext):
    # here data from byte to string then decrypting
    return cipher_suite.decrypt(ciphertext).decode("utf-8")


class UserDocument(models.Model):
    """Model to store document details with encryption and
    security measures for sensitive information."""

    status_choices = (
        ("draft", "draft"),
        ("sent", "sent"),
        ("cancelled", "cancelled"),
        ("trash", "trash"),
        ("withdrawn", "withdrawn"),
    )

    # Primary key, uniquely identifying each document
    id = models.UUIDField(
        default=uuid.uuid4, unique=True, primary_key=True, editable=False
    )

    # Encrypting reference_id
    reference_id = models.CharField(
        max_length=255, unique=True, editable=False, null=True, blank=True
    )

    # Reference to the user (assuming custom User model)
    # user = models.CharField(max_length=255)
    owner = models.ForeignKey(User, on_delete=models.SET_NULL,
                              null=True, blank=True, related_name="owned_documents")
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, related_name="created_documents")

    email_message = models.TextField(null=True, blank=True)
    # Encrypting document title
    title = models.CharField(max_length=255, null=True, blank=True)

    download_pdf = models.BooleanField(default=True)

    # New fields for previous status tracking
    previous_status = models.CharField(
        max_length=70,
        choices=status_choices,
        null=True,
        blank=True,
        help_text="Stores the status before moving to trash",
    )

    trashed_at = models.DateTimeField(
        null=True, blank=True, help_text="Timestamp when document was moved to trash"
    )

    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)
    deleted_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True, related_name="deleted_documents")

    # Document status
    status = models.CharField(choices=status_choices, default="draft", max_length=70)

    # Document file (Encrypt the file at rest)
    document_file = models.FileField(upload_to="documents/")

    latest_document_file = models.FileField(
        upload_to="documents/", null=True, blank=True
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Transaction details which will be uuid.
    transaction_id = models.UUIDField(
        default=uuid.uuid4,  null=True, blank=True, editable=False
    )
    bank_id_transaction_id = models.CharField(max_length=255, null=True, blank=True)
    language = models.CharField(max_length=255, null=True, blank=True)
    # Additional fields for GDPR compliance, security audit, and consent management
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    location = models.CharField(max_length=255, null=True, blank=True)
    gdpr_compliance_status = models.BooleanField(
        default=False
    )  # GDPR compliance consent
    encryption_status = models.BooleanField(default=True)  # Tracks if data is encrypted
    access_log = models.TextField(
        null=True, blank=True
    )  # Audit log for access tracking
    last_accessed_at = models.DateTimeField(
        null=True, blank=True
    )  # Last time the document was accessed
    document_password = models.CharField(max_length=100)
    expiration_date = models.DateTimeField(null=True, blank=True)
    is_expired = models.BooleanField(default=False)
    thumbnail_path = models.TextField(null=True, blank=True)
    thumbnail_file = models.FileField(null=True, blank=True)

    # Agreement
    is_agreement = models.BooleanField(default=False)
    agreement_start_date = models.DateTimeField(null=True, blank=True)
    agreement_end_date = models.DateTimeField(null=True, blank=True)
    notifications_status = models.JSONField(default=dict, blank=True, null=True)

    file_size = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Size of the document file in MB"
    )

    def save(self, *args, **kwargs):
        try:
            is_new = not self.pk
            if not self.reference_id:
                raw_reference_id = str(uuid.uuid4())
                self.reference_id = cipher_suite.encrypt(
                    raw_reference_id.encode()).decode()
                logger.info(
                    "Generated new reference ID for document",
                    extra={
                        'document_id': str(self.id),
                        'owner_id': str(self.owner.id) if self.owner else None,
                        'creted_by_id': str(self.created_by.id) if self.created_by else None
                    }
                )

            is_transaction_id_new = not self.transaction_id
            if is_transaction_id_new:
                self.transaction_id = str(uuid.uuid4())

            # Calculate and store file size if document file exists
            if self.document_file:
                try:
                    # Get file size in bytes
                    file_size_bytes = self.document_file.size
                    # Convert to MB with 2 decimal places
                    self.file_size = round(file_size_bytes / (1024 * 1024), 2)
                    logger.info(
                        "Calculated document file size",
                        extra={
                            'document_id': str(self.id),
                            'file_size_mb': str(self.file_size)
                        }
                    )
                except Exception as e:
                    logger.error(
                        "Error calculating file size",
                        extra={
                            'document_id': str(self.id),
                            'error': str(e)
                        },
                        exc_info=True
                    )

            if is_new:
                logger.info(
                    "New document created",
                    extra={
                        'document_id': str(self.id),
                        'title': self.title,
                        'status': self.status,
                        'owner_id': str(self.owner.id) if self.owner else None,
                        'creted_by_id': str(self.created_by.id) if self.created_by else None
                    }
                )
        except Exception as e:
            logger.error(
                "Error saving document",
                extra={
                    'document_id': str(self.id),
                    'error': str(e)
                },
                exc_info=True
            )
        super().save(*args, **kwargs)

    def move_to_trash(self):
        try:
            if self.status != "trash":
                logger.info(
                    "Moving document to trash",
                    extra={
                        'document_id': str(self.id),
                        'previous_status': self.status
                    }
                )
                self.previous_status = self.status
                self.status = "trash"
                self.trashed_at = timezone.now()
                self.save()
        except Exception as e:
            logger.error(
                "Error moving document to trash",
                extra={
                    'document_id': str(self.id),
                    'error': str(e)
                },
                exc_info=True
            )
            raise

    def restore_from_trash(self):
        """
        Restore document to its previous status
        """
        if self.status == "trash" and self.previous_status:
            self.status = self.previous_status
            self.previous_status = None
            self.trashed_at = None
            self.save()
        elif self.status == "trash":
            # Fallback if no previous status is stored
            self.status = "draft"
            self.trashed_at = None
            self.save()

    def move_to_delete(self):
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

    def withdraw_document(self, user: User):
        try:
            if self.status != "withdrawn":
                logger.info(
                    "Withdrawing document",
                    extra={
                        'document_id': str(self.id),
                        'previous_status': self.status
                    }
                )
                self.previous_status = self.status
                self.status = "withdrawn"
                self.is_deleted = True
                self.deleted_by = user
                self.deleted_at = timezone.now()
                self.save()
        except Exception as e:
            logger.error(
                "Error while withdrawing the document.",
                extra={
                    "document_id": str(self.id),
                    'error': str(e)
                },
                exc_info=True)
            raise

    def get_decrypted_reference_id(self):
        return fernet_decrypt(self.reference_id.encode()) if self.reference_id else None

    def get_decrypted_title(self):
        return self.title if self.title else None

    def get_decrypted_user(self):
        return fernet_decrypt(self.created_by.name.encode()) if self.created_by else None

    def get_decrypted_ip_address(self):
        return fernet_decrypt(self.ip_address.encode()) if self.ip_address else None

    def get_decrypted_location(self):
        return fernet_decrypt(self.location.encode()) if self.location else None

    def get_decrypted_access_log(self):
        return fernet_decrypt(self.access_log.encode()) if self.access_log else None

    def __str__(self):
        return self.get_decrypted_title() or str(self.id)

    is_global = models.BooleanField(default=False)

    @classmethod
    def get_expired_documents(cls):
        """Get all documents that have expired but haven't notified users yet"""
        return cls.objects.filter(
            expiration_date__lt=timezone.now(),
            meta_data__isnull=True
        ).select_related('user')

    def get_file_size_display(self):
        """Return the file size with appropriate unit"""
        if self.file_size:
            if self.file_size >= 1024:
                return f"{round(self.file_size / 1024, 2)} GB"
            return f"{self.file_size} MB"
        return "0 MB"


@receiver(post_save, sender=UserDocument)
def generate_document_thumbnail(sender, instance, created, **kwargs):
    """
    Signal handler to queue thumbnail generation when a document is created or updated
    """
    try:
        # Skip if this is a thumbnail update
        if kwargs.get('update_fields') == {'thumbnail_file'}:
            return

        # Check if document file exists and needs thumbnail generation
        if instance.document_file and (
            created or
            not instance.thumbnail_file or
            instance.document_file.name not in str(instance.thumbnail_file)
        ):
            from documents.tasks import generate_document_thumbnail_task

            logger.info(
                "Queuing thumbnail generation task",
                extra={
                    'document_id': str(instance.id),
                    'document_file': str(instance.document_file),
                    'is_new': created
                }
            )

            # Queue the task asynchronously
            generate_document_thumbnail_task.delay(str(instance.id))

    except Exception as e:
        pass


class OrganizationDocument(models.Model):
    """Custom through model to link UserDocument and Organization with encryption."""

    id = models.UUIDField(
        default=uuid.uuid4, unique=True, primary_key=True, editable=False
    )
    user_document = models.ForeignKey(UserDocument, on_delete=models.CASCADE)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    is_global = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)
    # Encrypted reference ID
    encrypted_reference_id = models.TextField(null=True, blank=True)
    # Timestamp
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        """Encrypt the reference ID before saving."""
        if not self.encrypted_reference_id:
            raw_reference_id = f"{self.user_document.id}-{self.organization.id}"
            self.encrypted_reference_id = cipher_suite.encrypt(
                raw_reference_id.encode()
            ).decode()
        super().save(*args, **kwargs)

    def get_reference_id(self):
        """Decrypt the reference ID."""
        if self.encrypted_reference_id:
            return cipher_suite.decrypt(self.encrypted_reference_id.encode()).decode()
        return None

    class Meta:
        unique_together = ("user_document", "organization")


class DocumentSigner(models.Model):
    """Model to store signer details with encryption
    for personal information, following GDPR and BankID policies."""

    signature_type_choices = (
        ("sweden_bank_id", "sweden_bank_id"),
        ("otp", "otp"),
        ("email", "email"),
        ("default", "default"),
    )
    role_choices = (("signer", "signer"), ("approver", "approver"))
    status_choices = (
        ("pending", "pending"),
        ("approved", "approved"),
        ("signed", "signed"),
    )
    id = models.UUIDField(
        default=uuid.uuid4, unique=True, primary_key=True, editable=False
    )
    is_name_editable = models.BooleanField(default=False)
    # Foreign key linking to the user document
    document = models.ForeignKey(
        UserDocument,
        on_delete=models.CASCADE,
        related_name="signers",
        null=True,
        blank=True,
    )
    reference_signer_id = models.CharField(
        max_length=255, unique=True, editable=False, null=True, blank=True
    )
    hide_security_number = models.BooleanField(default=False)
    signer_type = models.CharField(
        choices=signature_type_choices, default="default", max_length=25
    )
    # Encrypting sensitive signer data (name, phone number, email)
    name = models.CharField(max_length=250, null=True, blank=True)
    updated_name = models.CharField(max_length=250, null=True, blank=True)
    phone_number = models.CharField(max_length=250, null=True, blank=True)
    email = models.EmailField()
    signature_image = models.TextField(null=True, blank=True)
    # Role (signer or approver)

    role = models.CharField(choices=role_choices, default="signer", max_length=70)
    status = models.CharField(choices=status_choices, default="pending", max_length=10)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    meta_data = models.JSONField(null=True, blank=True)
    # GDPR consent and auditing
    signed_at = models.DateTimeField(null=True, blank=True)
    gdpr_consent = models.BooleanField(default=False)  # GDPR consent status
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    location = models.CharField(max_length=255, null=True, blank=True)
    access_log = models.TextField(null=True, blank=True)  # Audit log for signer access
    bank_id_meta_data = models.JSONField(null=True, blank=True)
    # BankID-related fields for verification
    bank_id_verified = models.BooleanField(default=False)  # BankID verification status
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)
    verification_timestamp = models.DateTimeField(
        null=True, blank=True
    )  # When BankID was verified
    bank_id_reference = models.CharField(
        max_length=255, null=True, blank=True
    )  # BankID reference for auditing

    last_reminder_sent = models.DateTimeField(null=True, blank=True)
    reminder_count = models.IntegerField(default=0)

    def save(self, *args, **kwargs):
        try:
            is_new = not self.pk
            if not self.reference_signer_id:
                raw_reference_id = str(uuid.uuid4())
                self.reference_signer_id = cipher_suite.encrypt(
                    raw_reference_id.encode()).decode()
                logger.info(
                    "Generated new reference ID for signer",
                    extra={
                        'signer_id': str(self.id),
                        'document_id': str(self.document.id) if self.document else None
                    }
                )

            self.verification_timestamp = timezone.now()
            super().save(*args, **kwargs)

            if is_new:
                logger.info(
                    "New document signer created",
                    extra={
                        'signer_id': str(self.id),
                        'document_id': str(self.document.id) if self.document else None,
                        'role': self.role,
                        'status': self.status
                    }
                )
        except Exception as e:
            logger.error(
                "Error saving document signer",
                extra={
                    'signer_id': str(self.id),
                    'document_id': str(self.document.id) if self.document else None,
                    'error': str(e)
                },
                exc_info=True
            )
            raise

    def log_access(self, user):
        try:
            access_entry = f"Accessed by {user} at {timezone.now()}\n"
            self.access_log = (self.access_log or "") + access_entry
            self.save()
            logger.info(
                "Document signer access logged",
                extra={
                    'signer_id': str(self.id),
                    'user': str(user),
                    'document_id': str(self.document.id) if self.document else None
                }
            )
        except Exception as e:
            logger.error(
                "Error logging signer access",
                extra={
                    'signer_id': str(self.id),
                    'user': str(user),
                    'error': str(e)
                },
                exc_info=True
            )

    def get_decrypted_name(self):
        """Retrieve decrypted name."""
        if self.name:
            return cipher_suite.decrypt(self.name.encode()).decode()
        return self.name

    def get_decrypted_updated_name(self):
        """Retrieve decrypted name."""
        if self.updated_name:
            return cipher_suite.decrypt(self.updated_name.encode()).decode()
        return self.updated_name

    def get_decrypted_phone_number(self):
        """Retrieve decrypted phone number."""
        if self.phone_number:
            return cipher_suite.decrypt(self.phone_number.encode()).decode()
        return self.phone_number

    def get_decrypted_email(self):
        """Retrieve decrypted email."""
        if self.email:
            return cipher_suite.decrypt(self.email.encode()).decode()
        return self.email

    def get_decrypted_ip_address(self):
        """Retrieve decrypted IP address."""
        if self.ip_address:
            return cipher_suite.decrypt(self.ip_address.encode()).decode()
        return self.ip_address

    def get_decrypted_location(self):
        """Retrieve decrypted location."""
        if self.location:
            return cipher_suite.decrypt(self.location.encode()).decode()
        return self.location

    def get_decrypted_reference_signer_id(self):
        """Retrieve decrypted reference_id."""
        if self.reference_signer_id:
            return cipher_suite.decrypt(self.reference_signer_id.encode()).decode()
        return self.reference_signer_id

    def __str__(self):
        return f"{self.get_decrypted_email()} - {self.role}"


@receiver(post_save, sender=DocumentSigner)
def document_signer_post_save(sender, instance, created, **kwargs):
    # if created:
    #     signer_added.send(sender=sender, instance=instance, user=instance.document.user)
    pass


class ActivityLog(models.Model):
    signature_type_choices = (
        ("sweden_bank_id", "sweden_bank_id"),
        ("otp", "otp"),
        ("email", "email"),
        ("default", "default"),
    )
    document_refrence_id = models.CharField(max_length=255, null=True, blank=True)
    event_type = models.CharField(
        max_length=255
    )  # e.g., 'create', 'update', 'access', etc.
    category = models.CharField(max_length=255)
    title = models.CharField(max_length=255, null=True)
    message = models.TextField(
        null=True, blank=True
    )  # Optional detailed description of the event
    user = models.CharField(
        max_length=255, null=True, blank=True
    )  # User who performed the action
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    signer_type = models.CharField(
        choices=signature_type_choices, default="default", max_length=25
    )
    device_data = models.JSONField(null=True, blank=True)
    location = models.JSONField(null=True, blank=True)
    # location = models.CharField(max_length=255, null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    email = models.EmailField(null=True, blank=True)
    phone_number = models.CharField(max_length=250, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    def get_decrypted_phone_number(self):
        """Retrieve decrypted phone number."""
        if self.phone_number:
            return cipher_suite.decrypt(self.phone_number.encode()).decode()
        return self.phone_number

    def get_decrypted_email(self):
        """Retrieve decrypted email."""
        if self.email:
            return cipher_suite.decrypt(self.email.encode()).decode()
        return self.email

    def get_decrypted_title(self):
        return fernet_decrypt(self.title.encode()) if self.title else None

    def save(self, *args, **kwargs):
        try:
            is_new = not self.pk
            super().save(*args, **kwargs)

            if is_new:
                logger.info(
                    "New activity log created",
                    extra={
                        'event_type': self.event_type,
                        'category': self.category,
                        'document_id': self.document_refrence_id,
                        'user': self.user
                    }
                )
        except Exception as e:
            logger.error(
                "Error saving activity log",
                extra={
                    'event_type': self.event_type,
                    'error': str(e)
                },
                exc_info=True
            )
            raise

    def __str__(self):
        return f"{self.title} - {self.event_type} - {self.category} - {self.document_refrence_id}"

# class TemplateModel(models.Model):
#     category = models.CharField(max_length=255)  # Category for this entry
#     reference_id = models.CharField(
#         max_length=255, unique=True, blank=True, null=True
#     )  # Encrypted reference_id
#     active = models.BooleanField(
#         default=True
#     )  # Status of the template (active or inactive)
#     document_file = models.FileField(upload_to="documents/", null=True, blank=True)
#     title = models.CharField(max_length=25, null=True, blank=True)
#
#     # You can also add other fields as required, for example:
#     # name = models.CharField(max_length=255)
#     # email = models.EmailField()
#
#     def save(self, *args, **kwargs):
#         # If reference_id is not provided, generate a new UUID and encrypt it
#         if not self.reference_id:
#             # Generate a new UUID reference_id
#             raw_reference_id = str(uuid.uuid4())
#             # Encrypt the raw UUID reference_id before saving
#             self.reference_id = cipher_suite.encrypt(raw_reference_id.encode()).decode()
#
#         super().save(*args, **kwargs)  # Call the parent class save method
#
#     def __str__(self):
#         return f"""Category: {self.category}, Active: {self.active},
#         Reference ID: {self.reference_id}"""
#
#     def get_decrypted_reference_id(self):
#         """Utility method to get the decrypted reference_id"""
#         if self.reference_id:
#             return cipher_suite.decrypt(self.reference_id.encode()).decode()
#         return None


class OrganisationContacts(models.Model):
    organisation = models.ForeignKey(
        Organization, on_delete=models.CASCADE, related_name='contacts')
    email = models.EmailField(null=True, blank=True)
    phone_number = models.CharField(max_length=20, null=True, blank=True)
    bank_id = models.CharField(max_length=255, null=True, blank=True)
    name = models.CharField(max_length=100, null=True, blank=True)

    def __str__(self):
        return f"{self.name}"


class UserDocumentDirectory(models.Model):
    id = models.UUIDField(
        default=uuid.uuid4, unique=True, primary_key=True, editable=False
    )
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='directories', null=True,
                                     blank=True)
    title = models.CharField(max_length=255, null=True, blank=True)
    is_default = models.BooleanField(default=False)  # To mark "Agreements" directory
    is_active = models.BooleanField(default=True)  # For archiving directories
    owner = models.ForeignKey(User, on_delete=models.SET_NULL,
                              null=True, related_name='owned_directories')
    created_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name='created_directories')
    updated_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name='updated_directories')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        # Ensure unique directory names per organization
        unique_together = ('organization', 'title')

    def save(self, *args, **kwargs):
        # If this is a default directory, ensure only one exists per organization
        if self.is_default:
            UserDocumentDirectory.objects.filter(
                organization=self.organization,
                is_default=True
            ).exclude(id=self.id).update(is_default=False)
        super().save(*args, **kwargs)

    def archive(self, user):
        """Archive directory and move documents to default directory"""
        try:
            # Check if directory has any active documents
            if self.documents.filter(is_active=True).exists():
                raise ValueError(
                    "Move or delete documents before deleting directory.")

            # Add UUID to the title before archiving
            unique_suffix = f"__{uuid.uuid4()}"
            self.title = f"{self.title}{unique_suffix}"
            self.is_active = False
            self.updated_by = user
            self.save()

        except UserDocumentDirectory.DoesNotExist:
            raise ValueError("Default directory not found for this organization")

    def delete(self, *args, **kwargs):
        # Check if directory has any active documents
        if self.documents.filter(is_active=True).exists():
            raise ValueError(
                "Move or delete documents before deleting directory.")

        # Only allow deletion if no active documents exist
        super().delete(*args, **kwargs)


class DirectoryDocument(models.Model):
    id = models.UUIDField(
        default=uuid.uuid4, unique=True, primary_key=True, editable=False
    )
    directory = models.ForeignKey(
        UserDocumentDirectory, on_delete=models.CASCADE, related_name='documents', null=True, blank=True)
    document = models.ForeignKey(OrganizationDocument, on_delete=models.CASCADE,
                                 related_name='directory_associations', null=True, blank=True)
    added_at = models.DateTimeField(default=timezone.now)
    removed_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)
    owner = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    added_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name='added_directory_documents')
    removed_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name='removed_directory_documents')
    removed_from = models.ForeignKey(
        UserDocumentDirectory, on_delete=models.SET_NULL, null=True, related_name='removed_documents')

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['directory', 'document'],
                condition=models.Q(is_active=True),
                name='unique_active_document_in_directory'
            )
        ]

    def move_to_directory(self, new_directory, user):
        """Move document to a new directory"""
        # Check if document is signed by all users
        if not self._is_fully_signed():
            raise ValueError("Document cannot be moved until all signers have signed")

        if not new_directory.organization == self.directory.organization:
            raise ValueError(
                "Cannot move document to directory of different organization")

        self.is_active = False
        self.removed_at = timezone.now()
        self.removed_by = user
        self.removed_from = self.directory
        self.save()

        # Create new directory association
        DirectoryDocument.objects.create(
            directory=new_directory,
            document=self.document,
            added_by=user
        )

    def _is_fully_signed(self):
        """Check if document has been signed by all signers"""
        # Get all active signers for the document
        signers = DocumentSigner.objects.filter(
            document=self.document.user_document,
            is_active=True
        )

        # If no signers, consider document as signed
        if not signers.exists():
            return True

        # Check if any signers have pending or approved status
        pending_signers = signers.filter(status__in=['pending', 'approved'])
        return not pending_signers.exists()

# @receiver(post_save, sender=OrganizationDocument)
# def create_default_directory_and_association(sender, instance, created, **kwargs):
#     if created:
#         # Get or create default "Agreements" directory
#         default_directory, created = UserDocumentDirectory.objects.get_or_create(
#             organization=instance.organization,
#             defaults={
#                 'title': 'Agreements',
#                 'is_default': True,
#                 'created_by': instance.user_document.user
#             }
#         )
#
#         # Create association with default directory
#         DirectoryDocument.objects.create(
#             directory=default_directory,
#             document=instance,
#             added_by=instance.user_document.user
#         )


class DocumentAccessRequest(models.Model):
    """Model to handle document access requests from organization members"""

    status_choices = (
        ("pending", "pending"),
        ("approved", "approved"),
        ("rejected", "rejected"),
        ("revoked", "revoked"),  # Add revoked status
    )

    id = models.UUIDField(
        default=uuid.uuid4, unique=True, primary_key=True, editable=False
    )

    # The document being requested
    document = models.ForeignKey(
        UserDocument,
        on_delete=models.CASCADE,
        related_name='access_requests'
    )

    # The user requesting access
    requester = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='document_requests'
    )

    # The organization context
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='document_access_requests'
    )

    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)
    # Request status
    status = models.CharField(
        max_length=20,
        choices=status_choices,
        default="pending"
    )

    # Optional reason for request
    request_reason = models.TextField(null=True, blank=True)

    # Response details
    response_note = models.TextField(null=True, blank=True)
    responded_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='document_access_responses'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    responded_at = models.DateTimeField(null=True, blank=True)

    # Add new fields for revocation
    revoked_at = models.DateTimeField(null=True, blank=True)
    revoked_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='document_access_revocations'
    )
    revocation_note = models.TextField(null=True, blank=True)

    def save(self, *args, **kwargs):
        try:
            is_new = not self.pk
            super().save(*args, **kwargs)

            if is_new:
                logger.info(
                    "New document access request created",
                    extra={
                        'request_id': str(self.id),
                        'document_id': str(self.document.id),
                        'requester_id': str(self.requester.id),
                        'organization_id': str(self.organization.id)
                    }
                )
        except Exception as e:
            logger.error(
                "Error saving document access request",
                extra={
                    'request_id': str(self.id),
                    'error': str(e)
                },
                exc_info=True
            )
            raise

    def approve(self, approver, note=None):
        """Approve the access request"""
        self.status = "approved"
        self.responded_by = approver
        self.response_note = note
        self.responded_at = timezone.now()
        self.save()

    def reject(self, rejector, note=None):
        """Reject the access request"""
        self.status = "rejected"
        self.responded_by = rejector
        self.response_note = note
        self.responded_at = timezone.now()
        self.save()

    def revoke(self, revoker, note=None):
        """Revoke an approved access request"""
        if self.status != "approved":
            raise ValueError(f"Cannot revoke request with status: {self.status}")

        try:
            logger.info(
                "Revoking document access",
                extra={
                    'request_id': str(self.id),
                    'document_id': str(self.document.id),
                    'revoked_by': str(revoker.id)
                }
            )

            self.status = "revoked"
            self.revoked_by = revoker
            self.revoked_at = timezone.now()
            self.revocation_note = note
            self.save()

        except Exception as e:
            logger.error(
                "Error revoking document access",
                extra={
                    'request_id': str(self.id),
                    'error': str(e)
                },
                exc_info=True
            )
            raise

    class Meta:
        ordering = ['-created_at']
        # Ensure user can't make multiple pending requests for same document
        constraints = [
            models.UniqueConstraint(
                fields=['document', 'requester'],
                condition=models.Q(status='pending'),
                name='unique_pending_request'
            )
        ]

    def __str__(self):
        return f"Access request for {self.document} by {self.requester}"


class DocumentVerificationData(models.Model):
    """
    Model to store document verification data including OTP and verification tokens
    that were previously stored in cookies.
    """
    document = models.ForeignKey(
        'UserDocument', on_delete=models.CASCADE, related_name='verification_data')
    signer = models.ForeignKey(
        'DocumentSigner', on_delete=models.CASCADE, related_name='verification_data')
    otp = models.CharField(max_length=255, null=True, blank=True)  # Hashed OTP
    otp_expiry = models.DateTimeField(null=True, blank=True)
    verification_token = models.CharField(
        max_length=255, null=True, blank=True)  # Hashed verification token
    token_expiry = models.DateTimeField(null=True, blank=True)
    is_verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        unique_together = ('document', 'signer')
        indexes = [
            models.Index(fields=['document', 'signer']),
            models.Index(fields=['otp_expiry']),
            models.Index(fields=['token_expiry']),
        ]

    def __str__(self):
        return f"Verification data for Document {self.document.reference_id} - Signer {self.signer.reference_signer_id}"
