from django.core.management.base import BaseCommand
from django.utils import timezone
from django.conf import settings
from django.core.mail import send_mail

from documents.models import UserDocument
import logging

logger = logging.getLogger('app')

class Command(BaseCommand):
    help = 'Check for expired documents and send notifications'

    def handle(self, *args, **options):
        try:
            expired_documents = UserDocument.get_expired_documents()
            
            for document in expired_documents:
                if document.user and document.user.email:
                    # Send email notification
                    subject = 'Document Expired'
                    message = f'''Your document "{document.title}" has expired.
                                Document ID: {document.id}
                                Expiration Date: {document.expiration_date}'''
                    
                    send_mail(
                        subject=subject,
                        message=message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[document.user.email],
                        fail_silently=False,
                    )

                    # Mark document as notified by setting meta_data
                    document.meta_data = {
                        'expiration_notified': True,
                        'notification_sent_at': timezone.now().isoformat()
                    }
                    document.save()

                    logger.info(
                        "Sent document expiration notification",
                        extra={
                            'document_id': str(document.id),
                            'owner_id': str(document.owner.id),
                            'created_by_id': str(document.created_by.id),
                            'expiration_date': str(document.expiration_date)
                        }
                    )

            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully processed {expired_documents.count()} expired documents'
                )
            )

        except Exception as e:
            logger.error(
                "Error in check_expired_documents command",
                extra={'error': str(e)},
                exc_info=True
            )
            raise 