import logging
from datetime import datetime, timezone

import jwt  # type: ignore
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from django.http import HttpRequest
from django.utils.translation import gettext as _
from rest_framework import authentication, exceptions
from accounts.services.mobile_authentication.mobile_utils import is_token_blacklisted
# from esign.utils.custom_response import api_response
from rest_framework.exceptions import ValidationError


from accounts.models.users import BlacklistedToken
from accounts.services.jwt_authentication.authentication import fernet_decrypt

User = get_user_model()
logger = logging.getLogger(__name__)


class JWTAccessTokenAuthentication(authentication.BaseAuthentication):
    def __init__(self, *args, **kwargs):
        self.secret_key = settings.SECRET_KEY  # Secret key to decode JWT
        self.algorithm = "HS256"  # Algorithm used to encode JWT

    def authenticate(self, request: HttpRequest):
        authentication_token = request.META.get("HTTP_AUTHORIZATION")
        if not authentication_token:
            logger.warning("Authorization token missing in request")
            raise exceptions.AuthenticationFailed(_("Authorization token missing"))

        if not authentication_token.startswith("Bearer "):
            logger.warning(f"Invalid authorization header: {authentication_token}")
            raise exceptions.AuthenticationFailed(_("Invalid authorization header"))

        # Get the actual token
        token = authentication_token.split(" ")[1]
        # print(token)
        # Check if the token is blacklisted
        if is_token_blacklisted(token):  # Implement this function
            # is_blacklisted = True
            logger.warning("Token is blacklisted")
            # raise ValidationError(detail=_("Exception Message"),code=410)
            raise exceptions.NotAcceptable(_("Token is blacklisted"))

        try:
            
            logger.info("Attempting to decode JWT token")
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            logger.debug(f"Decoded JWT payload: {payload}")
            reference_id = fernet_decrypt(payload["reference_id"].encode())
            logger.info(f"Decrypted reference_id from token: {reference_id}")

            # Retrieve the user by reference ID
            try:
                user = User.objects.get(reference_id=reference_id)
                logger.info(f"User found with reference_id: {reference_id}")
            except User.DoesNotExist:
                logger.error(f"User not found with reference_id: {reference_id}")
                raise exceptions.AuthenticationFailed(_("User not found"))
            logger.info(f"Authentication successful for user: {user}")
            
            # Return the user and token for successful authentication
            return (user, token)

        except jwt.ExpiredSignatureError:
            logger.error("JWT token has expired")
            raise exceptions.AuthenticationFailed({
                "status": "error",
                "message": "Your session has expired. Please refresh your token.",
                "code": "token_expired",
                "is_token_expired": True
            })
        except jwt.InvalidTokenError:
            logger.error("Invalid JWT token")
            raise exceptions.AuthenticationFailed({
                "status": "error",
                "message": "Invalid authentication token",
                "code": "invalid_token"
            })


class OneTimeJWTTokenAuthentication(authentication.BaseAuthentication):
    def __init__(self, *args, **kwargs):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = "HS256"

    def authenticate(self, request: HttpRequest, *args, **kwargs):
        # Check for token in headers and validate format
        auth_token = request.META.get("HTTP_AUTHORIZATION")
        if not auth_token or not auth_token.startswith("Bearer "):
            raise exceptions.AuthenticationFailed(
                _("Authorization token missing or invalid")
            )

        token = auth_token.split(" ")[1]

        try:
            # Decode JWT and retrieve user by reference_id
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            reference_id = fernet_decrypt(payload["reference_id"].encode())
            user = User.objects.get(reference_id=reference_id)

            # Retrieve and normalize last_login values for comparison
            payload_last_login = (
                datetime.fromisoformat(payload.get("last_login")).replace(
                    tzinfo=timezone.utc
                )
                if payload.get("last_login")
                else None
            )
            user_last_login = (
                user.last_login.replace(microsecond=0, tzinfo=timezone.utc)
                if user.last_login
                else None
            )

            # Check that last_login in both payload and user are identical
            if user_last_login == payload_last_login:
                # Update last_login to current time and return authenticated user
                user.last_login = datetime.now(timezone.utc)
                user.save(update_fields=["last_login"])
                return (user, token)
            else:
                raise exceptions.AuthenticationFailed(_("Invalid token"))

        except jwt.ExpiredSignatureError:
            raise exceptions.AuthenticationFailed(_("Token has expired"))
        except jwt.InvalidTokenError:
            raise exceptions.AuthenticationFailed(_("Invalid token"))
        except User.DoesNotExist:
            raise exceptions.AuthenticationFailed(_("User not found"))
        except ValueError:
            raise exceptions.AuthenticationFailed(
                _("Invalid last_login format in token")
            )


class OneTimeAcessJWTTokenAuthentication(authentication.BaseAuthentication):
    def __init__(self, *args, **kwargs):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = "HS256"

    def is_token_blacklisted(self, token):
        """Check if a token is blacklisted."""
        try:
            blacklisted_token = BlacklistedToken.objects.get(token=token)
            print(blacklisted_token)
            logger.warning(f"Token is blacklisted: {token}")
            return True
        except ObjectDoesNotExist:
            return False

    def blacklist_token(self, token):
        """Blacklist a one-time token."""
        blacklisted_token = BlacklistedToken(token=token)
        blacklisted_token.save()
        logger.info(f"Token added to blacklist: {token}")

    def authenticate(self, request: HttpRequest, *args, **kwargs):
        # Check for token in headers and validate format
        auth_token = request.META.get("HTTP_AUTHORIZATION")
        if not auth_token or not auth_token.startswith("Bearer "):
            logger.warning("Authorization token missing in request")
            raise exceptions.AuthenticationFailed(
                _("Authorization token missing or invalid")
            )

        token = auth_token.split(" ")[1]
        if "onetime" in token:
            token = token[7:]
            logger.info(f"One-time access token identified: {token}")
        else:
            logger.warning(
                f"Token does not appear to be a one-time access token: {token}"
            )
            raise exceptions.AuthenticationFailed(
                _("This is not a one-time access token")
            )

        # Check if the token is blacklisted
        # if self.is_token_blacklisted(token):
        #     logger.warning(f"Attempted use of blacklisted token: {token}")
        #     raise exceptions.AuthenticationFailed(
        #         _("Token is blacklisted and cannot be used again")
        #     )

        try:
            logger.info(f"Attempting to decode JWT token: {token}")
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            reference_id = fernet_decrypt(payload["reference_id"].encode())
            logger.info(f"Decrypted reference_id from token: {reference_id}")
            # Retrieve the user by reference ID
            try:
                user = User.objects.get(reference_id=reference_id)
                logger.info(f"User found with reference_id: {reference_id}")
            except User.DoesNotExist:
                raise exceptions.AuthenticationFailed(_("User not found"))

            self.blacklist_token(token)

            logger.info(f"Authentication successful for user: {user}")
            # Return the user and token for successful authentication
            return (user, token)

        except jwt.ExpiredSignatureError:
            logger.error("JWT token has expired")
            raise exceptions.AuthenticationFailed(_("Token has expired"))
        except jwt.InvalidTokenError:
            logger.error("Invalid JWT token")
            raise exceptions.AuthenticationFailed(_("Invalid token"))
        except User.DoesNotExist:
            raise exceptions.AuthenticationFailed(_("User not found"))
        except ValueError:
            logger.error("Invalid login format in token")
            raise exceptions.AuthenticationFailed(_("Invalid login format in token"))
