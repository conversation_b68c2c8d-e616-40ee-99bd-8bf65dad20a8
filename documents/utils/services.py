import logging
import uuid
from datetime import datetime, timedelta, timezone

import jwt  # type: ignore
from django.conf import settings

from accounts.services.jwt_authentication.authentication import fernet_encrypt
from documents.models.document_models import UserDocument
from subscriptions.models.subscription_masters import UserSubscription

# from rest_framework.pagination import PageNumberPagination


logger = logging.getLogger(__name__)


def get_file_extension(file_name):
    try:
        name, extension = file_name.rsplit(".", 1)
    except ValueError:
        extension = ""

    return f"{uuid.uuid4()}.{extension}" if extension else str(uuid.uuid4())


def generate_one_time_token(user):
    """
    Generate a JWT token with encrypted user details and expiration.

    :param user: The user object for whom the token is generated.
    :return: Encoded JWT token.
    """
    logger.info("Generating one-time token for user ")
    expiration = datetime.now(tz=timezone.utc) + timedelta(minutes=10)
    logger.info("Token expiration set to: {expiration.isoformat()}")
    # Convert last_login to UTC ISO format if available, else empty string
    login_timestamp = (
        user.last_login.astimezone(timezone.utc).replace(microsecond=0).isoformat()
        if user.last_login
        else ""
    )

    payload = {
        "reference_id": fernet_encrypt(user.reference_id).decode(),
        "exp": expiration,
        "last_login": login_timestamp,
    }

    logger.debug(f"Token payload: {payload}")

    return jwt.encode(payload, settings.ONE_TIME_SECRET_KEY, algorithm="HS256")


def user_storage_usages(user=None):
    """
    Calculate the total storage usage for a user.

    :param user: The user object for whom to calculate storage usage.
    :return: Total storage usage in bytes.
    """
    if user is None:
        logger.warning("No user provided, defaulting to first user in database.")
        return 0

    total_storage = 0
    for document in UserDocument.objects.filter(user=user):
        if document.document_file:
            total_storage += document.file_size

    return total_storage

def get_user_plan_storage_limit(user):
    """
    Get the storage limit for a user's plan.

    :param user: The user object for whom to get the storage limit.
    :return: Storage limit in bytes.
    """
    if user is None:
        logger.warning("No user provided, defaulting to first user in database.")
        return 0

    subscription = UserSubscription.objects.select_related('plan').filter(user=user, status='active').first()

    if subscription and subscription.plan:
        return subscription.plan.storage_allowed

    return 0
