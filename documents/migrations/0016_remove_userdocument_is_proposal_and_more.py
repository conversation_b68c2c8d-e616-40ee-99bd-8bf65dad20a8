# Generated by Django 5.1.1 on 2025-09-26 07:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("documents", "0015_userdocument_is_proposal_userdocument_proposal"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="userdocument",
            name="is_proposal",
        ),
        migrations.RemoveField(
            model_name="userdocument",
            name="proposal",
        ),
        migrations.AddField(
            model_name="userdocument",
            name="document_type",
            field=models.CharField(
                choices=[
                    ("agreement", "Agreement Document"),
                    ("proposal", "Proposal Document"),
                    ("contract", "Contract Document"),
                    ("invoice", "Invoice Document"),
                    ("purchase_order", "Purchase Order"),
                    ("nda", "Non-Disclosure Agreement"),
                    ("other", "Other Document"),
                ],
                default="agreement",
                help_text="Type of document for categorization and workflow management",
                max_length=20,
            ),
        ),
    ]
