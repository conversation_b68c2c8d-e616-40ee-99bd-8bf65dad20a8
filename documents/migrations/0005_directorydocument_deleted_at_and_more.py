# Generated by Django 5.1.1 on 2025-06-26 18:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("documents", "0004_directorydocument_is_deleted_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="directorydocument",
            name="deleted_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="documentaccessrequest",
            name="deleted_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="documentsigner",
            name="deleted_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="documentverificationdata",
            name="deleted_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="organizationdocument",
            name="deleted_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="userdocument",
            name="deleted_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
