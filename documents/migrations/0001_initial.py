# Generated by Django 5.1.1 on 2025-03-24 17:09

import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("accounts", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ActivityLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "document_refrence_id",
                    models.Char<PERSON>ield(blank=True, max_length=255, null=True),
                ),
                ("event_type", models.Char<PERSON>ield(max_length=255)),
                ("category", models.Char<PERSON>ield(max_length=255)),
                ("title", models.Char<PERSON>ield(max_length=255, null=True)),
                ("message", models.TextField(blank=True, null=True)),
                ("user", models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                (
                    "signer_type",
                    models.Char<PERSON>ield(
                        choices=[
                            ("sweden_bank_id", "sweden_bank_id"),
                            ("otp", "otp"),
                            ("email", "email"),
                            ("default", "default"),
                        ],
                        default="default",
                        max_length=25,
                    ),
                ),
                ("device_data", models.JSONField(blank=True, null=True)),
                ("location", models.JSONField(blank=True, null=True)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("email", models.EmailField(blank=True, max_length=254, null=True)),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=250, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, null=True)),
                ("updated_at", models.DateTimeField(auto_now=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="OrganisationContacts",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(blank=True, max_length=254, null=True)),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                ("bank_id", models.CharField(blank=True, max_length=255, null=True)),
                ("name", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "organisation",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="contacts",
                        to="accounts.organization",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="UserDocument",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "reference_id",
                    models.CharField(
                        blank=True,
                        editable=False,
                        max_length=255,
                        null=True,
                        unique=True,
                    ),
                ),
                ("email_message", models.TextField(blank=True, null=True)),
                ("title", models.CharField(blank=True, max_length=255, null=True)),
                ("download_pdf", models.BooleanField(default=True)),
                (
                    "previous_status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("draft", "draft"),
                            ("sent", "sent"),
                            ("cancelled", "cancelled"),
                            ("trash", "trash"),
                        ],
                        help_text="Stores the status before moving to trash",
                        max_length=70,
                        null=True,
                    ),
                ),
                (
                    "trashed_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Timestamp when document was moved to trash",
                        null=True,
                    ),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "draft"),
                            ("sent", "sent"),
                            ("cancelled", "cancelled"),
                            ("trash", "trash"),
                        ],
                        default="draft",
                        max_length=70,
                    ),
                ),
                ("document_file", models.FileField(upload_to="documents/")),
                (
                    "latest_document_file",
                    models.FileField(blank=True, null=True, upload_to="documents/"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "transaction_id",
                    models.UUIDField(
                        blank=True, default=uuid.uuid4, editable=False, null=True
                    ),
                ),
                (
                    "bank_id_transaction_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("language", models.CharField(blank=True, max_length=255, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("location", models.CharField(blank=True, max_length=255, null=True)),
                ("gdpr_compliance_status", models.BooleanField(default=False)),
                ("encryption_status", models.BooleanField(default=True)),
                ("access_log", models.TextField(blank=True, null=True)),
                ("last_accessed_at", models.DateTimeField(blank=True, null=True)),
                ("document_password", models.CharField(max_length=100)),
                ("expiration_date", models.DateTimeField(blank=True, null=True)),
                ("is_expired", models.BooleanField(default=False)),
                ("thumbnail_path", models.TextField(blank=True, null=True)),
                (
                    "thumbnail_file",
                    models.FileField(blank=True, null=True, upload_to=""),
                ),
                ("is_global", models.BooleanField(default=False)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="OrganizationDocument",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("is_global", models.BooleanField(default=False)),
                ("encrypted_reference_id", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="accounts.organization",
                    ),
                ),
                (
                    "user_document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="documents.userdocument",
                    ),
                ),
            ],
            options={
                "unique_together": {("user_document", "organization")},
            },
        ),
        migrations.CreateModel(
            name="DocumentSigner",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("is_name_editable", models.BooleanField(default=False)),
                (
                    "reference_signer_id",
                    models.CharField(
                        blank=True,
                        editable=False,
                        max_length=255,
                        null=True,
                        unique=True,
                    ),
                ),
                ("hide_security_number", models.BooleanField(default=False)),
                (
                    "signer_type",
                    models.CharField(
                        choices=[
                            ("sweden_bank_id", "sweden_bank_id"),
                            ("otp", "otp"),
                            ("email", "email"),
                            ("default", "default"),
                        ],
                        default="default",
                        max_length=25,
                    ),
                ),
                ("name", models.CharField(blank=True, max_length=250, null=True)),
                (
                    "updated_name",
                    models.CharField(blank=True, max_length=250, null=True),
                ),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=250, null=True),
                ),
                ("email", models.EmailField(max_length=254)),
                ("signature_image", models.TextField(blank=True, null=True)),
                (
                    "role",
                    models.CharField(
                        choices=[("signer", "signer"), ("approver", "approver")],
                        default="signer",
                        max_length=70,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "pending"),
                            ("approved", "approved"),
                            ("signed", "signed"),
                        ],
                        default="pending",
                        max_length=10,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("meta_data", models.JSONField(blank=True, null=True)),
                ("signed_at", models.DateTimeField(blank=True, null=True)),
                ("gdpr_consent", models.BooleanField(default=False)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("location", models.CharField(blank=True, max_length=255, null=True)),
                ("access_log", models.TextField(blank=True, null=True)),
                ("bank_id_meta_data", models.JSONField(blank=True, null=True)),
                ("bank_id_verified", models.BooleanField(default=False)),
                ("verification_timestamp", models.DateTimeField(blank=True, null=True)),
                (
                    "bank_id_reference",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("last_reminder_sent", models.DateTimeField(blank=True, null=True)),
                ("reminder_count", models.IntegerField(default=0)),
                (
                    "document",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="signers",
                        to="documents.userdocument",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="UserDocumentDirectory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("title", models.CharField(blank=True, max_length=255, null=True)),
                ("is_default", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_directories",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="directories",
                        to="accounts.organization",
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="updated_directories",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("organization", "title")},
            },
        ),
        migrations.CreateModel(
            name="DocumentAccessRequest",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "pending"),
                            ("approved", "approved"),
                            ("rejected", "rejected"),
                            ("revoked", "revoked"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("request_reason", models.TextField(blank=True, null=True)),
                ("response_note", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("responded_at", models.DateTimeField(blank=True, null=True)),
                ("revoked_at", models.DateTimeField(blank=True, null=True)),
                ("revocation_note", models.TextField(blank=True, null=True)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="document_access_requests",
                        to="accounts.organization",
                    ),
                ),
                (
                    "requester",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="document_requests",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "responded_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="document_access_responses",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "revoked_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="document_access_revocations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="access_requests",
                        to="documents.userdocument",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "constraints": [
                    models.UniqueConstraint(
                        condition=models.Q(("status", "pending")),
                        fields=("document", "requester"),
                        name="unique_pending_request",
                    )
                ],
            },
        ),
        migrations.CreateModel(
            name="DirectoryDocument",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                ("added_at", models.DateTimeField(default=django.utils.timezone.now)),
                ("removed_at", models.DateTimeField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "added_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="added_directory_documents",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "removed_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="removed_directory_documents",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "document",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="directory_associations",
                        to="documents.organizationdocument",
                    ),
                ),
                (
                    "directory",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="documents.userdocumentdirectory",
                    ),
                ),
                (
                    "removed_from",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="removed_documents",
                        to="documents.userdocumentdirectory",
                    ),
                ),
            ],
            options={
                "constraints": [
                    models.UniqueConstraint(
                        condition=models.Q(("is_active", True)),
                        fields=("directory", "document"),
                        name="unique_active_document_in_directory",
                    )
                ],
            },
        ),
    ]
