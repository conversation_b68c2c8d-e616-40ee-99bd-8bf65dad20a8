# Generated by Django 5.1.1 on 2025-05-23 05:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("documents", "0002_userdocument_file_size"),
    ]

    operations = [
        migrations.CreateModel(
            name="DocumentVerificationData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("otp", models.CharField(blank=True, max_length=255, null=True)),
                ("otp_expiry", models.DateTimeField(blank=True, null=True)),
                (
                    "verification_token",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("token_expiry", models.DateTimeField(blank=True, null=True)),
                ("is_verified", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="verification_data",
                        to="documents.userdocument",
                    ),
                ),
                (
                    "signer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="verification_data",
                        to="documents.documentsigner",
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["document", "signer"],
                        name="documents_d_documen_1a8ee3_idx",
                    ),
                    models.Index(
                        fields=["otp_expiry"], name="documents_d_otp_exp_b3bb07_idx"
                    ),
                    models.Index(
                        fields=["token_expiry"], name="documents_d_token_e_e7237c_idx"
                    ),
                ],
                "unique_together": {("document", "signer")},
            },
        ),
    ]
