# Generated by Django 5.1.1 on 2025-07-10 05:40

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("documents", "0007_remove_userdocument_user_directorydocument_owner_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="userdocument",
            name="deleted_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="deleted_documents",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="userdocument",
            name="previous_status",
            field=models.CharField(
                blank=True,
                choices=[
                    ("draft", "draft"),
                    ("sent", "sent"),
                    ("cancelled", "cancelled"),
                    ("trash", "trash"),
                    ("withdrawn", "withdrawn"),
                ],
                help_text="Stores the status before moving to trash",
                max_length=70,
                null=True,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="userdocument",
            name="status",
            field=models.Char<PERSON>ield(
                choices=[
                    ("draft", "draft"),
                    ("sent", "sent"),
                    ("cancelled", "cancelled"),
                    ("trash", "trash"),
                    ("withdrawn", "withdrawn"),
                ],
                default="draft",
                max_length=70,
            ),
        ),
    ]
