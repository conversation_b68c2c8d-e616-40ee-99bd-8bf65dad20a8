from rest_framework import serializers

from accounts.serializers.mobile_auth_serializers import UserProfileRetrieveSerializer
from documents.models import DocumentAccessRequest
from documents.serializers.document_serializers import UserDocumentSerializer


class DocumentAccessRequestSerializer(serializers.ModelSerializer):
    requester_details = UserProfileRetrieveSerializer(
        source='requester', read_only=True)
    responded_by_details = UserProfileRetrieveSerializer(
        source='responded_by', read_only=True)
    document_details = UserDocumentSerializer(source='document', read_only=True)
    responder_details = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = DocumentAccessRequest
        fields = [
            'id',
            'document',
            'document_details',
            'requester',
            'requester_details',
            'organization',
            'status',
            'request_reason',
            'response_note',
            'responded_by',
            'responded_by_details',
            'created_at',
            'updated_at',
            'responded_at',
            'responder_details',
        ]
        read_only_fields = [
            'status',
            'responded_by',
            'responded_at',
            'created_at',
            'updated_at'
            'responder_details',
        ]

    def validate(self, data):
        # Ensure requester doesn't already have a pending request for this document
        document = data.get('document')
        requester = data.get('requester')

        if DocumentAccessRequest.objects.filter(
            document=document,
            requester=requester,
            status='pending'
        ).exists():
            raise serializers.ValidationError(
                "You already have a pending request for this document"
            )

        # Ensure requester isn't the document owner
        if document.owner == requester or document.created_by == requester:
            raise serializers.ValidationError(
                "You cannot request access to your own document"
            )

        return data

    def get_responder_details(self, obj):
        user = getattr(obj.document, 'created_by', None)
        if user:
            return UserProfileRetrieveSerializer(user).data

        return None
