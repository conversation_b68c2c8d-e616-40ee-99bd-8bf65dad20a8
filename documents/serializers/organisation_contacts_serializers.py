from rest_framework import serializers
from documents.models.document_models import OrganisationContacts


class OrganisationContactsSerializer(serializers.ModelSerializer):
    organisation = serializers.CharField(required=False)

    class Meta:
        model = OrganisationContacts
        fields = ['id', 'organisation', 'email', 'phone_number', 'bank_id', 'name', 'assigner_org_name', 'assigner_org_address', 'assigner_postal_code']
