# import base64
import logging
import uuid
from datetime import datetime

import pytz  # You might need to install this: pip install pytz
from accounts.models.users import User
from accounts.services.bankid_authentication.bankid_utils import get_client_ip
from accounts.services.jwt_authentication.authentication import fernet_decrypt
from cryptography.fernet import Fernet
from django.conf import settings
from django.core.files.base import ContentFile
from django.utils.timezone import localtime
from documents.models import (
    ActivityLog,
    DocumentSigner,
    OrganizationDocument,
    UserDocument,
)
from documents.utils.services import get_file_extension
from rest_framework import serializers

logger = logging.getLogger(__name__)
# Encryption setup using the same key as in the models
cipher_suite = Fernet(settings.ENCRYPTION_KEY)


class DocumentSignerSerializer(serializers.ModelSerializer):
    # Decrypt name, phone number, email,
    # and reference_signer_id when reading data, and encrypt them when writing
    # name = serializers.SerializerMethodField()
    # phone_number = serializers.SerializerMethodField()
    # email = serializers.SerializerMethodField()
    reference_signer_id = serializers.SerializerMethodField()
    # document_refrence_id = serializers.SerializerMethodField()

    class Meta:
        model = DocumentSigner
        fields = (
            "id",
            "reference_signer_id",
            "document",
            "bank_id_meta_data",
            # "document_refrence_id",
            "signature_image",
            "hide_security_number",
            "signer_type",
            "name",
            "phone_number",
            "role",
            "status",
            "email",
            "meta_data",
            "created_at",
            "updated_at",
            "is_name_editable",
            "hide_security_number",
            "ip_address",
        )

    def to_representation(self, instance):
        # Modify the data representation to include decrypted fields
        representation = super().to_representation(instance)
        # Add decrypted data

        if instance.name:
            representation["name"] = fernet_decrypt(instance.name.encode())
        if instance.phone_number:
            representation["phone_number"] = fernet_decrypt(
                instance.phone_number.encode()
            )
        if instance.email:
            representation["email"] = fernet_decrypt(instance.email.encode())

        logger.info(f"Decrypted fields for DocumentSigner ID {instance.id}")

        return representation

    def get_reference_signer_id(self, obj):
        # Decrypt the reference_signer_id field
        if obj.reference_signer_id:
            return obj.reference_signer_id
        return None

    def validate_email(self, value):
        # Add custom email validation if required
        if not value or "@" not in value:
            raise serializers.ValidationError("A valid email address is required.")
        return value

    def create(self, validated_data):
        # Encrypt the fields before saving them
        if "name" in validated_data:
            validated_data["name"] = cipher_suite.encrypt(
                validated_data["name"].encode()
            ).decode()
            logger.info("Encrypted 'name' field for DocumentSigner")
        if "phone_number" in validated_data:
            validated_data["phone_number"] = cipher_suite.encrypt(
                validated_data["phone_number"].encode()
            ).decode()
            logger.info("Encrypted 'phone_number' field for DocumentSigner")
        if "email" in validated_data:
            validated_data["email"] = cipher_suite.encrypt(
                validated_data["email"].encode()
            ).decode()
            logger.info("Encrypted 'email' field for DocumentSigner")
        instance = DocumentSigner(**validated_data)
        instance.save()
        # instance = super().create(validated_data)
        return instance

    def update(self, instance, validated_data):
        # Encrypt the fields before updating

        if "name" in validated_data and validated_data["name"] is not None:
            instance.name = cipher_suite.encrypt(
                validated_data["name"].encode()
            ).decode()
            logger.info(f"Encrypted 'name' field for DocumentSigner ID {instance.id}")
        if (
            "phone_number" in validated_data
            and validated_data["phone_number"] is not None
        ):
            instance.phone_number = cipher_suite.encrypt(
                validated_data["phone_number"].encode()
            ).decode()
            logger.info(
                f"Encrypted 'phone_number' field for DocumentSigner ID {instance.id}"
            )
        if "email" in validated_data and validated_data["email"] is not None:
            instance.email = cipher_suite.encrypt(
                validated_data["email"].encode()
            ).decode()
            logger.info(f"Encrypted 'email' field for DocumentSigner ID {instance.id}")
        if "role" in validated_data and validated_data["role"] is not None:
            instance.role = validated_data["role"]
            logger.info(f"Encrypted 'role' field for DocumentSigner ID {instance.id}")
        if "status" in validated_data and validated_data["status"] is not None:
            instance.status = validated_data["status"]
            logger.info(f"Encrypted 'status' field for DocumentSigner ID {instance.id}")
        if (
            "signature_image" in validated_data
            and validated_data["signature_image"] is not None
        ):
            instance.signature_image = validated_data["signature_image"]
            logger.info(
                f" 'signature_image' field for DocumentSigner ID {instance.signature_image}"
            )
        if (
            "bank_id_meta_data" in validated_data
            and validated_data["bank_id_meta_data"] is not None
        ):
            instance.bank_id_meta_data = validated_data["bank_id_meta_data"]

        if ("ip_address" in validated_data and validated_data["ip_address"]):
            instance.ip_address = validated_data["ip_address"]

        if ("hide_security_number" in validated_data and validated_data["hide_security_number"]):
            instance.hide_security_number = validated_data["hide_security_number"]

        instance.save()
        return instance


class UserDocumentListCountSerializer(serializers.ModelSerializer):
    # Include Signers
    signers = DocumentSignerSerializer(many=True, read_only=True)

    class Meta:
        model = UserDocument
        fields = ["id", "title", "status", "signers",
                  "created_at", "updated_at", "reference_id", "expiration_date"]

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        # Add decrypted data
        # representation["title"] = fernet_decrypt(instance.title.encode())

        return representation


class OrganizationDocumentSerializer(serializers.ModelSerializer):
    user_document = UserDocumentListCountSerializer(read_only=True)
    # doc_reference_id = serializers.CharField(source='user_document.reference_id')
    thumbnail_path = serializers.SerializerMethodField()
    thumbnail_file = serializers.SerializerMethodField()
    owner = serializers.SerializerMethodField()
    created_by = serializers.SerializerMethodField()

    class Meta:
        model = OrganizationDocument
        fields = [
            "encrypted_reference_id",
            "user_document",
            "thumbnail_path",
            "thumbnail_file",
            "owner",
            "created_by",
        ]

    def get_thumbnail_path(self, obj):
        return obj.user_document.thumbnail_path if obj.user_document else None

    def get_thumbnail_file(self, obj):
        if obj.user_document and obj.user_document.thumbnail_file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.user_document.thumbnail_file.url)
        return None

    def get_owner(self, obj):
        user_doc = obj.user_document
        if user_doc and user_doc.owner:
            return {
                "id": user_doc.owner.id,
                "name": user_doc.owner.get_full_name(),
                "email": user_doc.owner.get_decrypted_email() if hasattr(user_doc.owner, 'get_decrypted_email') else user_doc.owner.email,
            }
        return None

    def get_created_by(self, obj):
        user_doc = obj.user_document
        if user_doc and user_doc.created_by:
            return {
                "id": user_doc.created_by.id,
                "name": user_doc.created_by.get_full_name(),
                "email": user_doc.created_by.get_decrypted_email() if hasattr(user_doc.created_by, 'get_decrypted_email') else user_doc.created_by.email,
            }
        return None


class FlattenedDocumentSerializer(serializers.ModelSerializer):
    document_details = serializers.SerializerMethodField()
    signature = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    actions = serializers.SerializerMethodField()
    document_refrence_id = serializers.SerializerMethodField()
    organisation_documenet_reference_id = serializers.SerializerMethodField()
    thumbnail_path = serializers.SerializerMethodField()
    thumbnail_file = serializers.SerializerMethodField()
    expiry_date = serializers.SerializerMethodField()
    created_by = serializers.SerializerMethodField()
    agreement_start_date = serializers.SerializerMethodField()
    agreement_end_date = serializers.SerializerMethodField()

    # INSERT_YOUR_CODE

    def get_agreement_start_date(self, obj):
        agreement_start_date = None
        user_doc = getattr(obj, 'user_document', None)
        if user_doc and hasattr(user_doc, 'agreement_start_date'):
            agreement_start_date = user_doc.agreement_start_date
        return {"agreement_start_date": agreement_start_date}

    def get_agreement_end_date(self, obj):
        agreement_end_date = None
        user_doc = getattr(obj, 'user_document', None)
        if user_doc and hasattr(user_doc, 'agreement_end_date'):
            agreement_end_date = user_doc.agreement_end_date
        return {"agreement_end_date": agreement_end_date}

    def get_created_by(self, obj):
        user_doc = obj.user_document
        if user_doc and user_doc.created_by:
            created_by_user = user_doc.created_by
            return {
                "id": created_by_user.id,
                "email": created_by_user.get_decrypted_email() if hasattr(created_by_user, 'get_decrypted_email') else created_by_user.email,
                "full_name": created_by_user.get_full_name() if hasattr(created_by_user, "get_full_name") else "None",
            }
        return None

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # Ensure created_by is included (it will be, but this is explicit)
        representation['created_by'] = self.get_created_by(instance)
        return representation

    def get_expiry_date(self, obj):
        return obj.user_document.expiration_date if obj.user_document.expiration_date else None

    def get_expiry_date(self, obj):
        return obj.user_document.expiration_date if obj.user_document.expiration_date else None

    def get_org_doc_reference_id(self, obj):
        organisation_doc_reference_id = obj.encrypted_reference_id
        return organisation_doc_reference_id

    def get_document_details(self, obj):
        user_doc = obj.user_document
        created_at = user_doc.created_at.strftime("%d %b %Y")  # Format date
        return {
            "title": (user_doc.title if user_doc.title else ""),  # Decrypt title
            "created_at": created_at,
            # Add other document details as needed
        }

    def get_signature(self, obj):
        try:
            request = self.context.get("request")
            signers = obj.user_document.signers.all()

            return {
                "signers": [
                    {
                        "signer_id": signer.reference_signer_id,
                        "name": fernet_decrypt(signer.name.encode())
                        if signer.name
                        else "",
                        "email": fernet_decrypt(signer.email.encode())
                        if signer.email
                        else "",
                        "phone_number": (
                            fernet_decrypt(signer.phone_number.encode())
                            if signer.phone_number
                            else ""
                        ),
                        "signed": signer.status
                        == "signed",  # Clearly indicates signed status
                        "signer_type": signer.signer_type,
                        # Add other signer fields as needed
                        "signature_image": signer.signature_image
                        if signer.signature_image
                        else "",
                        "meta_data": signer.meta_data if signer.meta_data else None,
                        "is_name_editable": signer.is_name_editable,
                        "hide_security_number": signer.hide_security_number,
                        "bank_id_meta_data": signer.bank_id_meta_data if signer.bank_id_meta_data else None,
                    }
                    # ... other signer fields ...
                    for signer in signers
                ]
            }
        except Exception as e:
            logger.error(f"Error getting signer data: {e}")
            return {"signers": []}

    def get_signed_by(self, obj):
        signers = obj.user_document.signers.all()
        signed_signers = [
            fernet_decrypt(signer.name.encode()) if signer.name else ""
            for signer in signers
            if signer.status == "signed"
        ]
        return signed_signers

    def get_status(self, obj):
        user_doc = obj.user_document
        signers = user_doc.signers.all()
        status_label = None
        if signers:
            all_signed = all(signer.status == "signed" for signer in signers)

            if all_signed:
                status_label = "Signed by all Parties"
            elif signers.exists():  # Check if there are any signers at all
                status_label = "Waiting for Signature"
            else:
                status_label = None  # Fallback to document status if no signers
        # Add latest event   details if needed (see below)

        return {
            "label": status_label,
            "latest_event": (
                user_doc.updated_at.isoformat() if user_doc.updated_at else None
            ),
        }

    def get_actions(self, obj):
        # Add actions as needed based on status etc.
        return {
            "view": True,  # Example action
            "delete": True,  # Example action
        }

    def get_document_refrence_id(self, obj):
        user_doc = obj.user_document
        return {
            "document_refrence_id": user_doc.reference_id,
        }

    def get_thumbnail_path(self, obj):
        return obj.user_document.thumbnail_path if hasattr(obj, 'user_document') else None

    def get_thumbnail_file(self, obj):
        if hasattr(obj, 'user_document') and obj.user_document.thumbnail_file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.user_document.thumbnail_file.url)
        return None

    def to_representation(self, instance):
        request = self.context.get("request")
        org_superadmin = self.context.get('org_superadmin')
        user_role = self.context.get('user_role')
        data = {
            **self.get_document_details(instance),
            **self.get_signature(instance),
            **self.get_status(instance),
            "signed_by": self.get_signed_by(instance),
            **self.get_document_refrence_id(instance),
            "organisation_doc_reference_id": self.get_org_doc_reference_id(instance),
            "thumbnail_path": self.get_thumbnail_path(instance),
            "thumbnail_file": self.get_thumbnail_file(instance),
            "expiry_date": self.get_expiry_date(instance),
            "owner": {
                "id": org_superadmin.id if org_superadmin else None,
                "email": org_superadmin.get_decrypted_email() if org_superadmin else None,
            },
            "created_by": self.get_created_by(instance),
            "user_role": user_role,
            **self.get_agreement_start_date(instance),
            **self.get_agreement_end_date(instance),
        }
        # Apply filters if they exist in request
        # if request and request.query_params:
        #     should_include = True  # Default to including the document

        #     # General search query
        #     search_query = request.query_params.get("search", "").lower().strip()
        #     if search_query:
        #         # Get the document title and ensure it's a string
        #         title = (instance.user_document.title or "").lower()

        #         # Check if search query is in the title (exact match or contains match)
        #         title_match = search_query in title

        #         # Search in signer details
        #         signer_match = False
        #         signers = instance.user_document.signers.all()
        #         if signers:
        #             for signer in signers:
        #                 # Collect the fields to search (name, email, phone_number)
        #                 signer_text = " ".join([
        #                     (signer.name or "").lower(),
        #                     (signer.email or "").lower(),
        #                     (signer.phone_number or "").lower()
        #                 ])
        #                 if search_query in signer_text:
        #                     signer_match = True
        #                     break

        #         # Search in status
        #         status_label = data.get("status", {}).get("label", "").lower() if data.get("status") else ""
        #         status_match = search_query in status_label

        #         # Update should_include based on search matches
        #         should_include = title_match or signer_match or status_match

        #     # Date filtering
        #     start_date = request.query_params.get("start_date")
        #     end_date = request.query_params.get("end_date")
        #     if should_include and (start_date or end_date):
        #         try:
        #             doc_date = datetime.strptime(data.get("created_at", ""), "%d %b %Y")
        #             if start_date:
        #                 start = datetime.strptime(start_date, "%Y-%m-%d")
        #                 if doc_date < start:
        #                     should_include = False
        #             if end_date:
        #                 end = datetime.strptime(end_date, "%Y-%m-%d")
        #                 if doc_date > end:
        #                     should_include = False
        #         except ValueError as e:
        #             logger.warning(f"Invalid date format for document {instance.id}: {str(e)}")
        #             should_include = False

        #     # If document shouldn't be included, raise SkipField
        #     if not should_include:
        #         raise serializers.SkipField()

        return data

    class Meta:
        model = OrganizationDocument
        fields = '__all__'


class UserDocumentSerializer(serializers.ModelSerializer):
    document_signers = DocumentSignerSerializer(many=True, read_only=True)
    # user = serializers.CharField(required=False)
    document_file = serializers.FileField(required=False)
    latest_document_file = serializers.FileField(required=False)
    document_password = serializers.CharField(required=False)
    organization_document_reference_id = serializers.SerializerMethodField()
    user_reference_id = serializers.SerializerMethodField()  # For output
    created_by = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(), write_only=True, required=False
    )  # For input only
    thumbnail_path = serializers.CharField(read_only=True)
    thumbnail_file = serializers.FileField(read_only=True)
    owner = serializers.PrimaryKeyRelatedField(read_only=True)
    updated_by = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(), required=False, allow_null=True)

    def validate_title(self, value):
        """
        Ensure the title is unique within the organization.
        """
        organization = self.context.get('organization')
        if not organization:
            raise serializers.ValidationError("Organization context is required.")

        # Exclude current instance when updating
        queryset = self.Meta.model.objects.filter(
            owner=organization.get_organization_owner(), title=value)

        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)

        if queryset.exists():
            raise serializers.ValidationError(
                "A document with this title already exists in the organization.")
        return value

    class Meta:
        model = UserDocument
        fields = (
            "id",
            "reference_id",
            "owner",
            "created_by",
            "updated_by",
            "title",
            "status",
            "document_file",
            # "download_pdf",
            "transaction_id",
            "latest_document_file",
            "user_reference_id",
            "created_at",
            "updated_at",
            "document_signers",
            "document_password",
            "organization_document_reference_id",
            "thumbnail_path",
            "thumbnail_file",
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if hasattr(instance, "user_document") and instance.user_document.title:
            representation["title"] = instance.user_document.title

        if hasattr(instance, "user_document") and instance.user_document:
            representation["reference_id"] = instance.user_document.reference_id

        if hasattr(instance, "user_document") and instance.user_document.status:
            representation["status"] = instance.user_document.status
            # Decrypt sensitive fields for output

        if hasattr(instance, "status") and instance.status:
            representation["status"] = instance.status

        if hasattr(instance, "reference_id") and instance.reference_id:
            representation["reference_id"] = instance.reference_id
        # # Decrypt sensitive fields for output
        # if hasattr(instance, "tile") and instance.title:
        #     representation["title"] = fernet_decrypt(instance.title.encode())
        # if instance.user:
        #     representation["user"] = fernet_decrypt(instance.user.encode())
        if hasattr(instance, "ip_address") and instance.ip_address:
            representation["ip_address"] = fernet_decrypt(instance.ip_address.encode())
        if hasattr(instance, "location") and instance.location:
            representation["location"] = fernet_decrypt(instance.location.encode())
        if hasattr(instance, "access_log") and instance.access_log:
            representation["access_log"] = fernet_decrypt(instance.access_log.encode())

        if hasattr(instance, "document_file") and instance.document_file:
            representation["document_file"] = (
                instance.document_file.url if instance.document_file else None
            )
        logger.info(f"Decrypted fields for UserDocument ID {instance.id}")
        # Add owner, created_by, updated_by to output
        representation["owner"] = instance.owner.id if instance.owner else None
        representation["created_by"] = instance.created_by.id if instance.created_by else None
        representation["updated_by"] = instance.updated_by.id if hasattr(
            instance, 'updated_by') and instance.updated_by else None
        return representation

    def get_organization_document_reference_id(self, instance):
        # Fetch related OrganizationDocument's id
        if (
            hasattr(instance, "encrypted_reference_id")
            and instance.encrypted_reference_id
        ):
            return instance.encrypted_reference_id
        return None

    def get_user_reference_id(self, instance):
        """Return the user's reference ID."""
        if instance.owner and hasattr(instance.owner, "reference_id"):
            return instance.owner.reference_id
        return None

    def get_created_by_reference_id(self, instance):
        return getattr(instance.created_by, "reference_id", None)

    def create(self, validated_data):
        try:
            request = self.context.get('request', None)
            user = request.user if request and hasattr(request, 'user') else None
            # Get organization from validated_data or context
            organization = validated_data.get(
                'organization') or self.context.get('organization')
            if not organization:
                raise serializers.ValidationError(
                    'Organization must be provided to set owner (org_superadmin).')
            org_superadmin = organization.get_organization_owner()
            if not org_superadmin:
                raise serializers.ValidationError(
                    'No org_superadmin found for the organization.')

            # Add a user-friendly timestamp to the title if present
            # if "title" in validated_data and validated_data["title"]:
            #     title = validated_data["title"]
            #     title_timestamp = int(datetime.now().timestamp())
            #     validated_data["title"] = f"{title} {title_timestamp}"

            validated_data['owner'] = org_superadmin
            # Remove 'user' if present
            validated_data.pop('user', None)
            # Set created_by if not provided
            if 'created_by' not in validated_data or validated_data['created_by'] is None:
                validated_data['created_by'] = user
            validated_data = self._encrypt_fields(validated_data)
            document = super().create(validated_data)
            logger.info(
                "Document created through serializer",
                extra={
                    "document_id": str(document.id),
                    "title": document.title,
                    "user_id": str(document.owner.id) if document.owner else None,
                },
            )
            return document
        except Exception as e:
            logger.error(
                "Error creating document through serializer",
                extra={"error": str(e), "data": validated_data},
                exc_info=True,
            )
            raise

    def update(self, instance, validated_data):
        try:
            request = self.context.get('request', None)
            user = request.user if request and hasattr(request, 'user') else None
            if 'owner' in validated_data:
                validated_data.pop('owner')  # Prevent changing owner
            if user:
                validated_data['updated_by'] = user
            print(f"Updating the Serializer")
            validated_data = self._encrypt_fields(validated_data)
            document = super().update(instance, validated_data)
            logger.info(
                "Document updated through serializer",
                extra={
                    "document_id": str(document.id),
                    "updated_fields": list(validated_data.keys()),
                },
            )
            return document
        except Exception as e:
            logger.error(
                "Error updating document through serializer",
                extra={"document_id": str(instance.id), "error": str(e)},
                exc_info=True,
            )
            raise

    def _encrypt_fields(self, validated_data):
        if "document_file" in validated_data and validated_data["document_file"]:
            file_name = str(validated_data["document_file"].name)
            print(f"\n\n _encrypt_fields | file_name :  {file_name}")
            new_file_name = get_file_extension(file_name)
            print(f"\n\n _encrypt_fields | new_file_name :  {new_file_name}")
            file_data = validated_data["document_file"].read()
            if file_data:
                print(f"\n\n _encrypt_fields | file_data ")
            else:
                print(f"\n\n _encrypt_fields | no file_data ")
            encrypted_data = cipher_suite.encrypt(file_data)
            if encrypted_data:
                print(f"\n\n _encrypt_fields | encrypted_data")
            else:
                print(f"\n\n _encrypt_fields | no encrypted_data")
            validated_data["document_file"] = ContentFile(encrypted_data, new_file_name)
            print(f"\n\n _encrypt_fields | document_file")
            logger.info("Encrypted document file for UserDocument")
            validated_data['file_size'] = validated_data["document_file"].size

        if (
            "latest_document_file" in validated_data
            and validated_data["latest_document_file"]
        ):
            file_name = str(validated_data["latest_document_file"].name)
            new_file_name = get_file_extension(file_name)
            file_data = validated_data["latest_document_file"].read()
            encrypted_data = cipher_suite.encrypt(file_data)
            validated_data["latest_document_file"] = ContentFile(
                encrypted_data, new_file_name
            )
            logger.info("Encrypted document file for UserDocument")
            validated_data['file_size'] = validated_data["latest_document_file"].size
        return validated_data


class ActivityLogSerializer(serializers.ModelSerializer):
    user = serializers.CharField(read_only=True)
    email = serializers.CharField(required=False, allow_blank=True)
    phone_number = serializers.CharField(required=False, allow_blank=True)
    ip_address = serializers.CharField(read_only=True)
    location = serializers.CharField(read_only=True)

    class Meta:
        model = ActivityLog
        fields = [
            "event_type",
            "email",
            "phone_number",
            "category",
            "message",
            "signer_type",
            "user",
            "ip_address",
            "device_data",
            "location",
            "timestamp",
            "created_at",
            "updated_at",
        ]

    def to_representation(self, instance):
        """
        Decrypt sensitive fields before returning the data
        """
        # Use the model's method to decrypt sensitive fields
        data = super().to_representation(instance)
        data["email"] = (
            fernet_decrypt(instance.email.encode()) if instance.email else None
        )
        data["title"] = instance.title if instance.title else None
        data["phone_number"] = (
            fernet_decrypt(instance.phone_number.encode())
            if instance.phone_number
            else None
        )
        # data['user'] = fernet_decrypt(instance.user.encode())
        # if instance.user else None
        # data["ip_address"] = (
        #     fernet_decrypt(instance.ip_address.encode())
        #     if instance.ip_address
        #     else None
        # )
        data["location"] = (
            fernet_decrypt(instance.location.encode()) if instance.location else None
        )

        data["device_data"] = instance.device_data

        timestamp = localtime(instance.timestamp)
        date_str = timestamp.strftime("%Y-%m-%d")  # Format the timestamp as YYYY-MM-DD
        # time_str = timestamp.strftime("%H:%M:%S")  # Format the timestamp as HH:MM:SS
        # data["timestamp"] = f"{date_str} {time_str}"
        data["timestamp"] = instance.timestamp
        # Return the structured data
        return {
            date_str: {
                # "time": time_str,
                "time": data["timestamp"],
                "message": data["message"],
                "email": data["email"],
                "phone": data["phone_number"],
                "title": data["title"],
                "device_data": data["device_data"],
                "event_type": data["event_type"],
                "category": data["category"],
                "ip_address": data["ip_address"],
                "signer_type": data["signer_type"],
                "location": data["location"],
            }
        }


class DocumentPreviewSerializer(serializers.ModelSerializer):
    reference_signer_id = serializers.CharField()
    reference_id = serializers.CharField()
    status = serializers.CharField()
    document_preview = serializers.CharField()
    thumbnail_path = serializers.CharField(read_only=True)
    thumbnail_file = serializers.SerializerMethodField()

    class Meta:
        model = UserDocument
        fields = [
            "reference_signer_id",
            "reference_id",
            "status",
            "document_preview",
            "thumbnail_path",
            "thumbnail_file"
        ]

    def get_thumbnail_file(self, obj):
        if obj.thumbnail_file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.thumbnail_file.url)
        return None


class DocumentPreiviewViewSerializer(serializers.Serializer):
    document_details = serializers.SerializerMethodField()
    signatures = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    actions = serializers.SerializerMethodField()
    document_reference_id = serializers.SerializerMethodField()
    organisation_document_reference_id = serializers.SerializerMethodField()
    thumbnail_path = serializers.SerializerMethodField()
    thumbnail_file = serializers.SerializerMethodField()

    def get_organisation_document_reference_id(self, obj):
        return {"organisation_document_reference_id": obj.encrypted_reference_id}

    def get_transaction_id(self, obj):
        """Generate and set transaction_id if all signers have signed."""
        # and not self.transaction_id
        user_doc = obj.user_document
        # signers = user_doc.signers.all()
        # if signers:
        #     all_signed = all(signer.status == "signed" for signer in signers)
        #     if all_signed:
        # user_doc.transaction_id = uuid.uuid4()
        # user_doc.save()
        return user_doc.transaction_id
        # return None

    def get_document_details(self, obj):
        user_doc = obj.user_document
        # created_at = user_doc.created_at.strftime("%d %b %Y")
        created_at = user_doc.created_at
        return {
            "title": user_doc.title if user_doc.title else "",
            "created_at": created_at,
            "is_download_and_printable": user_doc.download_pdf,
            # Add other document details as needed
        }

    def get_signed_by(self, obj):
        signers = obj.user_document.signers.all()
        signed_signers = [
            fernet_decrypt(signer.name.encode()) if signer.name else ""
            for signer in signers
            if signer.status == "signed"
        ]
        return signed_signers

    def get_signatures(self, obj):  # Improved signer information
        request = self.context.get("request")
        signers = obj.user_document.signers.all()
        return [
            {
                "signer_id": signer.reference_signer_id,
                "name": fernet_decrypt(signer.name.encode()) if signer.name else "",
                "email": fernet_decrypt(signer.email.encode()) if signer.email else "",
                "phone_number": (
                    fernet_decrypt(signer.phone_number.encode())
                    if signer.phone_number
                    else ""
                ),
                "signed": signer.status == "signed",  # Clearly indicates signed status
                "signer_type": signer.signer_type,
                # Add other signer fields as needed
                "signature_image": signer.signature_image
                if signer.signature_image
                else "",
                "meta_data": signer.meta_data if signer.meta_data else None,
                "is_name_editable": signer.is_name_editable
                if signer.is_name_editable
                else None,
                "hide_security_number": signer.hide_security_number,
                "e_signer_meta_data": (
                    {
                        "signer_date": signer.updated_at if signer.updated_at else None,
                        "ip_address": signer.ip_address if signer.ip_address else None,
                    }
                    if signer.signer_type != "sweden_bank_id" and signer.status == "signed"
                    else None
                ),
                "bank_id_meta_data": signer.bank_id_meta_data if (signer.bank_id_meta_data and signer.signer_type == "sweden_bank_id") else None,
                "signed_date": signer.updated_at if signer.updated_at else None,
            }
            for signer in signers
        ]

    def get_document_reference_id(self, obj):
        user_doc = obj.user_document
        return {"document_reference_id": user_doc.reference_id}

    def get_thumbnail_path(self, obj):
        return obj.user_document.thumbnail_path if hasattr(obj, 'user_document') else None

    def get_thumbnail_file(self, obj):
        if hasattr(obj, 'user_document') and obj.user_document.thumbnail_file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.user_document.thumbnail_file.url)
        return None

    # def to_representation(self, instance):
    #     request = self.context.get("request")
    #     data = {
    #         **self.get_document_details(instance),
    #         **self.get_signatures(instance),
    #         **self.get_signed_by(instance),
    #
    #         **self.get_document_reference_id(instance),
    #         **self.get_organisation_document_reference_id(instance),
    #         "thumbnail_path": self.get_thumbnail_path(instance),
    #         "thumbnail_file": self.get_thumbnail_file(instance),
    #     }
    #     return data
    def to_representation(self, instance):
        request = self.context.get("request")
        org_superadmin = self.context.get('org_superadmin')
        user_role = self.context.get('user_role')
        data = {**self.get_document_details(instance)}

        signatures = self.get_signatures(instance)
        if isinstance(signatures, list):
            data["signatures"] = signatures
        else:
            data.update(signatures)

        signed_by = self.get_signed_by(instance)
        if isinstance(signed_by, list):
            data["signed_by"] = signed_by
        else:
            data.update(signed_by)

        data.update(self.get_document_reference_id(instance))
        data.update(self.get_organisation_document_reference_id(instance))

        data["thumbnail_path"] = self.get_thumbnail_path(instance)
        data["thumbnail_file"] = self.get_thumbnail_file(instance)
        try:
            data["transaction_id"] = self.get_transaction_id(instance)
        except Exception as e:
            data["transaction_id"] = None  # or handle the error appropriately
            # Optionally log the error for debugging

        data["owner"] = {
            "id": org_superadmin.id if org_superadmin else None,
            "email": org_superadmin.get_decrypted_email() if org_superadmin else None,
        }
        data["user_role"] = user_role

        return data


class DocumentSignerPreviewSerializer(serializers.Serializer):
    signature = serializers.SerializerMethodField()

    def get_signature(self, obj):
        try:
            request = self.context.get("request")
            # Get all signers for the user document
            signers = obj.user_document.signers.all()

            # Return a flat list of signers
            return [
                {
                    "signer_id": signer.reference_signer_id,
                    "name": fernet_decrypt(signer.name.encode()) if signer.name else "",
                    "email": fernet_decrypt(signer.email.encode())
                    if signer.email
                    else "",
                    "phone_number": (
                        fernet_decrypt(signer.phone_number.encode())
                        if signer.phone_number
                        else ""
                    ),
                    "signed": signer.status
                    == "signed",  # Clearly indicates signed status
                    "signer_type": signer.signer_type,
                    "signature_image": signer.signature_image
                    if signer.signature_image
                    else "",
                    "meta_data": signer.meta_data if signer.meta_data else None,
                    "e_signer_meta_data": (
                        {
                            "signer_date": signer.updated_at if signer.updated_at else None,
                            "ip_address": signer.ip_address if signer.ip_address else None,
                        }
                        if signer.signer_type != "sweden_bank_id" and signer.status == "signed"
                        else None
                    ),
                    "is_name_editable": signer.is_name_editable,
                    "hide_security_number": signer.hide_security_number,
                    "bank_id_meta_data": signer.bank_id_meta_data if (signer.bank_id_meta_data and signer.signer_type == "sweden_bank_id") else None,
                }
                for signer in signers
            ]
        except Exception as e:
            logger.error(f"Error getting signer data: {e}")
            return []  # Return an empty list in case of error

    def to_representation(self, instance):
        # Directly return the list of signers instead of wrapping it in another structure
        return {"signers": self.get_signature(instance)}
