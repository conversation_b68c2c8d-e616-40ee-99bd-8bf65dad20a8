from celery.worker.control import election
from rest_framework import serializers
from documents.models import UserDocumentDirectory, DirectoryDocument, OrganizationDocument
from documents.models.document_models import DocumentAccessRequest, UserDocument
from documents.serializers.document_serializers import OrganizationDocumentSerializer
import logging

logger = logging.getLogger('app')


class DirectoryDocumentSerializer(serializers.ModelSerializer):
    document = OrganizationDocumentSerializer()
    has_access = serializers.SerializerMethodField()
    access_request_status = serializers.SerializerMethodField()

    class Meta:
        model = DirectoryDocument
        fields = ['id', 'document', 'added_at', 'has_access', 'access_request_status']

    def get_has_access(self, obj):
        user = self.context.get('user')
        user_role = self.context.get('user_role')

        if not user:
            return False

        # If document is global, everyone has access
        if obj.document.is_global:
            return True

        # Super admin and org admin have access to all documents
        if user_role in ['org_superadmin', 'org_admin']:
            return True

        # # Document creator always has access
        if (obj.document.user_document.owner == user) or (obj.document.user_document.created_by == user):
            return True

        # Org members need to check permissions
        if user_role == 'org_member':
            if obj.document.user_document.created_by != user:
                access_request = DocumentAccessRequest.objects.filter(
                    document=obj.document.user_document,
                    requester__reference_id=self.context['user_reference_id']
                ).order_by('-created_at').first()
                if access_request:
                    return True if access_request.status == "approved" else False

        return False

    def get_access_request_status(self, obj):
        try:
            # First get the UserDocument associated with this OrganizationDocument
            user_document = UserDocument.objects.filter(
                organization_document=obj,
                user_reference_id=self.context['user_reference_id']
            ).first()

            if not user_document:
                return None

            # Now query DocumentAccessRequest with the UserDocument
            access_request = DocumentAccessRequest.objects.filter(
                document=user_document,
                requester__reference_id=self.context['user_reference_id']
            ).order_by('-created_at').first()

            return access_request.status if access_request else None
        except Exception as e:
            logger.error(
                "Error getting access request status",
                extra={
                    'document_id': str(obj.id),
                    'error': str(e)
                },
                exc_info=True
            )
            return None


class UserDocumentDirectorySerializer(serializers.ModelSerializer):
    documents = serializers.SerializerMethodField()
    document_count = serializers.SerializerMethodField()

    class Meta:
        model = UserDocumentDirectory
        fields = (
            'id',
            'title',
            'is_active',
            'is_default',
            'organization',
            'created_by',
            'updated_by',
            'created_at',
            'updated_at',
            'documents',
            'document_count'
        )
        read_only_fields = (
            'created_at',
            'updated_at',
            'is_default',
            'organization',
            'created_by',
            'updated_by'
        )

    def get_documents(self, obj):
        active_documents = DirectoryDocument.objects.filter(
            directory=obj,
            is_active=True
        ).select_related('document')
        return DirectoryDocumentSerializer(active_documents, many=True).data

    def get_document_count(self, obj):
        return DirectoryDocument.objects.filter(
            directory=obj,
            is_active=True
        ).count()

    def validate_title(self, value):
        if not value:
            raise serializers.ValidationError("Title cannot be empty")

        # Get organization from context
        user = self.context.get('user')
        organization = user.organizations.first()
        if not organization:
            raise serializers.ValidationError(
                "User does not belong to any organization")

        # Check for duplicate titles in the same organization
        if UserDocumentDirectory.objects.filter(
            organization=organization,
            title__iexact=value,
            is_deleted=False
        ).exclude(id=self.instance.id if self.instance else None).exists():
            raise serializers.ValidationError(
                "A directory with this title already exists")

        return value

    def create(self, validated_data):
        try:
            user = self.context.get('user')
            validated_data['created_by'] = user
            validated_data['updated_by'] = user
            # Get the first organization of the user
            organization = user.organizations.first()
            if not organization:
                raise serializers.ValidationError(
                    "User does not belong to any organization")
            validated_data['organization'] = organization

            return super().create(validated_data)
        except Exception as e:
            logger.error(
                "Error creating directory through serializer",
                extra={
                    'error': str(e),
                    'data': validated_data,
                    'user_reference_id': self.context.get('user_reference_id')
                },
                exc_info=True
            )
            raise

    def update(self, instance, validated_data):
        try:
            user = self.context.get('user')
            validated_data['updated_by'] = user

            if instance.is_default and not validated_data.get('is_active', True):
                raise serializers.ValidationError(
                    "Cannot archive default 'Agreements' directory"
                )

            return super().update(instance, validated_data)
        except Exception as e:
            logger.error(
                "Error updating directory through serializer",
                extra={
                    'directory_id': str(instance.id),
                    'error': str(e),
                    'user_reference_id': self.context.get('user_reference_id')
                },
                exc_info=True
            )
            raise


class DirectoryDocumentMoveSerializer(serializers.Serializer):
    target_directory = serializers.UUIDField()

    def validate_target_directory(self, value):
        try:
            directory = UserDocumentDirectory.objects.get(
                id=value,
                is_active=True
            )
            # Get user's organization
            user = self.context.get('user')
            user_organization = user.organizations.first()
            if not directory.organization == user_organization:
                raise serializers.ValidationError(
                    "Cannot move document to directory from different organization"
                )
            return directory
        except UserDocumentDirectory.DoesNotExist:
            raise serializers.ValidationError("Target directory not found")
