from accounts.models.organisation import Organization
from django.db.models.signals import post_save
from django.dispatch import receiver
from documents.models.document_models import (
    DocumentSigner,
    OrganisationContacts,
    OrganizationDocument,
)


@receiver(post_save, sender=DocumentSigner)
def add_to_organisation_contacts(sender, instance, created, **kwargs):
    if created:
        org_document = OrganizationDocument.objects.get(user_document=instance.document)

        existing_contacts = OrganisationContacts.objects.filter(
            organisation=org_document.organization,
            email=instance.get_decrypted_email(),
            phone_number=instance.get_decrypted_phone_number(),
            name=instance.get_decrypted_updated_name()
            if instance.updated_name
            else instance.get_decrypted_name(),
            bank_id=instance.bank_id_reference,
        )

        if not existing_contacts.exists():
            # If no matching contact exists, create a new one
            OrganisationContacts.objects.create(
                organisation=org_document.organization,
                email=instance.get_decrypted_email(),
                phone_number=instance.get_decrypted_phone_number(),
                name=instance.get_decrypted_updated_name()
                if instance.updated_name
                else instance.get_decrypted_name(),
                bank_id=instance.bank_id_reference,
            )
        else:
            # Handle the case where one or more records exist
            # For example, choose the first matching contact or log an issue
            contact = existing_contacts.first()
            # You can update the contact or handle the duplicate case as needed
