/*
Description: Foundation 4 docs style for highlight.js
Author: <PERSON> <<EMAIL>>
Website: http://foundation.zurb.com/docs/
Version: 1.0
Date: 2013-04-02
*/

.hljs {
  display: block; padding: 0.5em;
  background: #eee;
}

.hljs-header,
.hljs-decorator,
.hljs-annotation {
  color: #000077;
}

.hljs-horizontal_rule,
.hljs-link_url,
.hljs-emphasis,
.hljs-attribute {
  color: #070;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-link_label,
.hljs-strong,
.hljs-value,
.hljs-string,
.scss .hljs-value .hljs-string {
  color: #d14;
}

.hljs-strong {
  font-weight: bold;
}

.hljs-blockquote,
.hljs-comment {
  color: #998;
  font-style: italic;
}

.asciidoc .hljs-title,
.hljs-function .hljs-title {
  color: #900;
}

.hljs-class {
  color: #458;
}

.hljs-id,
.hljs-pseudo,
.hljs-constant,
.hljs-hexcolor {
  color: teal;
}

.hljs-variable {
  color: #336699;
}

.hljs-bullet,
.hljs-javadoc {
  color: #997700;
}

.hljs-pi,
.hljs-doctype {
  color: #3344bb;
}

.hljs-code,
.hljs-number {
  color: #099;
}

.hljs-important {
  color: #f00;
}

.smartquote,
.hljs-label {
  color: #970;
}

.hljs-preprocessor,
.hljs-pragma {
  color: #579;
}

.hljs-reserved,
.hljs-keyword,
.scss .hljs-value {
  color: #000;
}

.hljs-regexp {
  background-color: #fff0ff;
  color: #880088;
}

.hljs-symbol {
  color: #990073;
}

.hljs-symbol .hljs-string {
  color: #a60;
}

.hljs-tag {
  color: #007700;
}

.hljs-at_rule,
.hljs-at_rule .hljs-keyword {
  color: #088;
}

.hljs-at_rule .hljs-preprocessor {
  color: #808;
}

.scss .hljs-tag,
.scss .hljs-attribute {
  color: #339;
}
