from __future__ import absolute_import, unicode_literals

import os
from celery import Celery
from celery.schedules import crontab

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "esign.settings")

app = Celery("esign")

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object("django.conf:settings", namespace="CELERY")

# Load task modules from all registered Django app configs.
app.autodiscover_tasks()

# Configure the Celery beat schedule
app.conf.beat_schedule = {
    'check-expired-subscriptions-daily': {
        'task': 'check_expired_subscriptions_and_create_invoices',
        'schedule': crontab(hour=0, minute=0),  # Run daily at midnight
        'options': {
            'expires': 3600,  # Task expires after 1 hour
        }
    },
}

# Optional: Configure timezone for scheduled tasks
app.conf.timezone = 'UTC'
