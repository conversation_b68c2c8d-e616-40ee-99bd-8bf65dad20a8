import os

from channels.routing import ProtocolType<PERSON>outer, URLRouter
from django.core.asgi import get_asgi_application
from django.urls import path

# import django
from realtime.consumers.collect_status_consumer import CollectStatusConsumer  # noqa
from realtime.consumers.collect_status_one_consumer import CollectOneStatusConsumer
from realtime.consumers.notification_consumer import NotificationConsumer
from realtime.consumers.qrcode_consumer import QRCodeConsumer  # noqa
from realtime.middleware import TokenAuthMiddleWare

# Set the default Django settings module
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "esign.settings")

# Define the application
application = ProtocolTypeRouter(
    {
        "http": get_asgi_application(),
        "websocket": URLRouter(
            [
                path("ws/qr/<str:order_ref>/", QRCodeConsumer.as_asgi()),
                path(
                    "ws/collect-status/<str:language>/<str:order_ref>/",
                    CollectStatusConsumer.as_asgi(),
                ),
                path(
                    "ws/collect-status-once/<str:language>/<str:order_ref>/",
                    CollectOneStatusConsumer.as_asgi(),
                ),
                path(
                    "ws/notifications/<str:user_reference_id>/<str:language>/",
                    TokenAuthMiddleWare(NotificationConsumer.as_asgi()),
                ),
            ]
        ),
    }
)
