from django.utils.translation import gettext as _
from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response


class CustomPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = "page_size"
    page_query_param = "page"
    max_page_size = 500

    def get_paginated_response(
        self, data, status="success", message="Data retrieved successfully."
    ):
        return Response(
            {
                "data": data,
                "next": self.get_next_link(),
                "previous": self.get_previous_link(),
                "count": self.page.paginator.count,
                "status": status,
                "message": _(message),
                "status_code": 200,
            }
        )


if __name__ == "__main__":
    "Sample Usage"

    # Function-Based View
    """@api_view(['GET'])
    def my_list_view(request):

        queryset = MyModel.objects.all()  # Get all objects from the model
        paginator = CustomPagination()  # Create an instance of CustomPagination
        page = paginator.paginate_queryset(queryset, request)  # Paginate the queryset
        serializer = MyModelSerializer(page, many=True)  # Serialize the paginated data
        return paginator.get_paginated_response(serializer.data)
        # Return paginated response

    # Class-Based View
    class MyListView(generics.ListAPIView):

        queryset = MyModel.objects.all()  # Get all objects from the model
        serializer_class = MyModelSerializer  # Define the serializer
        pagination_class = CustomPagination  # Use CustomPagination for pagination

    # ViewSet
    class MyModelViewSet(viewsets.ModelViewSet):

        queryset = MyModel.objects.all()  # Get all objects from the model
        serializer_class = MyModelSerializer  # Define the serializer
        pagination_class = CustomPagination  # Use CustomPagination for pagination """
