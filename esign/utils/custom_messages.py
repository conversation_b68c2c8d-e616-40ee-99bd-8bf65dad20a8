from rest_framework import status


class MessagesList:
    # Define messages with success and error cases
    messages = {
        "data_created": {
            "message": "Data created successfully",
            "status_code": status.HTTP_201_CREATED,
        },
        "data_fetched": {
            "message": "Data fetched successfully",
            "status_code": status.HTTP_200_OK,
        },
        "data_not_created": {
            "message": "Failed to create data",
            "status_code": status.HTTP_400_BAD_REQUEST,
        },
        "data_updated": {
            "message": "Data updated successfully",
            "status_code": status.HTTP_200_OK,
        },
        "data_not_updated": {
            "message": "Failed to update data",
            "status_code": status.HTTP_400_BAD_REQUEST,
        },
        "data_deleted": {
            "message": "Data deleted successfully",
            "status_code": status.HTTP_200_OK,
        },
        "data_not_deleted": {
            "message": "Failed to delete data",
            "status_code": status.HTTP_400_BAD_REQUEST,
        },
        "data_retrieved": {
            "message": "Data retrieved successfully",
            "status_code": status.HTTP_200_OK,
        },
        "data_not_retrieved": {
            "message": "Data not found",
            "status_code": status.HTTP_404_NOT_FOUND,
        },
        "unauthorized_access": {
            "message": "Unauthorized access",
            "status_code": status.HTTP_401_UNAUTHORIZED,
        },
        "forbidden_access": {
            "message": "Forbidden access",
            "status_code": status.HTTP_403_FORBIDDEN,
        },
        "server_error": {
            "message": "Internal server error",
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
        },
        "validation_error": {
            "message": "Validation error",
            "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY,
        },
        "wrong_otp": {
            "message": "Invalid OTP",
            "status_code": status.HTTP_400_BAD_REQUEST,
        },
        "verification_failed": {
            "message": "Verification Failed",
            "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY,
        },
        "bankid_callback_success": {
            "message": "Verification Failed",
            "status_code": status.HTTP_200_OK,
        },
        "insufficient_tokens": {
            "message": "No BankID tokens remaining. Please contact the document owner to purchase more tokens.",
            "status_code": status.HTTP_400_BAD_REQUEST,
        },
        "feature_access_denied": {
            "message": "Feature access denied",
            "status_code": status.HTTP_403_FORBIDDEN,
        }
    }

    @classmethod
    def get_message(cls, action):
        """
        Get message and status code for a specific action.

        :param action: Action to get the message for.
        :return: Dictionary with message and status code.
        """
        return cls.messages.get(
            action,
            {"message": "Unknown action", "status_code": status.HTTP_400_BAD_REQUEST},
        )


if __name__ == "__main__":
    # Sample usage
    action = "data_not_retrieved"  # Replace with the action you want to test
    actions = [
        "data_created",
        "data_not_created",
        "data_updated",
        "data_not_updated",
        "data_deleted",
        "data_not_deleted",
        "data_retrieved",
        "data_not_retrieved",
        "unauthorized_access",
        "forbidden_access",
        "server_error",
        "validation_error",
        "wrong_otp"
    ]
    message_info = MessagesList.get_message(action)
    print(f"Action: {action}")
    print(f"Message: {message_info['message']}")
    print(f"Status Code: {message_info['status_code']}\n")
