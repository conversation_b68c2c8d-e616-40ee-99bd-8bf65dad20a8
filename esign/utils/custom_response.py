from django.utils.translation import gettext as _
from rest_framework.response import Response

from esign.utils.custom_messages import MessagesList


def api_response(action=None, data=None, status="failed", message=None):
    """
    Create a common response format for your API views using MessagesList for messages.

    :param action: Action to get the message for.
    :param data: Data to include in the response.
    :param status: status="success" in case of success, status="failed"
    in case of failure.
    :return: Response instance.
    """
    message_info = MessagesList.get_message(action)
    response_data = {
        "data": data,
        "status": status,
        "message": message if message else _(message_info["message"]),
        "status_code": message_info["status_code"],
    }
    return Response(response_data, status=response_data["status_code"])


if __name__ == "__main__":
    import os

    import django

    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "core.settings")
    django.setup()
    # Sample usage
    action = "data_not_retrieved"  # Replace with the action you want to test

    # Success response
    success_response = api_response(
        action="data_created", data={"key": "value"}, status="success"
    )

    # Failure response
    failure_response = api_response(
        action="data_not_created", data=None, status="failed"
    )

    # Print the response data
    print("Success Response:")
    print(success_response.data)  # Print response data for the success case
    print(
        "Status Code:", success_response.status_code
    )  # Print the status code for the success case

    print("\nFailure Response:")
    print(failure_response.data)  # Print response data for the failure case
    print("Status Code:", failure_response.status_code)  # Print the status code for t
