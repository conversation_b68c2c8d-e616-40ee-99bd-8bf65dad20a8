from typing import List

from django.conf import settings
from django.conf.urls.i18n import i18n_patterns
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import URLPattern, URLResolver, include, path, re_path
from django.views.static import serve
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework import permissions



from .swagger import urlpatterns as swagger_urls

schema_view = get_schema_view(
    openapi.Info(
        title="E-Sign API Documentation",
        default_version="v1",
        description="API documentation for E-Sign application",
        terms_of_service="https://www.yourapp.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="Your License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)


# Prefix for API versioning
v1_prefix = "api/v1/"

# Base URLs
urlpatterns: List[URLPattern | URLResolver] = [
    path("i18n/", include("django.conf.urls.i18n")),
    path("", schema_view.with_ui("swagger", cache_timeout=0), name="schema-swagger-ui"),
]

# Adding i18n patterns (for multi-language support with v1_prefix)
urlpatterns.extend(
    i18n_patterns(
        path("admin/", admin.site.urls),
        path(f"{v1_prefix}accounts/", include("accounts.urls")),
        path(f"{v1_prefix}user-documents/", include("documents.urls")),
        path(f"{v1_prefix}user-subscriptions/", include("subscriptions.urls")),
        path(f"{v1_prefix}admin-dashboard/", include("superadmin_dashboard.urls")),
        path(f"{v1_prefix}ai-documents/", include("ai_documents_app.urls")),
        path("ckeditor/", include("ckeditor_uploader.urls")),
        path(f"{v1_prefix}agreements/", include("agreements.urls")),

    )
)
# urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
# urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
# Serving static and media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
else:
    urlpatterns += [
        re_path(r"^media/(?P<path>.*)$", serve, {"document_root": settings.MEDIA_ROOT}),
        re_path(
            r"^static/(?P<path>.*)$", serve, {"document_root": settings.STATIC_ROOT}
        ),
    ]
