import os
from pathlib import Path
from urllib.parse import urlparse

from django.conf.global_settings import CSRF_TRUSTED_ORIGINS
# from dotenv import load_dotenv
from celery.schedules import crontab  # Add this import back

# load_dotenv()
from storages.backends.s3boto3 import S3Boto3Storage

BASE_DIR = Path(__file__).resolve().parent.parent


SECRET_KEY = os.getenv("SECRET_KEY")

DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "yes")

ALLOWED_HOSTS = ["*"]


# Application definition
PROJECT_APPS = [
    "accounts",
    "realtime",
    "documents",
    "superadmin_dashboard",
    "utils_app",
    "subscriptions",
    "ai_documents_app",
    "agreements",
]

THIRD_PARTY_APPS = [
    "rest_framework",
    "channels",
    "corsheaders",
    "celery",
    "django_celery_results",
    "django_filters",
    "drf_yasg",
    'ckeditor',
    'storages',
    'djmoney',
    
]
INSTALLED_APPS = (
    [
        "django.contrib.admin",
        "django.contrib.auth",
        "django.contrib.contenttypes",
        "django.contrib.sessions",
        "django.contrib.messages",
        "daphne",
        "django.contrib.staticfiles",
    ]
    + THIRD_PARTY_APPS
    + PROJECT_APPS
)

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "accounts.middleware.internationalization.TranslationMiddleware",
    "django.middleware.locale.LocaleMiddleware",
]

X_FRAME_OPTIONS = "ALLOWALL"


ROOT_URLCONF = "esign.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]


WSGI_APPLICATION = "esign.wsgi.application"
POSTGRES_DB_USE=os.getenv("POSTGRES_DB_USE",False)
if POSTGRES_DB_USE == "True":
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql',
            'NAME': os.getenv('DB_NAME'),
            'USER': os.getenv('DB_USER'),
            'PASSWORD': os.getenv('DB_PASSWORD'),
            'HOST': os.getenv('DB_HOST'),
            'PORT': os.getenv('DB_PORT'),
        }
    }
else:
    DATABASES = {
        "default": {
            "ENGINE": "django.db.backends.sqlite3",
            "NAME": BASE_DIR / "db.sqlite3",  # Path to your SQLite database file
        }
    }


AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation."
        "UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

USE_L10N = True

# Languages supported
LANGUAGES = [
    ("en", "English"),
    ("sv", "Swedish"),
]

# Path to translation files
LOCALE_PATHS = [
    os.path.join(BASE_DIR, "locale"),
]


LANGUAGE_COOKIE_NAME = "django_language"


STATIC_URL = '/en/dg/static/'
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'staticfiles')]

STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles_collected')



DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

AUTH_USER_MODEL = "accounts.User"

# BankID configurations
BANKID_BASE_URL = os.getenv("BANKID_BASE_URL")
BANKID_CERT_PATH = os.getenv("BANKID_CERT_PATH")
BANKID_KEY_PATH = os.getenv("BANKID_KEY_PATH", True)
BANK_ID_TEST_MODE = os.getenv("BANK_ID_TEST_MODE", "true").lower() in ("true", "1", "yes")
# BANKID_MOBILE_LINK = os.getenv("BANKID_MOBILE_LINK", "https://app.bankid.com/")
# BANKID_DESKTOP_LINK = os.getenv("BANKID_DESKTOP_LINK", "bankid:///")

# Websocketb BASE URL
WS_BASE_URL = os.getenv("WS_BASE_URL")

ASGI_APPLICATION = "esign.asgi.application"

# Redis configurations
REDIS_URL = os.getenv("REDIS_HOST", "redis://redis:6379")

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [REDIS_URL],
        },
    },
}

# CORS Settings
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [
    "https://api.skrivly.com",
    "https://app.skrivly.com",
    "http://localhost:8000"
]
CORS_ALLOWED_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-requested-with',
]
CSRF_TRUSTED_ORIGINS=["https://app.skrivly.com/","https://api.skrivly.com"]
EMAIL_BACKEND = os.getenv('EMAIL_BACKEND', 'django.core.mail.backends.smtp.EmailBackend')
EMAIL_HOST = os.getenv('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.getenv('EMAIL_PORT', 587))
EMAIL_USE_TLS = os.getenv('EMAIL_USE_TLS', 'True').lower() == 'true'
EMAIL_HOST_USER = os.getenv('EMAIL_HOST_USER', '<EMAIL>')
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', 'mrnb kgxg ftgn nukz')
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '<EMAIL>')
MAX_UPLOAD_SIZE = ********

# Encryption key should be stored in environment variables for security
ENCRYPTION_KEY = os.getenv("ENCRYPTION_KEY", "")

CELERY_TIMEZONE = "UTC"
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60
CELERY_BROKER_URL = os.getenv(
    "CELERY_BROKER_URL"
)  # Redis is running on localhost, database 0
CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_BACKEND = "django-db"

TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
TWILIO_PHONE_NUMBER = os.getenv("TWILIO_PHONE_NUMBER")

# Twilio SMS Configuration
# Set to True if you have an approved alphanumeric sender ID "Skrivly"
# Set to False if you need to use a phone number (will add "Skrivly" to message body)
USE_ALPHANUMERIC_SENDER = os.getenv("USE_ALPHANUMERIC_SENDER", "True").lower() == "true"

# Fallback phone number if alphanumeric sender is not available
TWILIO_FALLBACK_PHONE = os.getenv("TWILIO_FALLBACK_PHONE", None)

HMAC_SECRET_KEY = os.getenv("HMAC_SECRET_KEY")
ONE_TIME_SECRET_KEY = os.getenv("ONE_TIME_SECRET_KEY")


# Use BASE_DIR (already defined in settings.py) to create the log directory path
LOG_DIR = os.path.join(BASE_DIR, "logs")

# Create the directory if it does not exist
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)


LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "json": {
            "()": "pythonjsonlogger.jsonlogger.JsonFormatter",
            "format": "%(asctime)s %(levelname)s %(name)s %(message)s %(module)s %(funcName)s %(pathname)s %(lineno)s %(process)d %(thread)d",
        },
        "verbose": {
            "format": "{levelname} {asctime} {module} {process:d} {thread:d} {message}",
            "style": "{",
        },
    },
    "handlers": {
        "app_file": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": os.path.join(LOG_DIR, "app.log"),
            "maxBytes": 1024 * 1024 * 5,  # 5 MB
            "backupCount": 5,
            "formatter": "json",
        },
        "error_file": {
            "level": "ERROR",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": os.path.join(LOG_DIR, "error.log"),
            "maxBytes": 1024 * 1024 * 5,  # 5 MB
            "backupCount": 5,
            "formatter": "json",
        },
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "json",
        },
        "file": {
            "level": "INFO",
            "class": "logging.FileHandler",
            "filename": os.path.join(LOG_DIR, "document_expiry.log"),
            "formatter": "verbose",
        },
    },
    "loggers": {
        "app": {
            "handlers": ["console", "app_file", "error_file", "file"],
            "level": "INFO",
            "propagate": True,
        },
    },
}


REST_FRAMEWORK = {
    "DEFAULT_FILTER_BACKENDS": [
        "django_filters.rest_framework.DjangoFilterBackend",
        "rest_framework.filters.SearchFilter",
        "rest_framework.filters.OrderingFilter",
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': (
            'rest_framework_simplejwt.authentication.JWTAuthentication',
        ),
}

# Stripe Settings
ENVIRONMENT = os.getenv("ENVIRONMENT")

if ENVIRONMENT == "prod":
    STRIPE_SECRET_KEY = os.getenv("STRIPE_SECRET_KEY_LIVE")
    STRIPE_WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_SECRET_LIVE")
    STRIPE_PUBLISHABLE_KEY = os.getenv("STRIPE_PUBLISHABLE_KEY_LIVE")
else: # local or dev
    STRIPE_SECRET_KEY = os.getenv("STRIPE_SECRET_KEY_TEST")
    STRIPE_WEBHOOK_SECRET = os.getenv("STRIPE_WEBHOOK_SECRET_TEST")
    STRIPE_PUBLISHABLE_KEY = os.getenv("STRIPE_PUBLISHABLE_KEY_TEST")

DEFAULT_PRICE = os.getenv("DEFAULT_PRICE")

SWAGGER_SETTINGS = {
    "SECURITY_DEFINITIONS": {
        "Bearer": {"type": "apiKey", "name": "Authorization", "in": "header"}
    },
    "USE_SESSION_AUTH": False,
    "PERSIST_AUTH": True,
    "REFETCH_SCHEMA_WITH_AUTH": True,
    "REFETCH_SCHEMA_ON_LOGOUT": True,
    "DEFAULT_INFO": {
        "title": "Skrivly API Documentation",
        "description": "API documentation for Skrivly",
        "version": "v1",
        "contact": {"name": "API Support", "email": "<EMAIL>"},

    },
    "TAGS_SORTER": "alpha",
    "DOC_EXPANSION": "none",
}

# JWT Settings
from datetime import timedelta

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=4),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),
    "ROTATE_REFRESH_TOKENS": False,
    "BLACKLIST_AFTER_ROTATION": True,
    "UPDATE_LAST_LOGIN": False,
    "ALGORITHM": "HS256",
    "SIGNING_KEY": SECRET_KEY,
    "VERIFYING_KEY": None,
    "AUDIENCE": None,
    "ISSUER": None,
    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "id",
    "USER_ID_CLAIM": "user_id",
}


LOCAL_URL = os.getenv("LOCAL_URL")


STRIPE_SUCCESS_URL = os.getenv("FRONTEND_PAYMENT_SUCCESS_URL")
STRIPE_CANCEL_URL = os.getenv("FRONTEND_PAYMENT_FAILED_URL")

# CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL")  # Redis as the message broker
# CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND")
# CELERY_TIMEZONE = os.getenv("CELERY_TIMEZONE")



# OpenAI Settings
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

# Document AI Builder Settings
DOCUMENT_AI_BUILDER = {
    "MAX_TOKENS": 2000,
    "TEMPERATURE": 0.7,
    "TOP_K_DOCUMENTS": 3,
}

CKEDITOR_CONFIGS = {
    'default': {
        'toolbar': 'full',
        'height': 300,
        'width': 800,
    },
}

CKEDITOR_UPLOAD_PATH = "media/"

GROQ_API_KEY = os.getenv("GROQ_API_KEY")

# Document verification settings
CACHE_KEY_NAMESPACE = os.getenv("CACHE_KEY_NAMESPACE")
OTP_EXPIRY_SECONDS = os.getenv("OTP_EXPIRY_SECONDS")  # 5 minutes
TOKEN_EXPIRY_SECONDS = os.getenv("TOKEN_EXPIRY_SECONDS")  # 1 hour
MAX_OTP_ATTEMPTS = os.getenv("MAX_OTP_ATTEMPTS")

CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.redis.RedisCache",
        "LOCATION": REDIS_URL,
    }
}

CELERY_BEAT_SCHEDULE = {
    'check-expired-documents': {
        'task': 'documents.tasks.check_expired_documents',
        'schedule': timedelta(minutes=1),
    },
    'check-subscription-status': {
        'task': 'subscriptions.tasks.check_subscription_status',
        'schedule': crontab(hour=0, minute=0),  # Run at midnight every day
        'options': {
            'expires': 3600,  # Task expires after 1 hour
            'retry': True,
            'retry_policy': {
                'max_retries': 3,
                'interval_start': 300,  # 5 minutes
                'interval_step': 300,   # 5 minutes
                'interval_max': 900,    # 15 minutes
            }
        }
    },
    'check-expired-subscriptions-and-create-invoices': {
        'task': 'subscriptions.tasks.check_expired_subscriptions_and_create_invoices',
        'schedule': timedelta(days=1),  # Run daily
        'options': {
            'expires': 3600,  # Task expires after 1 hour
            'retry': True,
            'retry_policy': {
                'max_retries': 3,
                'interval_start': 300,
                'interval_step': 300,
                'interval_max': 900,
            }
        }
    },
    'compress-old-logs': {
        'task': 'utils_app.tasks.compress_old_logs',
        'schedule': timedelta(days=30),  # Run monthly
    },
    'send-agreement-expiry-notifications': {
        'task': 'documents.tasks.send_agreement_expiry_notifications',
        'schedule': crontab(minute=0, hour="*"),  # every hour
    },
    'remove-org-users-for-expired-subscriptions': {
        'task': 'subscriptions.tasks.remove_org_users_for_expired_subscriptions',
        'schedule': crontab(hour=0, minute=0),  # every night at midnight
    },
}

# Session settings
# SESSION_ENGINE = 'django.contrib.sessions.backends.db'  # or 'django.contrib.sessions.backends.cache'
# SESSION_COOKIE_AGE = 86400  # 24 hours in seconds
# SESSION_COOKIE_SECURE = True  # if using HTTPS
# SESSION_COOKIE_HTTPONLY = True
# SESSION_SAVE_EVERY_REQUEST = True

# Ensure log directory exists

# AWS S3 Configuration
AWS_ACCESS_KEY_ID = os.getenv('ACCESS_KEY')
AWS_SECRET_ACCESS_KEY = os.getenv('SECRET')
AWS_STORAGE_BUCKET_NAME = os.getenv('BUCKET_NAME')
AWS_S3_REGION_NAME = os.getenv('REGION')
AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.amazonaws.com'

# Media files configuration
# DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"

# S3 specific settings
# AWS_DEFAULT_ACL = None

 # Disable query parameter auth
# AWS_S3_FILE_OVERWRITE = False  # Don't overwrite files with the same name by default
AWS_S3_SIGNATURE_VERSION = 's3v4'  # Use signature version 4
AWS_S3_SIGNATURE_NAME = 's3v4'  # Use signature version 4

# MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/media/'

MEDIAFILES_LOCATION = 'media'

# class MediaStorage(S3Boto3Storage):
#     location = MEDIAFILES_LOCATION
#     file_overwrite = False


# MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/{MEDIAFILES_LOCATION}/'

# Storage Configuration
STORAGES = {
    "default": {
        "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
        "OPTIONS": {
            "bucket_name": AWS_STORAGE_BUCKET_NAME,
            "custom_domain": AWS_S3_CUSTOM_DOMAIN,
            "location": MEDIAFILES_LOCATION,
            "file_overwrite": False,
            "signature_version": "s3v4",
            "addressing_style": "virtual",
            "use_ssl": True,
            "object_parameters": {
                "ServerSideEncryption": "AES256"
            }
        },
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
    "media": {
        "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
        "OPTIONS": {
            "bucket_name": AWS_STORAGE_BUCKET_NAME,
            "custom_domain": AWS_S3_CUSTOM_DOMAIN,
            "location": "media",
            "file_overwrite": False,
            "signature_version": "s3v4",
            "addressing_style": "virtual",
            "use_ssl": True,
            "object_parameters": {
                "ServerSideEncryption": "AES256"
            }
        },
    }
}

MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/media/'

# Cookie namespace (optional, already in use)
COOKIE_KEY_NAMESPACE = "doc_cookie"

# Default max age for verification-related cookies (1 hour)
DEFAULT_COOKIE_MAX_AGE = 3600
CSRF_COOKIE_SAMESITE = 'Lax'
SESSION_COOKIE_SAMESITE = 'Lax'

# If you're using HTTPS in production, set this to True
CSRF_COOKIE_SECURE = False  # True in production
SESSION_COOKIE_SECURE = False  # True in production

DELETION_FLAG_TIMEOUT = 7200
