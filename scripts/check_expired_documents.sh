#!/bin/bash

# Get the directory where the script is located
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Activate virtual environment if you're using one
# source /path/to/your/virtualenv/bin/activate

# Navigate to project directory (one level up from scripts folder)
cd "$DIR/.."

# Run the management command
python manage.py check_expired_documents >> /var/log/esign/document_expiry.log 2>&1 