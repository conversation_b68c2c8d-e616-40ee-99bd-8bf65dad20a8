import json
import uuid
from decimal import Decimal
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework import status

from accounts.models.users import User
from accounts.models.organisation import Organization
from subscriptions.models.subscription_masters import (
    UserSubscription, PaymentTransaction, SubscriptionMasterPlan
)
from subscriptions.services.subscription_feature_service import SubscriptionFeatureManager


class BankIDTokenAPITestCase(TestCase):
    """
    Test cases for BankID token purchase API
    """
    
    def setUp(self):
        """
        Set up test data
        """
        self.client = APIClient()
        
        # Create test user
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
            role="org_superadmin",
            is_active=True
        )
        
        # Create test organization
        self.organization = Organization.objects.create(
            name="Test Organization",
            number="*********",
            vat="VAT123456",
            org_domain="test.com",
            bankid_tokens_remaining=0,
            bankid_tokens_purchased=0,
            bankid_tokens_used=0
        )
        
        # Add user to organization
        self.organization.users.add(self.user)
        
        # Create subscription plan
        self.subscription_plan = SubscriptionMasterPlan.objects.create(
            name="Test Plan",
            plan_category="business_monthly",
            plan_type="monthly",
            price=Decimal('29.99'),
            currency="USD",
            bankid_token_price=Decimal('2.00'),
            bankid_token_currency="USD",
            no_of_bankid_signs_allowed=1000,
            no_of_bankid_signs_allowed_unlimited=False
        )
        
        # Create user subscription
        self.user_subscription = UserSubscription.objects.create(
            user=self.user,
            plan=self.subscription_plan,
            status="active",
            start_date=timezone.now(),
            end_date=timezone.now() + timezone.timedelta(days=30),
            stripe_subscription_id="sub_test123",
            stripe_customer_id="cus_test123"
        )
        
        # Mock Stripe customer for organization owner
        self.user.stripe_customer_id = "cus_test123"
        self.user.save()
        
        # Authenticate the client
        self.client.force_authenticate(user=self.user)
    
    @patch('subscriptions.views.bankid_token_views.stripe.Invoice.create')
    @patch('subscriptions.views.bankid_token_views.stripe.InvoiceItem.create')
    @patch('subscriptions.views.bankid_token_views.stripe.Invoice.finalize_invoice')
    @patch('subscriptions.views.bankid_token_views.stripe.Invoice.pay')
    @patch('subscriptions.views.bankid_token_views.stripe.Subscription.retrieve')
    @patch('subscriptions.views.bankid_token_views.stripe.Subscription.modify')
    def test_purchase_bankid_tokens_success(self, mock_subscription_modify, mock_subscription_retrieve, 
                                           mock_invoice_pay, mock_invoice_finalize, mock_invoice_item_create, 
                                           mock_invoice_create):
        """
        Test successful BankID token purchase with immediate Stripe invoice
        """
        # Mock Stripe invoice creation
        mock_invoice = MagicMock()
        mock_invoice.id = "in_test123"
        mock_invoice.hosted_invoice_url = "https://invoice.url"
        mock_invoice.invoice_pdf = "https://invoice.pdf"
        mock_invoice_create.return_value = mock_invoice
        
        # Mock invoice item creation
        mock_invoice_item_create.return_value = MagicMock()
        
        # Mock invoice finalize and pay
        mock_invoice_finalize.return_value = mock_invoice
        mock_invoice_pay.return_value = mock_invoice
        
        # Mock Stripe subscription operations
        mock_subscription_retrieve.return_value = MagicMock(
            metadata={'existing_key': 'existing_value'}
        )
        mock_subscription_modify.return_value = MagicMock()
        
        # Test data
        data = {
            "quantity": 100
        }
        
        # Make API request
        url = reverse('bankid-token-purchase')
        response = self.client.post(url, data, format='json')
        
        # Assertions
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['status'], 'success')
        self.assertEqual(response_data['action'], 'data_updated')
        self.assertIn('payment_transaction_id', response_data['data'])
        self.assertEqual(response_data['data']['tokens_purchased'], 100)
        self.assertEqual(response_data['data']['tokens_remaining'], 100)
        self.assertEqual(response_data['data']['price_per_token'], 2.0)
        self.assertEqual(response_data['data']['amount_paid'], 200.0)
        
        # Verify payment transaction was created and marked as successful
        payment_transaction = PaymentTransaction.objects.get(
            transaction_type='bankid_token_purchase',
            bankid_token_quantity=100
        )
        self.assertEqual(payment_transaction.amount, Decimal('200.00'))
        self.assertEqual(payment_transaction.payment_status, 'successful')
        self.assertEqual(payment_transaction.invoice_id, "in_test123")
        
        # Verify organization tokens were updated
        self.organization.refresh_from_db()
        self.assertEqual(self.organization.bankid_tokens_remaining, 100)
        self.assertEqual(self.organization.bankid_tokens_purchased, 100)
    
    def test_purchase_bankid_tokens_invalid_quantity(self):
        """
        Test BankID token purchase with invalid quantity (not multiple of 100)
        """
        data = {
            "quantity": 150  # Not a multiple of 100
        }
        
        url = reverse('bankid-token-purchase')
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['status'], 'error')
        self.assertEqual(response_data['action'], 'validation_error')
        self.assertIn('Quantity must be in multiples of 100', response_data['message'])
    
    def test_purchase_bankid_tokens_member_permission_denied(self):
        """
        Test that org_member cannot purchase BankID tokens
        """
        # Change user role to org_member
        self.user.role = "org_member"
        self.user.save()
        
        data = {
            "quantity": 100
        }
        
        url = reverse('bankid-token-purchase')
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['status'], 'error')
        self.assertEqual(response_data['action'], 'permission_denied')
        self.assertIn('Members cannot purchase BankID tokens', response_data['message'])
    
    @patch('subscriptions.views.bankid_token_views.stripe.Invoice.create')
    def test_purchase_bankid_tokens_stripe_error(self, mock_invoice_create):
        """
        Test BankID token purchase with Stripe error
        """
        # Mock Stripe error
        import stripe
        mock_invoice_create.side_effect = stripe.error.StripeError("Stripe error occurred")
        
        data = {
            "quantity": 100
        }
        
        url = reverse('bankid-token-purchase')
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['status'], 'error')
        self.assertEqual(response_data['action'], 'stripe_error')
        self.assertIn('Payment processing error', response_data['message'])
        
        # Verify no payment transaction was created
        self.assertEqual(PaymentTransaction.objects.filter(
            transaction_type='bankid_token_purchase'
        ).count(), 0)
    
    def test_get_token_balance(self):
        """
        Test getting BankID token balance
        """
        # Set some tokens for the organization
        self.organization.bankid_tokens_remaining = 50
        self.organization.bankid_tokens_purchased = 100
        self.organization.bankid_tokens_used = 50
        self.organization.save()
        
        url = reverse('bankid-token-balance')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['status'], 'success')
        self.assertEqual(response_data['data']['tokens_remaining'], 50)
        self.assertEqual(response_data['data']['tokens_purchased'], 100)
        self.assertEqual(response_data['data']['tokens_used'], 50)
        self.assertEqual(response_data['data']['price_per_token'], 2.0)
        self.assertEqual(response_data['data']['currency'], 'USD')
    
    def test_get_transaction_history(self):
        """
        Test getting BankID token transaction history
        """
        # Create some test transactions
        PaymentTransaction.objects.create(
            user_subscription=self.user_subscription,
            organization=self.organization,
            transaction_type='bankid_token_purchase',
            amount=Decimal('200.00'),
            currency='USD',
            payment_status='successful',
            bankid_token_quantity=100,
            bankid_token_price_per_unit=Decimal('2.00'),
            initiated_by=self.user
        )
        
        PaymentTransaction.objects.create(
            user_subscription=self.user_subscription,
            organization=self.organization,
            transaction_type='bankid_token_usage',
            amount=Decimal('0.00'),
            currency='USD',
            payment_status='successful',
            bankid_token_quantity=1,
            bankid_token_price_per_unit=Decimal('2.00'),
            initiated_by=self.user
        )
        
        url = reverse('bankid-token-transactions')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['status'], 'success')
        self.assertEqual(len(response_data['data']['transactions']), 2)
        self.assertEqual(response_data['data']['total_count'], 2)
    
    def test_get_auto_topup_settings(self):
        """
        Test getting auto-topup settings
        """
        url = reverse('bankid-token-auto-topup-settings')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['status'], 'success')
        self.assertIn('enabled', response_data['data'])
        self.assertIn('threshold', response_data['data'])
        self.assertIn('quantity', response_data['data'])
    
    def test_update_auto_topup_settings_success(self):
        """
        Test updating auto-topup settings successfully
        """
        data = {
            "enabled": True,
            "threshold": 20,
            "quantity": 200
        }
        
        url = reverse('bankid-token-auto-topup-settings')
        response = self.client.put(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['status'], 'success')
        self.assertEqual(response_data['action'], 'settings_updated')
        
        # Verify settings were updated
        self.organization.refresh_from_db()
        self.assertTrue(self.organization.bankid_auto_topup_enabled)
        self.assertEqual(self.organization.bankid_auto_topup_threshold, 20)
        self.assertEqual(self.organization.bankid_auto_topup_quantity, 200)
    
    def test_update_auto_topup_settings_invalid_quantity(self):
        """
        Test updating auto-topup settings with invalid quantity
        """
        data = {
            "quantity": 150  # Not a multiple of 100
        }
        
        url = reverse('bankid-token-auto-topup-settings')
        response = self.client.put(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['status'], 'error')
        self.assertEqual(response_data['action'], 'validation_error')
        self.assertIn('Auto-topup quantity must be in multiples of 100', str(response_data['data']))
    
    def test_update_auto_topup_settings_member_permission_denied(self):
        """
        Test that org_member cannot update auto-topup settings
        """
        # Change user role to org_member
        self.user.role = "org_member"
        self.user.save()
        
        data = {
            "enabled": True
        }
        
        url = reverse('bankid-token-auto-topup-settings')
        response = self.client.put(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['status'], 'error')
        self.assertEqual(response_data['action'], 'permission_denied')
        self.assertIn('Members cannot modify auto-topup settings', response_data['message'])
    
    def test_purchase_tokens_no_subscription(self):
        """
        Test BankID token purchase when user has no active subscription
        """
        # Delete the user subscription
        self.user_subscription.delete()
        
        data = {
            "quantity": 100
        }
        
        url = reverse('bankid-token-purchase')
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['status'], 'error')
        self.assertEqual(response_data['action'], 'not_found')
        self.assertIn('No active subscription found', response_data['message'])
    
    def test_purchase_tokens_no_organization(self):
        """
        Test BankID token purchase when user has no organization
        """
        # Remove user from organization
        self.organization.users.remove(self.user)
        
        data = {
            "quantity": 100
        }
        
        url = reverse('bankid-token-purchase')
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        
        self.assertEqual(response_data['status'], 'error')
        self.assertEqual(response_data['action'], 'not_found')
        self.assertIn('Organization not found', response_data['message'])
