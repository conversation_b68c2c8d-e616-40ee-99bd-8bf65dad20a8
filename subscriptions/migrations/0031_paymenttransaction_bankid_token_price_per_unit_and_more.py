# Generated by Django 5.1.1 on 2025-09-08 07:41

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0022_organization_bankid_auto_topup_enabled_and_more"),
        ("subscriptions", "0030_subscriptionhistory_created_by"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="paymenttransaction",
            name="bankid_token_price_per_unit",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Price per BankID token",
                max_digits=10,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="paymenttransaction",
            name="bankid_token_quantity",
            field=models.IntegerField(
                blank=True,
                help_text="Number of BankID tokens involved in this transaction",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="paymenttransaction",
            name="initiated_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="paymenttransaction",
            name="organization",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="payment_transactions",
                to="accounts.organization",
            ),
        ),
        migrations.AddField(
            model_name="paymenttransaction",
            name="transaction_type",
            field=models.CharField(
                choices=[
                    ("subscription", "Subscription"),
                    ("addon_user", "Add-on User"),
                    ("bankid_token_purchase", "BankID Token Purchase"),
                    ("bankid_token_usage", "BankID Token Usage"),
                    ("bankid_token_refund", "BankID Token Refund"),
                    ("bankid_token_auto_topup", "BankID Token Auto Top-up"),
                ],
                default="subscription",
                max_length=30,
            ),
        ),
        migrations.AddField(
            model_name="subscriptionmasterplan",
            name="bankid_token_count",
            field=models.IntegerField(default=100, help_text="Number of BankID tokens"),
        ),
        migrations.AddField(
            model_name="subscriptionmasterplan",
            name="bankid_token_currency",
            field=models.CharField(
                default="USD",
                help_text="Currency for BankID token pricing",
                max_length=3,
            ),
        ),
        migrations.AddField(
            model_name="subscriptionmasterplan",
            name="bankid_token_price",
            field=models.DecimalField(
                decimal_places=2,
                default=2.0,
                help_text="Price per BankID token",
                max_digits=200,
            ),
        ),
        migrations.CreateModel(
            name="BankIDUsageTracking",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "document_ref_id",
                    models.CharField(
                        help_text="Reference ID of the document being signed",
                        max_length=255,
                    ),
                ),
                (
                    "signer_ref_id",
                    models.CharField(
                        help_text="Reference ID of the signer", max_length=255
                    ),
                ),
                (
                    "document_name",
                    models.CharField(
                        blank=True,
                        help_text="Name/title of the document",
                        max_length=500,
                        null=True,
                    ),
                ),
                (
                    "signer_name",
                    models.CharField(
                        blank=True,
                        help_text="Name of the signer",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "signer_email",
                    models.EmailField(
                        blank=True,
                        help_text="Email of the signer",
                        max_length=254,
                        null=True,
                    ),
                ),
                (
                    "usage_status",
                    models.CharField(
                        choices=[
                            ("initiated", "Initiated"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="initiated",
                        max_length=20,
                    ),
                ),
                (
                    "tokens_consumed",
                    models.IntegerField(
                        default=1, help_text="Number of tokens consumed for this usage"
                    ),
                ),
                ("initiated_at", models.DateTimeField(auto_now_add=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, help_text="IP address of the signer", null=True
                    ),
                ),
                (
                    "user_agent",
                    models.TextField(
                        blank=True,
                        help_text="User agent of the signer's browser",
                        null=True,
                    ),
                ),
                (
                    "session_id",
                    models.CharField(
                        blank=True,
                        help_text="Session ID for tracking",
                        max_length=255,
                        null=True,
                    ),
                ),
                (
                    "initiated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="bankid_usage_initiated",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bankid_usage_tracking",
                        to="accounts.organization",
                    ),
                ),
                (
                    "user_subscription",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bankid_usage_tracking",
                        to="subscriptions.usersubscription",
                    ),
                ),
            ],
            options={
                "ordering": ["-initiated_at"],
                "indexes": [
                    models.Index(
                        fields=["document_ref_id"],
                        name="subscriptio_documen_f3ae9d_idx",
                    ),
                    models.Index(
                        fields=["signer_ref_id"], name="subscriptio_signer__e42262_idx"
                    ),
                    models.Index(
                        fields=["usage_status"], name="subscriptio_usage_s_92e4a7_idx"
                    ),
                    models.Index(
                        fields=["initiated_at"], name="subscriptio_initiat_2e2d6f_idx"
                    ),
                ],
            },
        ),
    ]
