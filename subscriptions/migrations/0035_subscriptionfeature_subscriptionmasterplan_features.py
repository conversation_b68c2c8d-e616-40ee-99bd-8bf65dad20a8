# Generated by Django 5.1.1 on 2025-09-25 07:18

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("subscriptions", "0034_subscriptionmasterplan_is_deprecated"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SubscriptionFeature",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Unique feature name", max_length=100, unique=True
                    ),
                ),
                ("description", models.TextField(help_text="Feature description")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("deprecated", "Deprecated"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                (
                    "prev_status",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("deprecated", "Deprecated"),
                        ],
                        help_text="Previous status for tracking changes",
                        max_length=20,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who created this feature",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_subscription_features",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "updated_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who last updated this feature",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="updated_subscription_features",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Subscription Feature",
                "verbose_name_plural": "Subscription Features",
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="subscriptionmasterplan",
            name="features",
            field=models.ManyToManyField(
                blank=True,
                help_text="Features included in this subscription plan",
                related_name="subscription_plans",
                to="subscriptions.subscriptionfeature",
            ),
        ),
    ]
