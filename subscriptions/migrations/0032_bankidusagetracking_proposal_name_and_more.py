# Generated by Django 5.1.1 on 2025-09-08 14:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "subscriptions",
            "0031_paymenttransaction_bankid_token_price_per_unit_and_more",
        ),
    ]

    operations = [
        migrations.AddField(
            model_name="bankidusagetracking",
            name="proposal_name",
            field=models.CharField(
                blank=True,
                help_text="Name/title of the proposal",
                max_length=500,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="bankidusagetracking",
            name="proposal_ref_id",
            field=models.CharField(
                blank=True,
                help_text="Reference ID of the proposal",
                max_length=255,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="subscriptionhistory",
            name="proposals_used",
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name="subscriptionmasterplan",
            name="no_of_proposals_allowed",
            field=models.Integer<PERSON>ield(default=0),
        ),
        migrations.Add<PERSON>ield(
            model_name="subscriptionmasterplan",
            name="no_of_proposals_allowed_unlimited",
            field=models.<PERSON><PERSON>an<PERSON>ield(default=False),
        ),
        migrations.AddField(
            model_name="subscriptionusage",
            name="proposals_used",
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="paymenttransaction",
            name="bankid_token_price_per_unit",
            field=models.DecimalField(
                blank=True,
                decimal_places=2,
                help_text="Price per BankID token",
                max_digits=20,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="subscriptionmasterplan",
            name="bankid_token_count",
            field=models.IntegerField(default=0, help_text="Number of BankID tokens"),
        ),
        migrations.AlterField(
            model_name="subscriptionmasterplan",
            name="bankid_token_price",
            field=models.DecimalField(
                decimal_places=2,
                default=2.0,
                help_text="Price per BankID token",
                max_digits=20,
            ),
        ),
    ]
