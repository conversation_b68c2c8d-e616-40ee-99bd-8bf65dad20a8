import logging
import stripe
from decimal import Decimal
from django.conf import settings
from django.utils import timezone
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from documents.utils.jwt_verifier import JWTAccessTokenAuthentication
from esign.utils.custom_response import api_response
from subscriptions.models.subscription_masters import (
    PaymentTransaction, SubscriptionMasterPlan
)
from subscriptions.services.bankid_token_service import BankIDTokenService
from subscriptions.services.subscription_feature_service import SubscriptionFeatureManager
from subscriptions.serializers.bankid_token_serializers import (
    BankIDTokenPurchaseSerializer,
    BankIDAutoTopupSettingsSerializer
)
from accounts.tasks import send_payment_success_confirmation_email_task

logger = logging.getLogger('app')
stripe.api_key = settings.STRIPE_SECRET_KEY


class BankIDTokenPurchaseView(APIView):
    """
    API view for purchasing BankID tokens
    """
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Purchase BankID tokens for the user's organization using immediate Stripe invoice
        """
        try:
            # Validate input
            serializer = BankIDTokenPurchaseSerializer(data=request.data)
            if not serializer.is_valid():
                # Extract the first error message to use as the main message
                error_message = "Invalid input data"
                if serializer.errors:
                    # Check for non_field_errors first
                    if 'non_field_errors' in serializer.errors and serializer.errors['non_field_errors']:
                        error_message = serializer.errors['non_field_errors'][0]
                    else:
                        # Get the first error message from any field
                        for field, errors in serializer.errors.items():
                            if isinstance(errors, list) and errors:
                                error_message = errors[0]
                                break
                            elif isinstance(errors, dict) and errors:
                                # If errors is a dict, get the first value
                                first_value = next(iter(errors.values()))
                                if isinstance(first_value, list) and first_value:
                                    error_message = first_value[0]
                                else:
                                    error_message = str(first_value)
                                break
                
                return api_response(
                    action="validation_error",
                    message=error_message,
                    data=serializer.errors,
                    status="error"
                )

            quantity = serializer.validated_data['quantity']

            # Check if user has permission to purchase tokens
            if request.user.role in ["org_member", "org_admin"]:
                return api_response(
                    action="permission_denied",
                    message="Members and admins cannot purchase BankID tokens. Only organization admins can make purchases.",
                    status="error"
                )

            # Get user's subscription and organization
            subscription_feature = SubscriptionFeatureManager(request.user)
            organization_subscription = subscription_feature.get_organisation_subscription()

            if not organization_subscription:
                return api_response(
                    action="not_found",
                    message="No active subscription found for this user",
                    status="error"
                )

            organization = subscription_feature.get_organization()
            if not organization:
                return api_response(
                    action="not_found",
                    message="Organization not found",
                    status="error"
                )

            # Get subscription plan for pricing
            try:
                subscription_plan = SubscriptionMasterPlan.objects.get(
                    id=organization_subscription.plan.id
                )
            except SubscriptionMasterPlan.DoesNotExist:
                return api_response(
                    action="not_found",
                    message="Subscription plan not found",
                    status="error"
                )

            # Calculate pricing
            price_per_token = getattr(
                subscription_plan, 'bankid_token_price', Decimal('2.00'))
            currency = getattr(subscription_plan, 'bankid_token_currency', 'USD')
            total_amount = price_per_token * quantity

            # Create payment transaction
            payment_transaction = PaymentTransaction.objects.create(
                user_subscription=organization_subscription,
                organization=organization,
                transaction_type='bankid_token_purchase',
                amount=total_amount,
                currency=currency,
                payment_status='pending',
                bankid_token_quantity=quantity,
                bankid_token_price_per_unit=price_per_token,
                notes=f"Purchase of {quantity} BankID tokens",
                initiated_by=request.user
            )

            try:
                # Get organization owner for Stripe customer
                owner = organization.get_organization_owner()
                if not owner or not owner.stripe_customer_id:
                    return api_response(
                        action="payment_error",
                        message="Organization owner not found or no Stripe customer ID available",
                        status="error"
                    )

                # Check if organization has tax_id
                has_tax_id = False
                if organization and organization.tax_id:
                    try:
                        decrypted_tax_id = organization.get_decrypted_tax_id()
                        has_tax_id = bool(decrypted_tax_id)
                    except Exception:
                        logger.error(
                            f"Failed to decrypt the tax id for organization id: {organization.id}")
                        has_tax_id = False

                # Create immediate one-time invoice for the BankID tokens
                invoice = stripe.Invoice.create(
                    customer=organization_subscription.stripe_customer_id,
                    collection_method='charge_automatically',
                    auto_advance=True,
                    metadata={
                        'user_id': str(request.user.id),
                        'subscription_id': str(organization_subscription.id),
                        'organization_id': str(organization.id),
                        'payment_transaction_id': str(payment_transaction.id),
                        'bankid_tokens': str(quantity),
                        'plan_name': subscription_plan.name,
                        'plan_type': subscription_plan.plan_type,
                        'billing_rule': 'bankid_token_immediate_charge'
                    },
                    automatic_tax={'enabled': True}
                )

                # Add invoice item for the BankID tokens
                stripe.InvoiceItem.create(
                    customer=organization_subscription.stripe_customer_id,
                    invoice=invoice.id,
                    quantity=quantity,
                    unit_amount=int(price_per_token * 100),  # Convert to cents
                    currency=currency,
                    description=f"BankID Tokens ({quantity} x ${price_per_token}) - {subscription_plan.name} - {subscription_plan.plan_type} plan",
                    metadata={
                        'plan_name': subscription_plan.name,
                        'plan_type': subscription_plan.plan_type,
                        'tokens_purchased': str(quantity),
                        'price_per_token': str(price_per_token)
                    }
                )

                # Finalize and pay the invoice immediately
                stripe.Invoice.finalize_invoice(invoice.id)
                paid_invoice = stripe.Invoice.pay(invoice.id)

                # Add tokens to organization
                organization.add_bankid_tokens(quantity, purchased=True)

                # Update payment transaction with invoice details
                payment_transaction.payment_status = 'successful'
                payment_transaction.invoice_id = paid_invoice.id
                payment_transaction.invoice_url = paid_invoice.hosted_invoice_url
                payment_transaction.invoice_pdf_url = paid_invoice.invoice_pdf
                payment_transaction.customer_id = organization_subscription.stripe_customer_id
                payment_transaction.payment_date = timezone.now()
                payment_transaction.save()

                # Update Stripe subscription metadata with new token count
                try:
                    stripe_subscription = stripe.Subscription.retrieve(
                        organization_subscription.stripe_subscription_id
                    )

                    # Update metadata with new token count
                    stripe.Subscription.modify(
                        organization_subscription.stripe_subscription_id,
                        metadata={
                            **stripe_subscription.metadata,
                            'bankid_tokens_remaining': str(organization.bankid_tokens_remaining),
                            'last_token_purchase': timezone.now().isoformat(),
                            'last_token_purchase_quantity': str(quantity)
                        }
                    )

                    logger.info(
                        f"Updated Stripe subscription metadata for organization {organization.id}")

                except stripe.error.StripeError as e:
                    logger.error(
                        f"Failed to update Stripe subscription metadata: {str(e)}")
                    # Don't fail the transaction for metadata update failure

                # Send payment confirmation email
                if owner:
                    send_payment_success_confirmation_email_task.delay(
                        user_email=owner.get_decrypted_email(),
                        user_first_name=owner.first_name,
                        plan_name=subscription_plan.name,
                        payment_date=timezone.now().strftime("%B %d, %Y"),
                        billing_cycle="One-time Purchase",
                        amount_paid=float(total_amount),
                        invoice_pdf_url=paid_invoice.invoice_pdf
                    )

                return api_response(
                    action="data_updated",
                    message="BankID tokens purchased successfully with immediate billing",
                    data={
                        'subscription_id': str(organization_subscription.id),
                        'payment_transaction_id': str(payment_transaction.id),
                        'tokens_purchased': quantity,
                        'tokens_remaining': organization.bankid_tokens_remaining,
                        'amount_paid': float(total_amount),
                        'price_per_token': float(price_per_token),
                        'currency': currency,
                        'invoice_url': paid_invoice.hosted_invoice_url,
                        'invoice_pdf_url': paid_invoice.invoice_pdf,
                        'organization_name': organization.name,
                        'plan_name': subscription_plan.name
                    },
                    status="success"
                )

            except stripe.error.StripeError as e:
                logger.error(
                    f"Stripe error during token purchase: {str(e)}", exc_info=True)
                # Clean up payment transaction if Stripe operation failed
                payment_transaction.delete()
                return api_response(
                    action="stripe_error",
                    message=f"Payment processing error: {str(e)}",
                    status="error"
                )

        except Exception as e:
            logger.error(
                f"Error processing BankID token purchase: {str(e)}", exc_info=True)
            return api_response(
                action="server_error",
                message="An error occurred while processing your request",
                status="error"
            )


class BankIDTokenBalanceView(APIView):
    """
    API view for getting BankID token balance
    """
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get current BankID token balance for the user's organization
        """
        try:
            # Get user's subscription and organization
            subscription_feature = SubscriptionFeatureManager(request.user)
            organization_subscription = subscription_feature.get_organisation_subscription()

            if not organization_subscription:
                return api_response(
                    action="not_found",
                    message="No active subscription found for this user",
                    status="error"
                )

            organization = subscription_feature.get_organization()
            if not organization:
                return api_response(
                    action="not_found",
                    message="Organization not found",
                    status="error"
                )

            # Initialize BankID token service
            bankid_service = BankIDTokenService()

            # Get token balance
            balance_data = bankid_service.get_token_balance(
                organization, organization_subscription)

            return api_response(
                action="data_retrieved",
                message="Token balance retrieved successfully",
                data=balance_data,
                status="success"
            )

        except Exception as e:
            logger.error(
                f"Error retrieving BankID token balance: {str(e)}", exc_info=True)
            return api_response(
                action="server_error",
                message="An error occurred while retrieving token balance",
                status="error"
            )


class BankIDTokenTransactionHistoryView(APIView):
    """
    API view for getting BankID token transaction history
    """
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get BankID token transaction history for the user's organization
        """
        try:
            # Get user's subscription and organization
            subscription_feature = SubscriptionFeatureManager(request.user)
            organization_subscription = subscription_feature.get_organisation_subscription()

            if not organization_subscription:
                return api_response(
                    action="not_found",
                    message="No active subscription found for this user",
                    status="error"
                )

            organization = subscription_feature.get_organization()
            if not organization:
                return api_response(
                    action="not_found",
                    message="Organization not found",
                    status="error"
                )

            # Initialize BankID token service
            bankid_service = BankIDTokenService()

            # Get transaction history
            limit = request.query_params.get('limit', 50)
            try:
                limit = int(limit)
            except ValueError:
                limit = 50

            transactions = bankid_service.get_transaction_history(organization, limit)

            return api_response(
                action="data_retrieved",
                message="Transaction history retrieved successfully",
                data={
                    'transactions': transactions,
                    'total_count': len(transactions)
                },
                status="success"
            )

        except Exception as e:
            logger.error(
                f"Error retrieving BankID token transaction history: {str(e)}", exc_info=True)
            return api_response(
                action="server_error",
                message="An error occurred while retrieving transaction history",
                status="error"
            )


class BankIDAutoTopupSettingsView(APIView):
    """
    API view for managing BankID auto-topup settings
    """
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get current auto-topup settings
        """
        try:
            # Get user's organization
            subscription_feature = SubscriptionFeatureManager(request.user)
            organization = subscription_feature.get_organization()

            if not organization:
                return api_response(
                    action="not_found",
                    message="Organization not found",
                    status="error"
                )

            settings_data = {
                'enabled': organization.bankid_auto_topup_enabled,
                'threshold': organization.bankid_auto_topup_threshold,
                'quantity': organization.bankid_auto_topup_quantity
            }

            return api_response(
                action="data_retrieved",
                message="Auto-topup settings retrieved successfully",
                data=settings_data,
                status="success"
            )

        except Exception as e:
            logger.error(
                f"Error retrieving auto-topup settings: {str(e)}", exc_info=True)
            return api_response(
                action="server_error",
                message="An error occurred while retrieving settings",
                status="error"
            )

    def put(self, request):
        """
        Update auto-topup settings
        """
        try:
            # Validate input
            serializer = BankIDAutoTopupSettingsSerializer(data=request.data)
            if not serializer.is_valid():
                # Extract the first error message to use as the main message
                error_message = "Invalid input data"
                if serializer.errors:
                    # Check for non_field_errors first
                    if 'non_field_errors' in serializer.errors and serializer.errors['non_field_errors']:
                        error_message = serializer.errors['non_field_errors'][0]
                    else:
                        # Get the first error message from any field
                        for field, errors in serializer.errors.items():
                            if isinstance(errors, list) and errors:
                                error_message = errors[0]
                                break
                            elif isinstance(errors, dict) and errors:
                                # If errors is a dict, get the first value
                                first_value = next(iter(errors.values()))
                                if isinstance(first_value, list) and first_value:
                                    error_message = first_value[0]
                                else:
                                    error_message = str(first_value)
                                break
                
                return api_response(
                    action="validation_error",
                    message=error_message,
                    data=serializer.errors,
                    status="error"
                )

            # Get user's organization
            subscription_feature = SubscriptionFeatureManager(request.user)
            organization = subscription_feature.get_organization()

            if not organization:
                return api_response(
                    action="not_found",
                    message="Organization not found",
                    status="error"
                )

            # Check permissions
            if request.user.role == "org_member":
                return api_response(
                    action="permission_denied",
                    message="Members cannot modify auto-topup settings",
                    status="error"
                )

            # Get validated data
            validated_data = serializer.validated_data
            enabled = validated_data.get('enabled')
            threshold = validated_data.get('threshold')
            quantity = validated_data.get('quantity')

            # Prepare update data based on business rules
            update_data = {}

            # Always update enabled if provided
            if enabled is not None:
                update_data['bankid_auto_topup_enabled'] = enabled

            # Only update threshold and quantity if enabled is True or not provided (keeping current state)
            if enabled is True or enabled is None:
                if threshold is not None:
                    update_data['bankid_auto_topup_threshold'] = threshold
                if quantity is not None:
                    update_data['bankid_auto_topup_quantity'] = quantity

            # Update organization settings
            if update_data:
                for field, value in update_data.items():
                    setattr(organization, field, value)
                organization.save(update_fields=list(update_data.keys()))

                logger.info(
                    f"Updated auto-topup settings for organization {organization.id}: {update_data}")
                return api_response(
                    action="data_updated",
                    message="Auto-topup settings updated successfully",
                    data={
                        'enabled': organization.bankid_auto_topup_enabled,
                        'threshold': organization.bankid_auto_topup_threshold,
                        'quantity': organization.bankid_auto_topup_quantity
                    },
                    status="success"
                )
            else:
                return api_response(
                    action="no_changes",
                    message="No changes were made to auto-topup settings",
                    data={
                        'enabled': organization.bankid_auto_topup_enabled,
                        'threshold': organization.bankid_auto_topup_threshold,
                        'quantity': organization.bankid_auto_topup_quantity
                    },
                    status="success"
                )

        except Exception as e:
            logger.error(f"Error updating auto-topup settings: {str(e)}", exc_info=True)
            return api_response(
                action="server_error",
                message="An error occurred while updating settings",
                status="error"
            )
