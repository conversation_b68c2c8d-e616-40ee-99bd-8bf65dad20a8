import logging
import stripe
from decimal import Decimal
from django.conf import settings
from django.utils import timezone
from django.db import transaction

from subscriptions.models.subscription_masters import (
    PaymentTransaction,
    BankIDUsageTracking
)

logger = logging.getLogger('app')


class BankIDTokenService:
    """
    Service to handle BankID token purchases, usage tracking, and auto-topup functionality
    """

    def __init__(self):
        stripe.api_key = settings.STRIPE_SECRET_KEY

    def purchase_tokens(self, organization, user_subscription, quantity, initiated_by=None):
        """
        Purchase BankID tokens for an organization

        Args:
            organization: Organization instance
            user_subscription: UserSubscription instance
            quantity: Number of tokens to purchase (must be in multiples of 100)
            initiated_by: User who initiated the purchase

        Returns:
            dict: Result with success status and transaction details
        """
        # Validate quantity is in multiples of 100
        if quantity % 100 != 0:
            return {
                'success': False,
                'message': 'Quantity must be in multiples of 100. For example: 100, 200, 300, etc.'
            }
        try:
            with transaction.atomic():
                # Get BankID token pricing from the subscription plan
                plan = user_subscription.plan
                # Assuming we'll add BankID token pricing to the plan or use a default
                price_per_token = getattr(plan, 'bankid_token_price', Decimal('2.00'))
                currency = getattr(plan, 'bankid_token_currency', 'USD')
                total_amount = price_per_token * quantity

                # Get organization owner for Stripe customer
                owner = organization.get_organization_owner()

                if not owner or not owner.stripe_customer_id:
                    return {
                        'success': False,
                        'message': 'Organization owner not found or no Stripe customer ID available'
                    }

                # Create Stripe payment intent
                payment_intent = stripe.PaymentIntent.create(
                    amount=int(total_amount * 100),  # Convert to cents
                    currency=currency.lower(),
                    customer=owner.stripe_customer_id,
                    metadata={
                        'organization_id': str(organization.id),
                        'user_subscription_id': str(user_subscription.id),
                        'token_quantity': str(quantity),
                        'transaction_type': 'bankid_token_purchase'
                    },
                    description=f"BankID Token Purchase - {quantity} tokens for {organization.name}"
                )

                # Create payment transaction record
                payment_transaction = PaymentTransaction.objects.create(
                    user_subscription=user_subscription,
                    organization=organization,
                    transaction_type='bankid_token_purchase',
                    amount=total_amount,
                    currency=currency,
                    payment_status='pending',
                    stripe_payment_intent_id=payment_intent.id,
                    bankid_token_quantity=quantity,
                    bankid_token_price_per_unit=price_per_token,
                    notes=f"Purchase of {quantity} BankID tokens",
                    initiated_by=initiated_by
                )

                logger.info(
                    f"Created BankID token purchase transaction: {payment_transaction.id}")

                return {
                    'success': True,
                    'transaction_id': str(payment_transaction.id),
                    'payment_intent_id': payment_intent.id,
                    'client_secret': payment_intent.client_secret,
                    'total_amount': float(total_amount),
                    'currency': currency
                }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error during token purchase: {str(e)}")
            return {
                'success': False,
                'message': f"Payment processing error: {str(e)}"
            }
        except Exception as e:
            logger.error(f"Error purchasing BankID tokens: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f"An error occurred while purchasing tokens: {str(e)}"
            }

    def confirm_payment(self, payment_intent_id):
        """
        Confirm payment and add tokens to subscription

        Args:
            payment_intent_id: Stripe payment intent ID

        Returns:
            dict: Result with success status
        """
        try:
            # Retrieve payment intent from Stripe
            payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)

            if payment_intent.status != 'succeeded':
                return {
                    'success': False,
                    'message': f"Payment not successful. Status: {payment_intent.status}"
                }

            # Find the transaction
            try:
                payment_transaction = PaymentTransaction.objects.get(
                    stripe_payment_intent_id=payment_intent_id,
                    transaction_type='bankid_token_purchase'
                )
            except PaymentTransaction.DoesNotExist:
                return {
                    'success': False,
                    'message': 'Transaction not found'
                }

            # Update transaction status
            payment_transaction.payment_status = 'successful'
            payment_transaction.save()

            # Add tokens to organization
            organization = payment_transaction.organization
            organization.add_bankid_tokens(
                quantity=payment_transaction.bankid_token_quantity,
                purchased=True
            )

            logger.info(
                f"Successfully added {payment_transaction.bankid_token_quantity} tokens to organization {organization.id}")

            return {
                'success': True,
                'message': f"Successfully purchased {payment_transaction.bankid_token_quantity} BankID tokens",
                'tokens_added': payment_transaction.bankid_token_quantity,
                'total_tokens': organization.bankid_tokens_remaining
            }

        except stripe.error.StripeError as e:
            logger.error(f"Stripe error confirming payment: {str(e)}")
            return {
                'success': False,
                'message': f"Error confirming payment: {str(e)}"
            }
        except Exception as e:
            logger.error(f"Error confirming payment: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f"An error occurred while confirming payment: {str(e)}"
            }

    def consume_token(self, organization, user_subscription, used_by=None, document_ref_id=None, signer_ref_id=None, document_name=None, signer_name=None, signer_email=None, ip_address=None, user_agent=None, session_id=None):
        """
        Consume a BankID token for an organization

        Args:
            organization: Organization instance
            user_subscription: UserSubscription instance
            used_by: User who used the token
            document_ref_id: Reference ID of the document being signed
            signer_ref_id: Reference ID of the signer
            document_name: Name/title of the document
            signer_name: Name of the signer
            signer_email: Email of the signer
            ip_address: IP address of the signer
            user_agent: User agent of the signer's browser
            session_id: Session ID for tracking

        Returns:
            dict: Result with success status
        """
        try:
            if organization.bankid_tokens_remaining <= 0:
                return {
                    'success': False,
                    'message': 'No BankID tokens remaining',
                    'tokens_remaining': 0
                }

            # Consume token
            success = organization.consume_bankid_token()

            if success:
                # Get pricing from the subscription plan
                plan = user_subscription.plan
                price_per_token = getattr(plan, 'bankid_token_price', Decimal('2.00'))
                currency = getattr(plan, 'bankid_token_currency', 'USD')

                # Create usage transaction record
                payment_transaction = PaymentTransaction.objects.create(
                    user_subscription=user_subscription,
                    organization=organization,
                    transaction_type='bankid_token_usage',
                    amount=Decimal('0.00'),  # No cost for usage
                    currency=currency,
                    payment_status='successful',
                    bankid_token_quantity=1,
                    bankid_token_price_per_unit=price_per_token,
                    notes="BankID token consumed for signature",
                    initiated_by=used_by
                )

                # Create detailed usage tracking record
                BankIDUsageTracking.objects.create(
                    user_subscription=user_subscription,
                    organization=organization,
                    payment_transaction=payment_transaction,
                    document_ref_id=document_ref_id or '',
                    signer_ref_id=signer_ref_id or '',
                    document_name=document_name,
                    signer_name=signer_name,
                    signer_email=signer_email,
                    usage_status='completed',
                    tokens_consumed=1,
                    completed_at=timezone.now(),
                    ip_address=ip_address,
                    user_agent=user_agent,
                    session_id=session_id,
                    initiated_by=used_by
                )

                logger.info(f"Consumed BankID token for organization {organization.id}")

                return {
                    'success': True,
                    'message': 'BankID token consumed successfully',
                    'tokens_remaining': organization.bankid_tokens_remaining
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to consume token',
                    'tokens_remaining': organization.bankid_tokens_remaining
                }

        except Exception as e:
            logger.error(f"Error consuming BankID token: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f"An error occurred while consuming token: {str(e)}"
            }

    def get_token_balance(self, organization, user_subscription=None):
        """
        Get current token balance for an organization

        Args:
            organization: Organization instance
            user_subscription: UserSubscription instance (optional, for pricing info)

        Returns:
            dict: Token balance information
        """
        # Get pricing from subscription plan if available
        price_per_token = Decimal('2.00')  # Default price
        currency = 'USD'  # Default currency

        if user_subscription and user_subscription.plan:
            plan = user_subscription.plan
            price_per_token = getattr(plan, 'bankid_token_price', price_per_token)
            currency = getattr(plan, 'bankid_token_currency', currency)

        return {
            'tokens_remaining': organization.bankid_tokens_remaining,
            'tokens_used': organization.bankid_tokens_used,
            'tokens_purchased': organization.bankid_tokens_purchased,
            'price_per_token': float(price_per_token),
            'currency': currency,
            'auto_topup_enabled': organization.bankid_auto_topup_enabled,
            'auto_topup_threshold': organization.bankid_auto_topup_threshold,
            'auto_topup_quantity': organization.bankid_auto_topup_quantity
        }

    def update_auto_topup_settings(self, organization, enabled=None, threshold=None, quantity=None):
        """
        Update auto-topup settings for an organization

        Args:
            organization: Organization instance
            enabled: Enable/disable auto-topup
            threshold: Minimum tokens before auto-topup triggers
            quantity: Number of tokens to purchase during auto-topup

        Returns:
            dict: Result with success status
        """
        try:
            update_fields = []

            if enabled is not None:
                organization.bankid_auto_topup_enabled = enabled
                update_fields.append('bankid_auto_topup_enabled')

            if threshold is not None:
                organization.bankid_auto_topup_threshold = threshold
                update_fields.append('bankid_auto_topup_threshold')

            if quantity is not None:
                organization.bankid_auto_topup_quantity = quantity
                update_fields.append('bankid_auto_topup_quantity')

            if update_fields:
                organization.save(update_fields=update_fields)

                logger.info(
                    f"Updated auto-topup settings for organization {organization.id}")

                return {
                    'success': True,
                    'message': 'Auto-topup settings updated successfully',
                    'settings': {
                        'enabled': organization.bankid_auto_topup_enabled,
                        'threshold': organization.bankid_auto_topup_threshold,
                        'quantity': organization.bankid_auto_topup_quantity
                    }
                }
            else:
                return {
                    'success': False,
                    'message': 'No settings provided to update'
                }

        except Exception as e:
            logger.error(f"Error updating auto-topup settings: {str(e)}", exc_info=True)
            return {
                'success': False,
                'message': f"An error occurred while updating settings: {str(e)}"
            }

    def get_transaction_history(self, organization, limit=50):
        """
        Get transaction history for an organization

        Args:
            organization: Organization instance
            limit: Maximum number of transactions to return

        Returns:
            list: List of transaction records
        """
        transactions = PaymentTransaction.objects.filter(
            organization=organization,
            transaction_type__in=['bankid_token_purchase', 'bankid_token_usage',
                                  'bankid_token_refund', 'bankid_token_auto_topup']
        ).order_by('-payment_date')[:limit]

        return [
            {
                'id': str(transaction.id),
                'transaction_type': transaction.transaction_type,
                'quantity': transaction.bankid_token_quantity,
                'price_per_token': float(transaction.bankid_token_price_per_unit) if transaction.bankid_token_price_per_unit else 0.0,
                'total_amount': float(transaction.amount) if transaction.amount else 0.0,
                'currency': transaction.currency,
                'payment_status': transaction.payment_status,
                'description': transaction.notes,
                'created_at': transaction.payment_date.isoformat(),
                'initiated_by': transaction.initiated_by.get_full_name() if transaction.initiated_by else None
            }
            for transaction in transactions
        ]
