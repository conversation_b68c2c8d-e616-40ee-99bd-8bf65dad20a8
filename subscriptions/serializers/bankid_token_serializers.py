from rest_framework import serializers
from subscriptions.models.subscription_masters import PaymentTransaction, BankIDUsageTracking


class BankIDTokenPurchaseSerializer(serializers.Serializer):
    """
    Serializer for BankID token purchase requests
    """
    quantity = serializers.IntegerField(
        min_value=1,
        help_text="Number of BankID tokens to purchase (must be in multiples of 100)"
    )
    
    def validate_quantity(self, value):
        """
        Validate that quantity is in multiples of 100
        """
        if value % 100 != 0:
            raise serializers.ValidationError(
                "Quantity must be in multiples of 100. For example: 100, 200, 300, etc."
            )
        return value


class BankIDTokenBalanceSerializer(serializers.Serializer):
    """
    Serializer for BankID token balance information
    """
    tokens_remaining = serializers.IntegerField()
    tokens_used = serializers.IntegerField()
    tokens_purchased = serializers.IntegerField()
    price_per_token = serializers.DecimalField(max_digits=10, decimal_places=2)
    currency = serializers.CharField(max_length=3)
    auto_topup_enabled = serializers.BooleanField()
    auto_topup_threshold = serializers.IntegerField()
    auto_topup_quantity = serializers.IntegerField()


class BankIDTokenPurchaseResponseSerializer(serializers.Serializer):
    """
    Serializer for BankID token purchase response
    """
    transaction_id = serializers.UUIDField()
    payment_intent_id = serializers.CharField()
    client_secret = serializers.CharField()
    total_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    currency = serializers.CharField(max_length=3)
    quantity = serializers.IntegerField()
    price_per_token = serializers.DecimalField(max_digits=10, decimal_places=2)


class BankIDTokenTransactionSerializer(serializers.ModelSerializer):
    """
    Serializer for BankID token transaction history
    """
    transaction_type_display = serializers.CharField(source='get_transaction_type_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    initiated_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = PaymentTransaction
        fields = [
            'id', 'transaction_type', 'transaction_type_display',
            'bankid_token_quantity', 'bankid_token_price_per_unit',
            'amount', 'currency', 'payment_status', 'payment_status_display',
            'notes', 'payment_date', 'initiated_by', 'initiated_by_name'
        ]
        read_only_fields = ['id', 'payment_date']
    
    def get_initiated_by_name(self, obj):
        if obj.initiated_by:
            return f"{obj.initiated_by.first_name} {obj.initiated_by.last_name}"
        return None


class BankIDUsageTrackingSerializer(serializers.ModelSerializer):
    """
    Serializer for BankID usage tracking records
    """
    usage_status_display = serializers.CharField(source='get_usage_status_display', read_only=True)
    initiated_by_name = serializers.SerializerMethodField()
    
    class Meta:
        model = BankIDUsageTracking
        fields = [
            'id', 'document_ref_id', 'signer_ref_id', 'document_name',
            'signer_name', 'signer_email', 'proposal_ref_id', 'proposal_name',
            'usage_status', 'usage_status_display', 'tokens_consumed',
            'initiated_at', 'completed_at', 'ip_address', 'user_agent',
            'session_id', 'initiated_by', 'initiated_by_name'
        ]
        read_only_fields = ['id', 'initiated_at']
    
    def get_initiated_by_name(self, obj):
        if obj.initiated_by:
            return f"{obj.initiated_by.first_name} {obj.initiated_by.last_name}"
        return None


class BankIDAutoTopupSettingsSerializer(serializers.Serializer):
    """
    Serializer for BankID auto-topup settings
    """
    enabled = serializers.BooleanField(required=False, allow_null=True)
    threshold = serializers.IntegerField(min_value=1, required=False, allow_null=True)
    quantity = serializers.IntegerField(min_value=100, required=False, allow_null=True)
    
    def validate(self, data):
        """
        Validate the auto-topup settings based on business rules
        """
        enabled = data.get('enabled')
        threshold = data.get('threshold')
        quantity = data.get('quantity')
        
        # If enabled is False, threshold and quantity should not be updated
        if enabled is False:
            if threshold is not None or quantity is not None:
                raise serializers.ValidationError(
                    "When auto-topup is disabled, threshold and quantity cannot be updated"
                )
        
        # If enabled is True, validate threshold and quantity if provided
        if enabled is True:
            if threshold is not None and threshold < 1:
                raise serializers.ValidationError(
                    "Threshold must be at least 1"
                )
            
            if quantity is not None:
                if quantity < 100:
                    raise serializers.ValidationError(
                        "Quantity must be at least 100"
                    )
                if quantity % 100 != 0:
                    raise serializers.ValidationError(
                        "Auto-topup quantity must be in multiples of 100"
                    )
        
        return data
    
    def validate_quantity(self, value):
        """
        Validate that quantity is in multiples of 100
        """
        if value is not None and value % 100 != 0:
            raise serializers.ValidationError(
                "Auto-topup quantity must be in multiples of 100"
            )
        return value
