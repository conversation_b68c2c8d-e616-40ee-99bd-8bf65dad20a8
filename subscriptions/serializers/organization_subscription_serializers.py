from rest_framework import serializers
from subscriptions.models.subscription_masters import (
    OrganizationSubscription,
    OrganizationSubscriptionHistory,
    OrganizationSubscriptionUsage,
    BankIDTokenTransaction,
    SubscriptionMasterPlan
)
from accounts.models.organisation import Organization


class OrganizationSerializer(serializers.ModelSerializer):
    """Serializer for Organization model"""
    
    class Meta:
        model = Organization
        fields = [
            'id', 'name', 'number', 'tax_id', 'tax_id_type',
            'tax_id_verification_status', 'created_at', 'updated_at',
            'is_active', 'user_added_count', 'total_active_user',
            'add_on_user_limit'
        ]
        read_only_fields = ['created_at', 'updated_at']


class SubscriptionMasterPlanSerializer(serializers.ModelSerializer):
    """Serializer for SubscriptionMasterPlan model"""
    plan_category_display = serializers.CharField(source='get_plan_category_display', read_only=True)
    plan_type_display = serializers.CharField(source='get_plan_type_display', read_only=True)

    class Meta:
        model = SubscriptionMasterPlan
        fields = '__all__'


class BankIDTokenTransactionSerializer(serializers.ModelSerializer):
    """Serializer for BankIDTokenTransaction model"""
    transaction_type_display = serializers.CharField(source='get_transaction_type_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    initiated_by_name = serializers.SerializerMethodField()

    class Meta:
        model = BankIDTokenTransaction
        fields = [
            'id', 'transaction_type', 'transaction_type_display',
            'quantity', 'price_per_token', 'total_amount', 'currency',
            'payment_status', 'payment_status_display',
            'stripe_payment_intent_id', 'stripe_invoice_id',
            'description', 'created_at', 'updated_at',
            'initiated_by', 'initiated_by_name'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_initiated_by_name(self, obj):
        if obj.initiated_by:
            return obj.initiated_by.get_full_name()
        return None


class OrganizationSubscriptionUsageSerializer(serializers.ModelSerializer):
    """Serializer for OrganizationSubscriptionUsage model"""
    used_by_details = serializers.SerializerMethodField()

    class Meta:
        model = OrganizationSubscriptionUsage
        fields = [
            'id', 'start_date', 'end_date', 'esigns_used', 'templates_used',
            'bankid_sings_used', 'organisations_used', 'users_used',
            'templates_token_used', 'default_contract_used', 'storage_used',
            'agreements_used', 'bankid_tokens_used', 'bankid_tokens_consumed',
            'used_by', 'used_by_details', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_used_by_details(self, obj):
        if obj.used_by:
            return {
                'email': obj.used_by.get_decrypted_email(),
                'reference_id': obj.used_by.reference_id,
                'first_name': obj.used_by.first_name,
                'last_name': obj.used_by.last_name
            }
        return None


class OrganizationSubscriptionHistorySerializer(serializers.ModelSerializer):
    """Serializer for OrganizationSubscriptionHistory model"""
    previous_plan = SubscriptionMasterPlanSerializer(read_only=True)
    new_plan = SubscriptionMasterPlanSerializer(read_only=True)
    change_type_display = serializers.CharField(source='get_change_type_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    created_by_name = serializers.SerializerMethodField()

    class Meta:
        model = OrganizationSubscriptionHistory
        fields = [
            'id', 'previous_plan', 'new_plan', 'change_type', 'change_type_display',
            'change_date', 'start_date', 'end_date', 'notes', 'payment_status',
            'payment_status_display', 'esigns_used', 'templates_used',
            'bankid_sings_used', 'organisations_used', 'users_used',
            'templates_token_used', 'default_contract_used', 'storage_used',
            'agreements_used', 'bankid_tokens_used', 'bankid_tokens_purchased',
            'bankid_tokens_remaining', 'created_by', 'created_by_name'
        ]
        read_only_fields = ['change_date']

    def get_created_by_name(self, obj):
        if obj.created_by:
            return obj.created_by.get_full_name()
        return None


class OrganizationSubscriptionSerializer(serializers.ModelSerializer):
    """Main serializer for OrganizationSubscription model"""
    organization = OrganizationSerializer(read_only=True)
    plan = SubscriptionMasterPlanSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    renewal_status_display = serializers.CharField(source='get_renewal_status_display', read_only=True)
    usage_stats = serializers.SerializerMethodField()
    bankid_token_balance = serializers.SerializerMethodField()

    class Meta:
        model = OrganizationSubscription
        fields = [
            'id', 'organization', 'plan', 'status', 'status_display',
            'reminder_sent', 'trial_ending_reminder_sent', 'add_on_user_limit',
            'start_date', 'end_date', 'cancelled_date', 'cancelled_reason',
            'cancelled_by', 'renewal_date', 'renewal_status', 'renewal_status_display',
            'renewal_date_next', 'renewed_by', 'stripe_subscription_id',
            'stripe_customer_id', 'usage_stats', 'bankid_token_balance',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def get_usage_stats(self, obj):
        """Get usage statistics for the subscription"""
        return obj.get_latest_usage_stats()

    def get_bankid_token_balance(self, obj):
        """Get BankID token balance information"""
        return {
            'tokens_remaining': obj.bankid_tokens_remaining,
            'tokens_used': obj.bankid_tokens_used,
            'tokens_purchased': obj.bankid_tokens_purchased,
            'price_per_token': float(obj.bankid_token_price),
            'currency': obj.bankid_token_currency,
            'auto_topup_enabled': obj.bankid_auto_topup_enabled,
            'auto_topup_threshold': obj.bankid_auto_topup_threshold,
            'auto_topup_quantity': obj.bankid_auto_topup_quantity,
            'last_topup_date': obj.bankid_last_topup_date
        }


class OrganizationSubscriptionCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating OrganizationSubscription"""
    organization_id = serializers.UUIDField(write_only=True)
    plan_id = serializers.UUIDField(write_only=True)

    class Meta:
        model = OrganizationSubscription
        fields = [
            'organization_id', 'plan_id', 'status', 'add_on_user_limit',
            'start_date', 'end_date', 'stripe_subscription_id', 'stripe_customer_id'
        ]

    def create(self, validated_data):
        organization_id = validated_data.pop('organization_id')
        plan_id = validated_data.pop('plan_id')
        
        try:
            organization = Organization.objects.get(id=organization_id)
            plan = SubscriptionMasterPlan.objects.get(id=plan_id)
        except (Organization.DoesNotExist, SubscriptionMasterPlan.DoesNotExist) as e:
            raise serializers.ValidationError(f"Invalid organization or plan ID: {str(e)}")
        
        validated_data['organization'] = organization
        validated_data['plan'] = plan
        
        return super().create(validated_data)


class BankIDTokenPurchaseSerializer(serializers.Serializer):
    """Serializer for BankID token purchase requests"""
    quantity = serializers.IntegerField(min_value=1, max_value=10000)
    auto_confirm = serializers.BooleanField(default=False)

    def validate_quantity(self, value):
        if value <= 0:
            raise serializers.ValidationError("Quantity must be greater than 0")
        return value


class BankIDTokenSettingsSerializer(serializers.Serializer):
    """Serializer for updating BankID token settings"""
    auto_topup_enabled = serializers.BooleanField(required=False)
    auto_topup_threshold = serializers.IntegerField(min_value=1, max_value=1000, required=False)
    auto_topup_quantity = serializers.IntegerField(min_value=1, max_value=10000, required=False)

    def validate_auto_topup_threshold(self, value):
        if value <= 0:
            raise serializers.ValidationError("Auto-topup threshold must be greater than 0")
        return value

    def validate_auto_topup_quantity(self, value):
        if value <= 0:
            raise serializers.ValidationError("Auto-topup quantity must be greater than 0")
        return value


class OrganizationSubscriptionUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating OrganizationSubscription"""
    bankid_token_settings = BankIDTokenSettingsSerializer(required=False)

    class Meta:
        model = OrganizationSubscription
        fields = [
            'status', 'add_on_user_limit', 'start_date', 'end_date',
            'cancelled_date', 'cancelled_reason', 'renewal_date',
            'renewal_status', 'renewal_date_next', 'bankid_token_settings'
        ]

    def update(self, instance, validated_data):
        bankid_settings = validated_data.pop('bankid_token_settings', None)
        
        # Update main subscription fields
        instance = super().update(instance, validated_data)
        
        # Update BankID token settings if provided
        if bankid_settings:
            from subscriptions.services.bankid_token_service import BankIDTokenService
            service = BankIDTokenService()
            service.update_auto_topup_settings(
                organization_subscription=instance,
                enabled=bankid_settings.get('auto_topup_enabled'),
                threshold=bankid_settings.get('auto_topup_threshold'),
                quantity=bankid_settings.get('auto_topup_quantity')
            )
        
        return instance
