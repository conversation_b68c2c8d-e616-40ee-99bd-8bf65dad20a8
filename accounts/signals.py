from django.db.models.signals import post_save
from django.dispatch import receiver
from accounts.models.users import User
from documents.models import UserDocumentDirectory
import logging

logger = logging.getLogger('app')


@receiver(post_save, sender=User)
def create_user_default_directory(sender, instance, created, **kwargs):
    """Create default 'Agreements' directory when a new user is created"""
    if created:
        try:
            directory = UserDocumentDirectory.objects.create(
                organization=getattr(instance, 'organization', None),
                title='Agreements',
                is_default=True,
                is_active=True,
                created_by=instance
            )
            logger.info(
                "Default Agreements directory created for new user",
                extra={
                    'user_id': str(instance.id),
                    'directory_id': str(directory.id)
                }
            )
        except Exception as e:
            logger.error(
                "Error creating default directory for user",
                extra={
                    'user_id': str(instance.id),
                    'error': str(e)
                },
                exc_info=True
            )
