import logging
from django.db import models
from django.utils import timezone

logger = logging.getLogger('app')


class OneTimePassword(models.Model):
    email = models.CharField(max_length=100, null=True, blank=True)
    phone_number = models.CharField(max_length=100, null=True, blank=True)
    otp_code = models.CharField(max_length=10)
    expiration_time = models.DateTimeField()
    is_verified = models.BooleanField(default=False)
    is_link_verified_email = models.BooleanField(default=False)
    is_link_verified_phone = models.BooleanField(default=False)
    is_phone_verified = models.BooleanField(default=False)
    is_email_verified = models.BooleanField(default=False)
    is_download = models.BooleanField(default=False)
    is_sent = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        try:
            is_new = not self.pk
            self.expiration_time = timezone.now() + timezone.timedelta(minutes=10)

            super().save(*args, **kwargs)

            if is_new:
                logger.info(
                    "New OTP created",
                    extra={
                        'email': self.email,
                        'phone': self.phone_number,
                        'expiry': self.expiration_time
                    }
                )
            else:
                logger.info(
                    "OTP updated",
                    extra={
                        'email': self.email,
                        'phone': self.phone_number,
                        'is_verified': self.is_verified
                    }
                )
        except Exception as e:
            logger.error(
                "Error saving OTP",
                extra={
                    'email': self.email,
                    'phone': self.phone_number,
                    'error': str(e)
                },
                exc_info=True
            )
            raise
