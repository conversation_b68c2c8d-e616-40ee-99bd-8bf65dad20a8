import uuid
import logging

from django.utils import timezone
from cryptography.fernet import Fernet
from django.conf import settings
from django.db import models
from accounts.utils.stripe_utils import get_or_create_customer

from accounts.services.jwt_authentication.authentication import fernet_decrypt

cipher_suite = Fernet(settings.ENCRYPTION_KEY)
logger = logging.getLogger('app')


class Organization(models.Model):
    """Model to represent organizations."""

    # Tax ID verification status choices
    TAX_ID_VERIFICATION_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('verified', 'Verified'),
        ('unverified', 'Unverified'),
    ]

    # Complete Stripe tax ID type choices
    TAX_ID_TYPE_CHOICES = [
        ('ad_nrt', 'Andorra NRT'),
        ('ae_trn', 'UAE TRN'),
        ('al_tin', 'Albania TIN'),
        ('am_tin', 'Armenia TIN'),
        ('ao_tin', 'Angola TIN'),
        ('ar_cuit', 'Argentina CUIT'),
        ('au_abn', 'Australia ABN'),
        ('au_arn', 'Australia ARN'),
        ('aw_tin', 'Aruba TIN'),
        ('az_tin', 'Azerbaijan TIN'),
        ('ba_tin', 'Bosnia and Herzegovina TIN'),
        ('bb_tin', 'Barbados TIN'),
        ('bd_bin', 'Bangladesh BIN'),
        ('bf_ifu', 'Burkina Faso IFU'),
        ('bg_uic', 'Bulgaria UIC'),
        ('bh_vat', 'Bahrain VAT'),
        ('bj_ifu', 'Benin IFU'),
        ('bo_tin', 'Bolivia TIN'),
        ('br_cnpj', 'Brazil CNPJ'),
        ('br_cpf', 'Brazil CPF'),
        ('bs_tin', 'Bahamas TIN'),
        ('by_tin', 'Belarus TIN'),
        ('ca_bn', 'Canada BN'),
        ('ca_gst_hst', 'Canada GST/HST'),
        ('ca_pst_bc', 'Canada PST (BC)'),
        ('ca_pst_mb', 'Canada PST (MB)'),
        ('ca_pst_sk', 'Canada PST (SK)'),
        ('ca_qst', 'Canada QST'),
        ('cd_nif', 'DR Congo NIF'),
        ('ch_uid', 'Switzerland UID'),
        ('ch_vat', 'Switzerland VAT'),
        ('cl_tin', 'Chile TIN'),
        ('cm_niu', 'Cameroon NIU'),
        ('cn_tin', 'China TIN'),
        ('co_nit', 'Colombia NIT'),
        ('cr_tin', 'Costa Rica TIN'),
        ('cv_nif', 'Cape Verde NIF'),
        ('de_stn', 'Germany STN'),
        ('do_rcn', 'Dominican Republic RCN'),
        ('ec_ruc', 'Ecuador RUC'),
        ('eg_tin', 'Egypt TIN'),
        ('es_cif', 'Spain CIF'),
        ('et_tin', 'Ethiopia TIN'),
        ('eu_oss_vat', 'EU OSS VAT'),
        ('eu_vat', 'EU VAT'),
        ('gb_vat', 'UK VAT'),
        ('ge_vat', 'Georgia VAT'),
        ('gn_nif', 'Guinea NIF'),
        ('hk_br', 'Hong Kong BR'),
        ('hr_oib', 'Croatia OIB'),
        ('hu_tin', 'Hungary TIN'),
        ('id_npwp', 'Indonesia NPWP'),
        ('il_vat', 'Israel VAT'),
        ('in_gst', 'India GST'),
        ('is_vat', 'Iceland VAT'),
        ('jp_cn', 'Japan CN'),
        ('jp_rn', 'Japan RN'),
        ('jp_trn', 'Japan TRN'),
        ('ke_pin', 'Kenya PIN'),
        ('kg_tin', 'Kyrgyzstan TIN'),
        ('kh_tin', 'Cambodia TIN'),
        ('kr_brn', 'South Korea BRN'),
        ('kz_bin', 'Kazakhstan BIN'),
        ('la_tin', 'Laos TIN'),
        ('li_uid', 'Liechtenstein UID'),
        ('li_vat', 'Liechtenstein VAT'),
        ('ma_vat', 'Morocco VAT'),
        ('md_vat', 'Moldova VAT'),
        ('me_pib', 'Montenegro PIB'),
        ('mk_vat', 'North Macedonia VAT'),
        ('mr_nif', 'Mauritania NIF'),
        ('mx_rfc', 'Mexico RFC'),
        ('my_frp', 'Malaysia FRP'),
        ('my_itn', 'Malaysia ITN'),
        ('my_sst', 'Malaysia SST'),
        ('ng_tin', 'Nigeria TIN'),
        ('no_vat', 'Norway VAT'),
        ('no_voec', 'Norway VOEC'),
        ('np_pan', 'Nepal PAN'),
        ('nz_gst', 'New Zealand GST'),
        ('om_vat', 'Oman VAT'),
        ('pe_ruc', 'Peru RUC'),
        ('ph_tin', 'Philippines TIN'),
        ('ro_tin', 'Romania TIN'),
        ('rs_pib', 'Serbia PIB'),
        ('ru_inn', 'Russia INN'),
        ('ru_kpp', 'Russia KPP'),
        ('sa_vat', 'Saudi Arabia VAT'),
        ('sg_gst', 'Singapore GST'),
        ('sg_uen', 'Singapore UEN'),
        ('si_tin', 'Slovenia TIN'),
        ('sn_ninea', 'Senegal NINEA'),
        ('sr_fin', 'Suriname FIN'),
        ('sv_nit', 'El Salvador NIT'),
        ('th_vat', 'Thailand VAT'),
        ('tj_tin', 'Tajikistan TIN'),
        ('tr_tin', 'Turkey TIN'),
        ('tw_vat', 'Taiwan VAT'),
        ('tz_vat', 'Tanzania VAT'),
        ('ua_vat', 'Ukraine VAT'),
        ('ug_tin', 'Uganda TIN'),
        ('us_ein', 'US EIN'),
        ('uy_ruc', 'Uruguay RUC'),
        ('uz_tin', 'Uzbekistan TIN'),
        ('uz_vat', 'Uzbekistan VAT'),
        ('ve_rif', 'Venezuela RIF'),
        ('vn_tin', 'Vietnam TIN'),
        ('za_vat', 'South Africa VAT'),
        ('zm_tin', 'Zambia TIN'),
        ('zw_tin', 'Zimbabwe TIN'),
    ]

    # Phone number country code to tax ID type mapping for automatic detection
    PHONE_TO_TAX_ID_MAPPING = {
        '+91': 'in_gst',      # India
        '+1': 'us_ein',       # US/Canada (default to US EIN)
        '+44': 'gb_vat',      # UK
        '+61': 'au_abn',      # Australia
        '+64': 'nz_gst',      # New Zealand
        '+65': 'sg_gst',      # Singapore
        '+60': 'my_sst',      # Malaysia
        '+66': 'th_vat',      # Thailand
        '+49': 'de_stn',      # Germany
        '+33': 'eu_vat',      # France
        '+39': 'eu_vat',      # Italy
        '+34': 'es_cif',      # Spain
        '+31': 'eu_vat',      # Netherlands
        '+46': 'eu_vat',      # Sweden
        '+47': 'no_vat',      # Norway
        '+41': 'ch_vat',      # Switzerland
        '+81': 'jp_trn',      # Japan
        '+82': 'kr_brn',      # South Korea
        '+86': 'cn_tin',      # China
        '+55': 'br_cnpj',     # Brazil
        '+52': 'mx_rfc',      # Mexico
        '+54': 'ar_cuit',     # Argentina
        '+56': 'cl_tin',      # Chile
        '+51': 'pe_ruc',      # Peru
        '+57': 'co_nit',      # Colombia
        '+27': 'za_vat',      # South Africa
        '+234': 'ng_tin',     # Nigeria
        '+254': 'ke_pin',     # Kenya
        '+20': 'eg_tin',      # Egypt
        '+966': 'sa_vat',     # Saudi Arabia
        '+971': 'ae_trn',     # UAE
        '+972': 'il_vat',     # Israel
        '+90': 'tr_tin',      # Turkey
        '+7': 'ru_inn',       # Russia
        '+380': 'ua_vat',     # Ukraine
        '+48': 'eu_vat',      # Poland
        '+420': 'eu_vat',     # Czech Republic
        '+36': 'hu_tin',      # Hungary
        '+40': 'ro_tin',      # Romania
        '+359': 'bg_uic',     # Bulgaria
        '+385': 'hr_oib',     # Croatia
        '+386': 'si_tin',     # Slovenia
        '+421': 'eu_vat',     # Slovakia
        '+370': 'eu_vat',     # Lithuania
        '+371': 'eu_vat',     # Latvia
        '+372': 'eu_vat',     # Estonia
        '+358': 'eu_vat',     # Finland
        '+45': 'eu_vat',      # Denmark
        '+353': 'eu_vat',     # Ireland
        '+32': 'eu_vat',      # Belgium
        '+43': 'eu_vat',      # Austria
        '+352': 'eu_vat',     # Luxembourg
        '+356': 'eu_vat',     # Malta
        '+357': 'eu_vat',     # Cyprus
        '+30': 'eu_vat',      # Greece
        '+351': 'eu_vat',     # Portugal
    }

    id = models.UUIDField(
        default=uuid.uuid4, unique=True, primary_key=True, editable=False
    )
    name = models.CharField(max_length=255, null=True, blank=True, unique=True)
    number = models.CharField(max_length=255, null=True, blank=True, unique=True)
    tax_id = models.CharField(max_length=255, null=True, blank=True)
    
    # New fields for Stripe tax ID management
    tax_id_type = models.CharField(
        max_length=20,
        choices=TAX_ID_TYPE_CHOICES,
        null=True, 
        blank=True,
        help_text="Type of tax ID (e.g., eu_vat, in_gst, gb_vat)"
    )
    tax_id_verification_status = models.CharField(
        max_length=20,
        choices=TAX_ID_VERIFICATION_STATUS_CHOICES,
        default='pending',
        help_text="Verification status from Stripe (pending, verified, unverified)"
    )
    tax_id_object = models.CharField(max_length=255, null=True, blank=True)
    tax_id_verification_updated_at = models.DateTimeField(
        null=True, 
        blank=True,
        help_text="When the tax ID verification status was last updated"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    reference_id = models.CharField(
        max_length=255, editable=False, unique=True, null=True, blank=True
    )
    is_owner_included = models.BooleanField(default=True)
    # Reference to the user (assuming custom User model)
    is_primary = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)

    # BankID Token-based pricing fields
    bankid_tokens_remaining = models.IntegerField(default=0, help_text="Remaining BankID signature tokens")
    bankid_tokens_purchased = models.IntegerField(default=0, help_text="Total BankID tokens purchased")
    bankid_tokens_used = models.IntegerField(default=0, help_text="Total BankID tokens used")
    bankid_auto_topup_enabled = models.BooleanField(default=True, help_text="Enable automatic token top-up when tokens are low")
    bankid_auto_topup_threshold = models.IntegerField(default=10, help_text="Minimum tokens before auto top-up triggers")
    bankid_auto_topup_quantity = models.IntegerField(default=100, help_text="Number of tokens to purchase during auto top-up")
    bankid_last_topup_date = models.DateTimeField(null=True, blank=True, help_text="Last automatic token top-up date")
    
    deleted_at = models.DateTimeField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    show_on_invoice = models.BooleanField(default=False)
    created_by = models.CharField(max_length=255, blank=True, null=True)
    creator_user = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        related_name='created_organizations',
        null=True,
        blank=True,
    )

    org_domain = models.CharField(max_length=255, null=True, blank=True)
    user_added_count = models.PositiveIntegerField(default=0)
    total_active_user = models.PositiveIntegerField(default=0)
    add_on_user_limit = models.PositiveBigIntegerField(default=0) # to track the add-on limit for an organization.

    def get_decrypted_name(self):
        return fernet_decrypt(self.name.encode()) if self.name else None

    def get_decrypted_number(self):
        return fernet_decrypt(self.number.encode()) if self.number else None

    def get_decrypted_tax_id(self):
        return fernet_decrypt(self.tax_id.encode()) if self.tax_id else None

    def get_decrypted_reference_id(self):
        return fernet_decrypt(self.reference_id.encode()) if self.reference_id else None

    def update_tax_id_verification_status(self, status, tax_id_type=None):
        """
        Update tax ID verification status and related fields
        
        Args:
            status (str): New verification status
            tax_id_type (str, optional): Tax ID type if provided
        """
        self.tax_id_verification_status = status
        self.tax_id_verification_updated_at = timezone.now()
        
        if tax_id_type:
            self.tax_id_type = tax_id_type
            
        self.save(update_fields=[
            'tax_id_verification_status', 
            'tax_id_verification_updated_at',
            'tax_id_type'
        ])

    def get_tax_id_display_name(self):
        """Get human-readable tax ID type name"""
        if self.tax_id_type:
            return dict(self.TAX_ID_TYPE_CHOICES).get(self.tax_id_type, self.tax_id_type)
        return "Tax ID"

    def is_tax_id_verified(self):
        """Check if tax ID is verified"""
        return self.tax_id_verification_status == 'verified'

    def is_tax_id_pending(self):
        """Check if tax ID verification is pending"""
        return self.tax_id_verification_status == 'pending'

    def is_tax_id_unverified(self):
        """Check if tax ID is unverified"""
        return self.tax_id_verification_status == 'unverified'

    def auto_detect_tax_id_type(self, phone_number):
        """
        Automatically detect tax ID type from phone number
        
        Args:
            phone_number (str): Phone number with country code (e.g., +91XXXXXXXXXX)
            
        Returns:
            str: Tax ID type or None if not detected
        """
        if not phone_number:
            logger.warning("No phone number provided for tax ID type detection")
            return None
            
        # Ensure phone number starts with +
        if not phone_number.startswith('+'):
            logger.warning(f"Phone number {phone_number} doesn't start with +, cannot detect country code")
            return None
            
        # Validate phone number format (should be + followed by 1-3 digits for country code + 10 digits)
        import re
        if not re.match(r"^\+\d{1,3}\d{10}$", phone_number):
            logger.warning(f"Phone number {phone_number} doesn't match expected format (+***********)")
            return None
            
        logger.info(f"Attempting to detect tax ID type from phone number: {phone_number}")
        
        # Check for exact matches in phone country codes
        for code, tax_id_type in self.PHONE_TO_TAX_ID_MAPPING.items():
            if phone_number.startswith(code):
                logger.info(f"Found matching country code {code} -> tax ID type: {tax_id_type}")
                return tax_id_type
                
        logger.warning(f"No matching country code found for phone number: {phone_number}")
        return None

    def set_tax_id_type_from_phone(self, phone_number):
        """
        Set tax ID type automatically from phone number
        
        Args:
            phone_number (str): Phone number for auto-detection
            
        Returns:
            str: Detected tax ID type or None
        """
        tax_id_type = self.auto_detect_tax_id_type(phone_number)
        if tax_id_type:
            self.tax_id_type = tax_id_type
            return tax_id_type
        return None

    def create_or_update_stripe_tax_id(self, tax_id_value, phone_number=None):
        """
        Create or update tax ID in Stripe, handling old tax ID removal automatically
        
        Args:
            tax_id_value (str): The tax ID value to create/update
            phone_number (str, optional): Phone number for auto-detection if tax_id_type not set
            
        Returns:
            dict: Result with success status, stripe_tax_id, and message
        """
        import stripe
        from django.conf import settings
        from cryptography.fernet import Fernet

        try:
            stripe.api_key = settings.STRIPE_SECRET_KEY
            cipher_suite = Fernet(settings.ENCRYPTION_KEY)
            
            # Get organization owner to access stripe_customer_id
            owner = self.get_organization_owner()

            # TODO: If stripe_customer_id is not set, create a new strip customer and set the stripe_customer_id to the new customer id and proceed with the changes.
            if not owner or not hasattr(owner, 'stripe_customer_id') or not owner.stripe_customer_id:
                stripe_customer = get_or_create_customer(owner)
                owner.stripe_customer_id = stripe_customer.id
                owner.save()
            
            customer_id = owner.stripe_customer_id
            
            # Auto-detect tax ID type if not set
            if not self.tax_id_type and phone_number:
                logger.info(f"Auto-detecting tax ID type from phone number: {phone_number}")
                self.tax_id_type = self.set_tax_id_type_from_phone(phone_number)
                logger.info(f"Detected tax ID type: {self.tax_id_type}")
            elif not self.tax_id_type:
                logger.warning("No tax_id_type set and no phone number provided for auto-detection")

            
            if not self.tax_id_type:
                # Fallback to a default tax ID type if detection fails
                logger.warning("Tax ID type not detected from phone number, using default 'eu_vat'")
                self.tax_id_type = 'eu_vat'
            
            # Remove old tax ID if exists
            if self.tax_id_object:
                try:
                    # Delete old tax ID from Stripe
                    stripe.Customer.delete_tax_id(
                        customer_id,
                        self.tax_id_object
                    )
                    logger.info(f"Deleted old tax ID {self.tax_id_object} for customer {customer_id}")
                except stripe.error.InvalidRequestError:
                    # Tax ID might already be deleted
                    logger.warning(f"Tax ID {self.tax_id_object} not found in Stripe for deletion")
                except Exception as e:
                    logger.error(f"Error deleting old tax ID: {str(e)}")
                
                # Clear old tax ID reference
                self.tax_id_object = None
            
            # Create new tax ID in Stripe
            new_tax_id = stripe.Customer.create_tax_id(
                customer_id,
                type=self.tax_id_type,
                value=tax_id_value
            )

            
            # Update organization with new tax ID details
            # Encrypt the tax_id value before saving
            encrypted_tax_id = cipher_suite.encrypt(tax_id_value.encode()).decode()
            self.tax_id = encrypted_tax_id
            self.tax_id_object = new_tax_id.id
            self.tax_id_verification_status = 'pending'
            self.tax_id_verification_updated_at = timezone.now()
            
            # Save the organization
            self.save(update_fields=[
                'tax_id', 
                'tax_id_object', 
                'tax_id_verification_status',
                'tax_id_verification_updated_at'
            ])
            
            logger.info(f"Created new tax ID {new_tax_id.id} for customer {customer_id}")
            
            return {
                'success': True,
                'stripe_tax_id': new_tax_id.id,
                'message': f'Tax ID created successfully. Status: {new_tax_id.verification.status}',
                'verification_status': new_tax_id.verification.status
            }
            
        except stripe.error.StripeError as e:
            error_msg = f"Stripe error: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg
            }
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                'success': False,
                'message': error_msg
            }

    def remove_stripe_tax_id(self):
        """
        Remove tax ID from Stripe and clear local references
        
        Returns:
            dict: Result with success status and message
        """
        import stripe
        from django.conf import settings
        
        if not self.tax_id_object:
            return {
                'success': False,
                'message': 'No tax ID object to remove'
            }
        
        try:
            stripe.api_key = settings.STRIPE_SECRET_KEY
            
            owner = self.get_organization_owner()
            if not owner or not hasattr(owner, 'stripe_customer_id') or not owner.stripe_customer_id:
                return {
                    'success': False,
                    'message': 'Organization owner not found or no Stripe customer ID available'
                }
            
            customer_id = owner.stripe_customer_id
            
            # Delete from Stripe
            stripe.Customer.delete_tax_id(
                customer_id,
                self.tax_id_object
            )
            
            # Clear local references
            self.tax_id = None
            self.tax_id_object = None
            self.tax_id_type = None
            self.tax_id_verification_status = 'pending'
            self.tax_id_verification_updated_at = timezone.now()
            
            self.save(update_fields=[
                'tax_id', 
                'tax_id_object', 
                'tax_id_type',
                'tax_id_verification_status',
                'tax_id_verification_updated_at'
            ])
            
            logger.info(f"Removed tax ID {self.tax_id_object} for customer {customer_id}")
            
            return {
                'success': True,
                'message': 'Tax ID removed successfully'
            }
            
        except stripe.error.InvalidRequestError:
            # Tax ID might already be deleted
            self.tax_id_object = None
            self.save(update_fields=['tax_id_object'])
            return {
                'success': True,
                'message': 'Tax ID was already removed from Stripe'
            }
        except Exception as e:
            error_msg = f"Error removing tax ID: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return {
                'success': False,
                'message': error_msg
            }

    def get_tax_id_requirements(self, phone_number=None):
        """
        Get tax ID format requirements based on phone number or existing tax_id_type
        
        Args:
            phone_number (str, optional): Phone number to detect requirements
            
        Returns:
            dict: Requirements including format, example, and description
        """
        # First try to use existing tax_id_type
        if self.tax_id_type:
            return self._get_requirements_by_tax_id_type(self.tax_id_type)
            
        # If no tax_id_type, try to detect from phone number
        if phone_number:
            tax_id_type = self.auto_detect_tax_id_type(phone_number)
            if tax_id_type:
                return self._get_requirements_by_tax_id_type(tax_id_type)
                
        return None

    def check_tax_id_details_match(self, stripe_tax_id_dict):
        """
        Check if local tax ID details match with Stripe tax ID data
        
        Args:
            stripe_tax_id_dict (dict): Stripe tax ID object
            
        Returns:
            dict: Comparison result with details about what matches and what differs
        """
        stripe_tax_id_type = stripe_tax_id_dict.get('type')
        stripe_tax_id_value = stripe_tax_id_dict.get('value')
        stripe_verification_status = stripe_tax_id_dict.get('verification', {}).get('status', 'pending')
        
        local_tax_id_value = self.get_decrypted_tax_id()
        
        comparison = {
            'tax_id_type_match': self.tax_id_type == stripe_tax_id_type,
            'tax_id_value_match': local_tax_id_value == stripe_tax_id_value,
            'verification_status_match': self.tax_id_verification_status == stripe_verification_status,
            'overall_match': True,
            'differences': []
        }
        
        # Check each field and record differences
        if not comparison['tax_id_type_match']:
            comparison['differences'].append({
                'field': 'tax_id_type',
                'local': self.tax_id_type,
                'stripe': stripe_tax_id_type
            })
            comparison['overall_match'] = False
            
        if not comparison['tax_id_value_match']:
            comparison['differences'].append({
                'field': 'tax_id_value',
                'local': local_tax_id_value,
                'stripe': stripe_tax_id_value
            })
            comparison['overall_match'] = False
            
        if not comparison['verification_status_match']:
            comparison['differences'].append({
                'field': 'verification_status',
                'local': self.tax_id_verification_status,
                'stripe': stripe_verification_status
            })
            comparison['overall_match'] = False
        
        return comparison

    def update_tax_id_from_stripe(self, stripe_tax_id_dict, prioritize_stripe=True):
        """
        Update local tax ID details from Stripe data
        
        Args:
            stripe_tax_id_dict (dict): Stripe tax ID object
            prioritize_stripe (bool): If True, always use Stripe values over local values
            
        Returns:
            dict: Update result with details about what was updated
        """
        from django.conf import settings
        from cryptography.fernet import Fernet
        
        cipher_suite = Fernet(settings.ENCRYPTION_KEY)
        
        if prioritize_stripe:
            # Always prioritize Stripe values
            needs_update = False
            update_fields = []
            
            stripe_tax_id_type = stripe_tax_id_dict.get('type')
            stripe_tax_id_value = stripe_tax_id_dict.get('value')
            stripe_verification_status = stripe_tax_id_dict.get('verification', {}).get('status', 'pending')
            
            # Check and update tax_id_type
            if self.tax_id_type != stripe_tax_id_type:
                self.tax_id_type = stripe_tax_id_type
                needs_update = True
                update_fields.append('tax_id_type')
            
            # Check and update tax_id_value
            if self.get_decrypted_tax_id() != stripe_tax_id_value:
                # Encrypt the tax_id value before saving
                encrypted_tax_id = cipher_suite.encrypt(stripe_tax_id_value.encode()).decode()
                self.tax_id = encrypted_tax_id
                needs_update = True
                update_fields.append('tax_id')
            
            # Check and update verification status
            if self.tax_id_verification_status != stripe_verification_status:
                self.tax_id_verification_status = stripe_verification_status
                needs_update = True
                update_fields.append('tax_id_verification_status')
            
            # Always update verification timestamp
            self.tax_id_verification_updated_at = timezone.now()
            update_fields.append('tax_id_verification_updated_at')
            
            if needs_update:
                self.save(update_fields=update_fields)
                
            return {
                'success': True,
                'updated_fields': update_fields,
                'needs_update': needs_update,
                'message': f"Updated {len(update_fields)} fields from Stripe" if needs_update else "No updates needed"
            }
        else:
            needs_update = False
            update_fields = []
            
            stripe_tax_id_type = stripe_tax_id_dict.get('type')
            stripe_tax_id_value = stripe_tax_id_dict.get('value')
            stripe_verification_status = stripe_tax_id_dict.get('verification', {}).get('status', 'pending')
            
            # Only update if local value is missing
            if not self.tax_id_type and stripe_tax_id_type:
                self.tax_id_type = stripe_tax_id_type
                needs_update = True
                update_fields.append('tax_id_type')
            
            # Only update if local value is missing
            if not self.get_decrypted_tax_id() and stripe_tax_id_value:
                # Encrypt the tax_id value before saving
                encrypted_tax_id = cipher_suite.encrypt(stripe_tax_id_value.encode()).decode()
                self.tax_id = encrypted_tax_id
                needs_update = True
                update_fields.append('tax_id')
            
            # Always update verification status from Stripe
            if self.tax_id_verification_status != stripe_verification_status:
                self.tax_id_verification_status = stripe_verification_status
                needs_update = True
                update_fields.append('tax_id_verification_status')
            
            # Always update verification timestamp
            self.tax_id_verification_updated_at = timezone.now()
            update_fields.append('tax_id_verification_updated_at')
            
            if needs_update:
                self.save(update_fields=update_fields)
                
            return {
                'success': True,
                'updated_fields': update_fields,
                'needs_update': needs_update,
                'message': f"Updated {len(update_fields)} fields from Stripe" if needs_update else "No updates needed"
            }

    def _get_requirements_by_tax_id_type(self, tax_id_type):
        """Helper method to get requirements by tax ID type"""
        requirements = {
            'in_gst': {
                'format': 'XXAAAAA0000A1Z5',
                'example': '22AAAAA0000A1Z5',
                'description': '15 characters: 2-digit state code + 10 alphanumeric + 1 letter + 1 digit + 1 letter',
                'validation': 'GST number must be 15 characters long'
            },
            'us_ein': {
                'format': 'XX-XXXXXXX',
                'example': '12-3456789',
                'description': '9 digits in format XX-XXXXXXX',
                'validation': 'EIN must be 9 digits'
            },
            'gb_vat': {
                'format': 'XXXXXXXXXX',
                'example': '*********',
                'description': '9 or 12 digits',
                'validation': 'VAT number must be 9 or 12 digits'
            },
            'ca_gst_hst': {
                'format': 'XXXXXXXXXXRT0001',
                'example': '*********RT0001',
                'description': '15 characters: 9 digits + RT + 4 digits',
                'validation': 'GST/HST number must be 15 characters'
            },
            'au_abn': {
                'format': 'XXXXXXXXXXX',
                'example': '***********',
                'description': '11 digits',
                'validation': 'ABN must be 11 digits'
            },
            'sg_gst': {
                'format': 'XXXXXXXXX',
                'example': '*********',
                'description': '9 digits',
                'validation': 'GST number must be 9 digits'
            },
            'my_sst': {
                'format': 'XXXXXXXXXX',
                'example': '*********0',
                'description': '10 digits',
                'validation': 'SST number must be 10 digits'
            },
            'th_vat': {
                'format': 'XXXXXXXXXX',
                'example': '*********0',
                'description': '10 digits',
                'validation': 'VAT number must be 10 digits'
            },
            'eu_vat': {
                'format': 'XX999999999',
                'example': 'DE*********',
                'description': '2-letter country code + 8-12 digits',
                'validation': 'VAT number must start with country code + digits'
            }
        }
        
        return requirements.get(tax_id_type)

    def save(self, *args, **kwargs):
        try:
            is_new = not self.pk
            # Always set these fields to True before saving
            self.is_primary = True
            self.is_active = True

            if not self.reference_id:
                raw_reference_id = str(uuid.uuid4())
                self.reference_id = cipher_suite.encrypt(
                    raw_reference_id.encode()).decode()

            super().save(*args, **kwargs)

            if is_new:
                logger.info(
                    "New organization created",
                    extra={
                        'org_id': str(self.id),
                        'org_name': self.name,
                        'created_by': self.created_by
                    }
                )
            else:
                logger.info(
                    "Organization updated",
                    extra={
                        'org_id': str(self.id),
                        'org_name': self.name
                    }
                )
        except Exception as e:
            logger.error(
                "Error saving organization",
                extra={
                    'org_name': self.name,
                    'error': str(e)
                },
                exc_info=True
            )
            raise

    def __str__(self):
        return self.name if self.name else f"Organization {self.id}"

    def consume_bankid_token(self):
        """
        Consume a BankID token and handle auto-topup if enabled
        """
        if self.bankid_tokens_remaining <= 0:
            return False
        
        self.bankid_tokens_remaining -= 1
        self.bankid_tokens_used += 1
        self.save(update_fields=['bankid_tokens_remaining', 'bankid_tokens_used'])
        
        # Check if auto-topup should be triggered
        if (self.bankid_auto_topup_enabled and 
            self.bankid_tokens_remaining <= self.bankid_auto_topup_threshold):
            self.trigger_auto_topup()
        
        return True

    def trigger_auto_topup(self):
        """
        Trigger automatic token top-up
        """
        from subscriptions.services.bankid_token_service import BankIDTokenService
        from subscriptions.models.subscription_masters import UserSubscription
        
        # Get the organization owner's active subscription
        owner = self.get_organization_owner()
        if not owner:
            return {'success': False, 'message': 'Organization owner not found'}
        
        try:
            user_subscription = UserSubscription.objects.filter(
                user=owner,
                status__in=['active', 'trial']
            ).first()
            
            if not user_subscription:
                return {'success': False, 'message': 'No active subscription found for organization owner'}
            
            service = BankIDTokenService()
            return service.purchase_tokens(
                organization=self,
                user_subscription=user_subscription,
                quantity=self.bankid_auto_topup_quantity
            )
        except Exception as e:
            return {'success': False, 'message': f'Error triggering auto-topup: {str(e)}'}

    def add_bankid_tokens(self, quantity, purchased=True):
        """
        Add BankID tokens to the organization
        """
        self.bankid_tokens_remaining += quantity
        if purchased:
            self.bankid_tokens_purchased += quantity
        self.bankid_last_topup_date = timezone.now()
        self.save(update_fields=['bankid_tokens_remaining', 'bankid_tokens_purchased', 'bankid_last_topup_date'])

    def get_organization_owner(self):
        """
        Get the original owner (org_superadmin) of the organization
        regardless of which user is accessing it.

        Returns:
            User: The organization owner (org_superadmin)
            None: If no owner is found
        """

        """usage 
        # Example usage
            organization = Organization.objects.get(reference_id='some_ref_id')
            owner = organization.get_organization_owner()

            if owner:
                # Access owner details
                owner_email = owner.get_decrypted_email()
                owner_name = f"{owner.first_name} {owner.last_name}"
                reference_id = owner.reference_id
        """
        try:
            # Find the user with role org_superadmin who is associated with this organization
            owner = self.users.filter(
                role='org_superadmin',
                organizations=self,
                is_active=True
            ).first()

            if owner:

                return owner

            return None

        except Exception as e:

            return None

    class Meta:
        verbose_name = "Organization"
        verbose_name_plural = "Organizations"
        indexes = [
            models.Index(fields=['tax_id_verification_status']),
            models.Index(fields=['tax_id_type']),
        ]



# TODO: Uncomment this to implement the Organization logic.
# class OrganizationUser(models.Model):
#     """
#     Mapping model to represent the relationship between an Organization and a User,
#     including metadata such as who invited the user, invitation status, and blocking.
#     """
#     organization = models.ForeignKey(
#         'accounts.Organization',
#         on_delete=models.CASCADE,
#         related_name='organization_users'
#     )
#     user = models.ForeignKey(
#         'accounts.User',
#         on_delete=models.CASCADE,
#         related_name='user_organizations'
#     )
#     invited_by = models.ForeignKey(
#         'accounts.User',
#         on_delete=models.SET_NULL,
#         null=True,
#         blank=True,
#         related_name='invited_organization_users',
#         help_text="The user who invited this user to the organization."
#     )
#     invited_at = models.DateTimeField(auto_now_add=True)
#     is_blocked = models.BooleanField(default=False)
#     blocked_at = models.DateTimeField(null=True, blank=True)
#     is_active = models.BooleanField(default=True)
#     is_deleted = models.BooleanField(default=False)

#     # You can add more fields as needed, e.g., invitation_status, custom roles, etc.

#     class Meta:
#         unique_together = ('organization', 'user')
#         verbose_name = "Organization User"
#         verbose_name_plural = "Organization Users"

#     def block(self):
#         self.is_blocked = True
#         self.blocked_at = timezone.now()
#         self.save(update_fields=['is_blocked', 'blocked_at'])

#     def unblock(self):
#         self.is_blocked = False
#         self.blocked_at = None
#         self.save(update_fields=['is_blocked', 'blocked_at'])

#     def __str__(self):
#         return f"{self.user} in {self.organization} (Invited by: {self.invited_by})"

