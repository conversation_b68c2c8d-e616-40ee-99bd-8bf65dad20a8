import uuid
import logging

from django.utils import timezone
from cryptography.fernet import Fernet
from django.conf import settings
from django.db import models

from accounts.services.jwt_authentication.authentication import fernet_decrypt

cipher_suite = Fernet(settings.ENCRYPTION_KEY)
logger = logging.getLogger('app')


class Organization(models.Model):
    """Model to represent organizations."""

    id = models.UUIDField(
        default=uuid.uuid4, unique=True, primary_key=True, editable=False
    )
    name = models.CharField(max_length=255, null=True, blank=True, unique=True)
    number = models.CharField(max_length=255, null=True, blank=True, unique=True)
    tax_id = models.CharField(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    reference_id = models.CharField(
        max_length=255, editable=False, unique=True, null=True, blank=True
    )
    is_owner_included = models.<PERSON><PERSON>an<PERSON>ield(default=True)
    # Reference to the user (assuming custom User model)
    is_primary = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    show_on_invoice = models.BooleanField(default=False)
    created_by = models.CharField(max_length=255, blank=True, null=True)
    creator_user = models.ForeignKey(
        'accounts.User',
        on_delete=models.CASCADE,
        related_name='created_organizations',
        null=True,
        blank=True,
    )

    org_domain = models.CharField(max_length=255, null=True, blank=True)
    user_added_count = models.PositiveIntegerField(default=0)
    total_active_user = models.PositiveIntegerField(default=0)
    add_on_user_limit = models.PositiveBigIntegerField(default=0) # to track the add-on limit for an organization.

    def get_decrypted_name(self):
        return fernet_decrypt(self.name.encode()) if self.name else None

    def get_decrypted_number(self):
        return fernet_decrypt(self.number.encode()) if self.number else None

    def get_decrypted_tax_id(self):
        return fernet_decrypt(self.tax_id.encode()) if self.tax_id else None

    def get_decrypted_reference_id(self):
        return fernet_decrypt(self.reference_id.encode()) if self.reference_id else None

    def save(self, *args, **kwargs):
        try:
            is_new = not self.pk
            # Always set these fields to True before saving
            self.is_primary = True
            self.is_active = True

            if not self.reference_id:
                raw_reference_id = str(uuid.uuid4())
                self.reference_id = cipher_suite.encrypt(
                    raw_reference_id.encode()).decode()

            super().save(*args, **kwargs)

            if is_new:
                logger.info(
                    "New organization created",
                    extra={
                        'org_id': str(self.id),
                        'org_name': self.name,
                        'created_by': self.created_by
                    }
                )
            else:
                logger.info(
                    "Organization updated",
                    extra={
                        'org_id': str(self.id),
                        'org_name': self.name
                    }
                )
        except Exception as e:
            logger.error(
                "Error saving organization",
                extra={
                    'org_name': self.name,
                    'error': str(e)
                },
                exc_info=True
            )
            raise

    def __str__(self):
        return self.name if self.name else f"Organization {self.id}"

    def get_organization_owner(self):
        """
        Get the original owner (org_superadmin) of the organization
        regardless of which user is accessing it.

        Returns:
            User: The organization owner (org_superadmin)
            None: If no owner is found
        """

        """usage 
        # Example usage
            organization = Organization.objects.get(reference_id='some_ref_id')
            owner = organization.get_organization_owner()

            if owner:
                # Access owner details
                owner_email = owner.get_decrypted_email()
                owner_name = f"{owner.first_name} {owner.last_name}"
                reference_id = owner.reference_id
        """
        try:
            # Find the user with role org_superadmin who is associated with this organization
            owner = self.users.filter(
                role='org_superadmin',
                organizations=self,
                is_active=True
            ).first()

            if owner:

                return owner

            return None

        except Exception as e:

            return None



            # INSERT_YOUR_CODE



# TODO: Uncomment this to implement the Organization logic.
# class OrganizationUser(models.Model):
#     """
#     Mapping model to represent the relationship between an Organization and a User,
#     including metadata such as who invited the user, invitation status, and blocking.
#     """
#     organization = models.ForeignKey(
#         'accounts.Organization',
#         on_delete=models.CASCADE,
#         related_name='organization_users'
#     )
#     user = models.ForeignKey(
#         'accounts.User',
#         on_delete=models.CASCADE,
#         related_name='user_organizations'
#     )
#     invited_by = models.ForeignKey(
#         'accounts.User',
#         on_delete=models.SET_NULL,
#         null=True,
#         blank=True,
#         related_name='invited_organization_users',
#         help_text="The user who invited this user to the organization."
#     )
#     invited_at = models.DateTimeField(auto_now_add=True)
#     is_blocked = models.BooleanField(default=False)
#     blocked_at = models.DateTimeField(null=True, blank=True)
#     is_active = models.BooleanField(default=True)
#     is_deleted = models.BooleanField(default=False)

#     # You can add more fields as needed, e.g., invitation_status, custom roles, etc.

#     class Meta:
#         unique_together = ('organization', 'user')
#         verbose_name = "Organization User"
#         verbose_name_plural = "Organization Users"

#     def block(self):
#         self.is_blocked = True
#         self.blocked_at = timezone.now()
#         self.save(update_fields=['is_blocked', 'blocked_at'])

#     def unblock(self):
#         self.is_blocked = False
#         self.blocked_at = None
#         self.save(update_fields=['is_blocked', 'blocked_at'])

#     def __str__(self):
#         return f"{self.user} in {self.organization} (Invited by: {self.invited_by})"
