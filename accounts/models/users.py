import logging
import os
from uuid import uuid4
from django.utils import timezone

from accounts.services.jwt_authentication.authentication import fernet_decrypt
from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.contrib.auth.models import UserManager as DefaultUserManager
from django.db import models
from esign.utils.custom_constants import USER_ROLE_CHOICES

logger = logging.getLogger("app")


class CustomUserManager(DefaultUserManager):
    def create_user(self, email, password=None, **extra_fields):
        try:
            if not email:
                logger.error("Email is required for user creation")
                raise ValueError("The Email field must be set")

            email = self.normalize_email(email)
            user = self.model(email=email, **extra_fields)
            user.set_password(password)
            user.save(using=self._db)

            logger.info(
                "User created successfully", extra={"email": email, "user_id": user.id}
            )
            return user
        except Exception as e:
            logger.error(
                "Error creating user",
                extra={"email": email, "error": str(e)},
                exc_info=True,
            )
            raise

    def create_superuser(self, email, password=None, **extra_fields):
        try:
            extra_fields.setdefault("is_staff", True)
            extra_fields.setdefault("is_superuser", True)
            extra_fields.setdefault("role", "super_admin")

            logger.info("Creating superuser", extra={"email": email})
            return self.create_user(email, password, **extra_fields)
        except Exception as e:
            logger.error(
                "Error creating superuser",
                extra={"email": email, "error": str(e)},
                exc_info=True,
            )
            raise


class AccountHistory(models.Model):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True,
        related_name="account_history"
    )
    email = models.CharField(max_length=255)
    private_email = models.CharField(max_length=255, blank=True, null=True)
    phone_number = models.CharField(max_length=100, blank=True, null=True)
    role = models.CharField(max_length=50)

    organization = models.ForeignKey(
        "accounts.Organization", on_delete=models.SET_NULL, null=True, blank=True
    )
    subscription = models.ForeignKey(
        "subscriptions.UserSubscription", on_delete=models.SET_NULL, null=True, blank=True
    )
    plan_name = models.CharField(max_length=100, blank=True, null=True)
    stripe_subscription_id = models.CharField(max_length=255, blank=True, null=True)
    plan_id = models.CharField(max_length=100, blank=True, null=True)

    is_self_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField()
    deleted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True,
        related_name="deleted_users"
    )

    class Meta:
        ordering = ["-deleted_at"]

    def get_decrypted_email(self):
        return fernet_decrypt(self.email.encode()) if self.email else None

    def get_decrypted_phone_number(self):
        return fernet_decrypt(self.phone_number.encode()) if self.phone_number else None

    def get_unencrypted_data(self):
        if self.get_decrypted_email():
            return self.get_decrypted_email()
        elif self.get_decrypted_phone_number():
            return self.get_decrypted_phone_number()
        else:
            return self.username

    def __str__(self):
        return f"{self.get_unencrypted_data()} deleted at {self.deleted_at}"


class User(AbstractUser):
    objects = CustomUserManager()
    id = models.BigAutoField(primary_key=True)
    reference_id = models.CharField(max_length=255, editable=False, unique=True)
    private_number = models.CharField(max_length=100)
    private_phone = models.CharField(max_length=100)
    private_email = models.CharField(max_length=100)
    # check this for bank id timestamp
    authentication_method = models.CharField(
        max_length=20,
        choices=[
            ("kyc", "KYC"),
            ("otp", "OTP"),
            ("sweden_bank_id", "Sweden Bank ID"),
            ("email", "email"),
        ],
        blank=True,  # Allow initially unset
        null=True,  # Allow NULL in database
    )
    # New fields you want to add
    role = models.CharField(
        max_length=50,
        choices=USER_ROLE_CHOICES,
        default="org_superadmin",
    )
    user_type = models.CharField(max_length=50, blank=True, null=True)
    is_invited = models.BooleanField(default=False)
    phone_number = models.CharField(max_length=255, blank=True, null=True)
    residency = models.CharField(max_length=100, blank=True, null=True)
    stripe_customer_id = models.CharField(max_length=255, blank=True, null=True)
    bank_id = models.CharField(max_length=50, blank=True, null=True)
    organisation_number = models.CharField(max_length=50, blank=True, null=True)
    address1 = models.CharField(max_length=255, blank=True, null=True)
    address2 = models.CharField(max_length=255, blank=True, null=True)
    zip_code = models.CharField(max_length=20, blank=True, null=True)
    state = models.CharField(max_length=100, blank=True, null=True)
    country = models.CharField(max_length=100, blank=True, null=True)
    last_login = models.DateTimeField(blank=True, null=True)
    organizations = models.ManyToManyField(
        "Organization",
        related_name="users",
        blank=True,
    )
    is_active = models.BooleanField(default=True)
    profile_picture = models.ImageField(
        upload_to="profile_pictures/", blank=True, null=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.TextField(null=True, blank=True)
    is_deleted = models.BooleanField(default=False)
    self_deleted = models.BooleanField(default=False)
    reactivated_at = models.DateTimeField(null=True, blank=True)
    reactivated_by = models.TextField(null=True, blank=True)

    # Add these new fields
    activation_token = models.CharField(max_length=100, null=True, blank=True)
    activation_token_created_at = models.DateTimeField(null=True, blank=True)
    last_login_ip = models.CharField(max_length=45, null=True, blank=True)
    meta_data = models.JSONField(null=True, blank=True)

    # Lockout fields
    failed_login_attempts = models.IntegerField(default=0)
    is_locked = models.BooleanField(default=False)
    locked_at = models.DateTimeField(null=True, blank=True)
    two_fa_enabled = models.BooleanField(default=True)

    class Meta:
        ordering = ["-date_joined"]

    def __str__(self):
        if self.get_decrypted_email():
            return self.get_decrypted_email()
        elif self.get_decrypted_phone_number():
            return self.get_decrypted_phone_number()
        else:
            return self.username

    # def soft_delete_old(self, deleted_by=None):
    #     """Soft delete user and handle associated data"""
    #     from django.db import transaction
    #     from subscriptions.models import UserSubscription
    #     import stripe
    #     from django.conf import settings

    #     stripe.api_key = settings.STRIPE_SECRET_KEY

    #     with transaction.atomic():
    #         is_self_deletion = deleted_by is None or deleted_by == self

    #         # Create AccountHistory record for the user being deleted
    #         org = self.organizations.filter(is_active=True).first()

    #         active_user_subscription = UserSubscription.objects.filter(user=self, status="active").order_by('-end_date').first()
    #         AccountHistory.objects.create(
    #             user_reference_id=self.reference_id,
    #             email=self.email,  # Storing encrypted email
    #             private_email=self.private_email,
    #             phone_number=self.phone_number,  # Storing encrypted phone
    #             role=self.role,
    #             deleted_at=timezone.now(),
    #             deleted_by=self if is_self_deletion else deleted_by,
    #             is_self_deleted=is_self_deletion,
    #             organization=org,
    #             subscription=active_user_subscription,
    #             plan_name=getattr(active_user_subscription.plan, "name", None) if active_user_subscription and active_user_subscription.plan else None,
    #             stripe_subscription_id=getattr(active_user_subscription, "stripe_subscription_id", None) if active_user_subscription else None,
    #         )

    #         if self.role == "org_superadmin":
    #             # Get all organizations where this user is super admin
    #             orgs = self.organizations.all()

    #             # Get all users in these organizations
    #             org_users = User.objects.filter(organizations__in=orgs).distinct()

    #             # Cancel all active subscriptions for these users
    #             active_subscriptions = UserSubscription.objects.filter(
    #                 user__in=org_users,
    #                 status='active'
    #             )

    #             for subscription in active_subscriptions:
    #                 try:
    #                     # Cancel in Stripe if subscription exists
    #                     if subscription.stripe_subscription_id:
    #                         stripe.Subscription.modify(
    #                             subscription.stripe_subscription_id,
    #                             cancel_at_period_end=True
    #                         )

    #                     # Update subscription status
    #                     subscription.status = 'cancelled'
    #                     subscription.cancelled_date = timezone.now()
    #                     subscription.save()

    #                 except stripe.error.StripeError as e:
    #                     logger.error(f"Error cancelling Stripe subscription: {str(e)}")
    #                     # Continue with other subscriptions even if one fails
    #                     continue

    #             # Deactivate all organizations
    #             for org in orgs:
    #                 org.is_active = False
    #                 org.deactivated_at = timezone.now()
    #                 org.save()

    #             # Soft delete all users in the organization
    #             for user in org_users:
    #                 user.is_active = False
    #                 user.is_deleted = True
    #                 user.deleted_at = timezone.now()
    #                 user.self_deleted = True
    #                 user.save()

    #         # Soft delete the user
    #         self.is_active = False
    #         self.is_deleted = True
    #         self.deleted_at = timezone.now()
    #         self.self_deleted = True
    #         # Remove all organizations from the user
    #         # self.organizations.clear()
    #         self.save()

    def soft_delete(self, deleted_by=None):
        """Soft delete user and handle associated data"""
        # from accounts.tasks import soft_delete_user_task
        # soft_delete_user_task.delay(self.email, deleted_by_id=deleted_by.id if deleted_by else None)
        from accounts.services.soft_delete_service import soft_delete_user_and_related
        soft_delete_user_and_related(self.email, deleted_by=deleted_by)

    def get_decrypted_email(self):
        return fernet_decrypt(self.email.encode()) if self.email else None

    def get_decrypted_phone_number(self):
        return fernet_decrypt(self.phone_number.encode()) if self.phone_number else None

    def get_full_name(self) -> str:
        if self.first_name and self.last_name:
            return self.first_name + " " + self.last_name
        elif self.first_name:
            return self.first_name
        elif self.last_name:
            return self.last_name
        else:
            return None

    def save(self, *args, **kwargs):
        if not self.reference_id:
            self.reference_id = str(uuid4())
            # self.username = f"usr_{self.reference_id}"

        # # Create directory if it doesn't exist
        # upload_path = os.path.join(settings.MEDIA_ROOT, "profile_pictures")
        # os.makedirs(upload_path, exist_ok=True)

        super().save(*args, **kwargs)


class BlacklistedToken(models.Model):
    token = models.CharField(max_length=1024, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.token


class SessionTrack(models.Model):
    user = models.ForeignKey("User", on_delete=models.CASCADE)
    # tab_id = models.UUIDField(default=uuid4, unique=True)
    ip_address = models.CharField(max_length=45)
    is_logged_in = models.BooleanField(default=True)
    last_active = models.DateTimeField(auto_now=True)
    last_logged_out_at = models.DateTimeField(null=True, blank=True)
    user_agent = models.CharField(max_length=255)
    generated_token = models.CharField(max_length=1024, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "session_track"


class UserContactHistory(models.Model):
    CONTACT_TYPES = (
        ('email', 'Email'),
        ('phone', 'Phone Number'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE,
                             related_name='contact_history')
    contact_type = models.CharField(max_length=10, choices=CONTACT_TYPES)
    old_value = models.CharField(max_length=255)
    new_value = models.CharField(max_length=255)
    changed_at = models.DateTimeField(auto_now_add=True)
    changed_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name='contact_changes_made')
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-changed_at']
        indexes = [
            models.Index(fields=['user', 'contact_type', 'changed_at']),
            models.Index(fields=['user', 'contact_type', 'is_active']),
        ]

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.contact_type} change at {self.changed_at}"
