import logging
from django.contrib.auth.management.commands.createsuperuser import (
    Command as CreateSuperuserCommand,
)
from django.core.management import CommandError

logger = logging.getLogger('app')


class Command(CreateSuperuserCommand):
    help = "Create a superuser with only first name, last name, email, and password."

    def add_arguments(self, parser):
        super().add_arguments(parser)
        parser.add_argument(
            "--first_name",
            dest="first_name",
            default=None,
            help="Specifies the first name for the superuser.",
        )
        parser.add_argument(
            "--last_name",
            dest="last_name",
            default=None,
            help="Specifies the last name for the superuser.",
        )

    def handle(self, *args, **options):
        try:
            first_name = options.get("first_name")
            last_name = options.get("last_name")
            email = options.get("email")
            password = options.get("password")

            if not all([first_name, last_name, email, password]):
                logger.error(
                    "Missing required fields for superuser creation",
                    extra={
                        'email': email,
                        'has_first_name': bool(first_name),
                        'has_last_name': bool(last_name)
                    }
                )
                raise CommandError("All fields are required")

            user_data = {
                "first_name": first_name,
                "last_name": last_name,
                "email": email,
                "password": password,
            }

            user = self.UserModel._default_manager.db_manager().create_superuser(**user_data)
            
            logger.info(
                "Superuser created successfully",
                extra={
                    'email': email,
                    'user_id': str(user.id)
                }
            )
            
            self.stdout.write(
                self.style.SUCCESS(f"Superuser created successfully with email: {email}")
            )
        except Exception as e:
            logger.error(
                "Error creating superuser",
                extra={
                    'email': options.get('email'),
                    'error': str(e)
                },
                exc_info=True
            )
            raise
