import stripe
import logging

from django.conf import settings
from django.core.management.base import BaseCommand

from accounts.models.users import User

logger = logging.getLogger("app")


stripe.api_key = settings.STRIPE_SECRET_KEY

class Command(BaseCommand):
    help = "Sync Stripe customers for users who have stripe_customer_id but may be deleted on Stripe"

    def handle(self, *args, **kwargs):
        users = User.objects.filter(stripe_customer_id__isnull=False, is_deleted=False)
        logger.info(f"Found {users.count()} users with stripe_customer_id and deleted=False")

        for user in users:
            customer_id = user.stripe_customer_id
            logger.info(f"\nProcessing user: {user.get_decrypted_email()} (Customer ID: {customer_id})")

            try:
                customer = stripe.Customer.retrieve(customer_id)
                if customer.get("deleted", False):
                    logger.info(f"Customer was deleted, skipping the creating")
                    # continue
                    raise stripe.error.InvalidRequestError(
                        message="Customer was deleted",
                        param="id"
                    )
                logger.info("✅ Stripe customer exists. Skipping...")
            except Exception as e:
                logger.info("⚠️ Stripe customer not found or deleted. Creating new one...")
                try:
                    new_customer = stripe.Customer.create(
                        email=user.get_decrypted_email(),
                        name=user.get_full_name(),
                        metadata={"user_id": str(user.id)},
                    )
                    user.stripe_customer_id = new_customer.id
                    user.save(update_fields=['stripe_customer_id'])
                    logger.info(f"✅ New Stripe customer created: {new_customer.id}")
                except Exception as e:
                    self.stderr.write(f"❌ Failed to create customer for {user.email}: {str(e)}")
                    logger.error(f"Error creating Stripe customer for user {user.id}")

        logger.info("\nDone syncing Stripe customers.")
