from rest_framework import serializers
from accounts.models.users import User
from accounts.serializers.organisation_serializers import OrganizationSerializer


class UserFullDetailsRetrieveSerializer(serializers.ModelSerializer):
    organizations = OrganizationSerializer(many=True, read_only=True)
    email = serializers.SerializerMethodField()
    phone_number = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id',
            'reference_id',
            'email',
            'phone_number',
            'first_name',
            'last_name',
            'role',
            'user_type',
            'is_invited',
            'residency',
            'stripe_customer_id',
            'bank_id',
            'organisation_number',
            'address1',
            'address2',
            'zip_code',
            'state',
            'country',
            'last_login',
            'organizations',
            'is_active',
            'profile_picture',
            'created_at',
            'updated_at',
            'authentication_method',
            'meta_data'
        ]

    def get_email(self, obj):
        return obj.get_decrypted_email()

    def get_phone_number(self, obj):
        return obj.get_decrypted_phone_number()

    def get_deleted_by(self, obj):
        return obj.deleted_by.get_decrypted_reference_id() if obj.deleted_by else None

    def get_deleted_at(self, obj):
        return obj.deleted_at.strftime('%Y-%m-%d %H:%M:%S') if obj.deleted_at else None

    def get_deleted_by(self, obj):
        return obj.deleted_by.get_decrypted_reference_id() if obj.deleted_by else None

    def get_deleted_by_name(self, obj):
        return f"{obj.deleted_by.first_name} {obj.deleted_by.last_name}" if obj.deleted_by else None

    def get_deleted_at(self, obj):
        return obj.deleted_at.strftime('%Y-%m-%d %H:%M:%S') if obj.deleted_at else None

    def get_deleted_by_email(self, obj):
        return obj.deleted_by.email if obj.deleted_by else None

    def get_deleted_by_phone_number(self, obj):
        return obj.deleted_by.phone_number if obj.deleted_by else None

    def get_deleted_by_role(self, obj):
        return obj.deleted_by.role if obj.deleted_by else None
