import logging
from django.utils import timezone

from accounts.exceptions import FailedLoginAttemptException
from accounts.models.otp import OneTimePassword
from accounts.services.jwt_authentication.authentication import generate_hmac_token
from cryptography.fernet import Fernet
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import check_password
from django.db.models import Q
from django.utils.translation import gettext as _
from rest_framework import serializers
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.response import Response

logger = logging.getLogger(__name__)
cipher_suite = Fernet(settings.ENCRYPTION_KEY)


class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)

    def validate(self, data):
        email = data.get("email")
        password = data.get("password")
        if not email or not password:
            raise AuthenticationFailed(_("Both email and password are required."))
        # Authenticate user
        private_email = generate_hmac_token(email)
        user_obj = get_user_model().objects.filter(
            private_email=private_email, is_deleted=False).first()

        if user_obj is None:
            raise AuthenticationFailed(_("Account doesn't exists."))

        login_attempts = getattr(user_obj, 'failed_login_attempts', 0)
        if login_attempts:
            if login_attempts >= 10:
                login_attempts = 0
            else:
                login_attempts = 10 - (login_attempts + 1)
        elif login_attempts == 0:
            login_attempts = 9

        # Check if user is self-deleted
        if user_obj.self_deleted:
            raise AuthenticationFailed(
                _("This account has been deleted by the user and cannot be reactivated."))

        if not user_obj.is_active:
            raise FailedLoginAttemptException(
                _("Your Account is Deactivated. Please Contact your admin"), attempt=login_attempts)

        # Check if user is locked
        if getattr(user_obj, 'is_locked', False):
            raise FailedLoginAttemptException(
                _("Your account is locked due to too many failed login attempts. Please reset your password to unlock."), attempt=login_attempts)

        # Check if user is invited, if not then check if the user is verified using the OTP
        if not user_obj.is_invited:
            is_verified = OneTimePassword.objects.filter(
                email=private_email,
                is_link_verified_email=True,
            ).first()
            if not is_verified:
                raise FailedLoginAttemptException(
                    _("Please verify your account to login"), attempt=login_attempts)

        user = check_password(password, user_obj.password)

        if user is None or not user:
            # Increment failed attempts
            user_obj.failed_login_attempts = getattr(
                user_obj, 'failed_login_attempts', 0) + 1
            if user_obj.failed_login_attempts >= 10:
                user_obj.is_locked = True
                user_obj.locked_at = timezone.now()
                user_obj.save(
                    update_fields=["failed_login_attempts", "is_locked", "locked_at"])
                # Send password reset email (OTP)
                from accounts.tasks import send_blocked_account_info_email
                from django.utils.crypto import get_random_string
                otp = get_random_string(6, allowed_chars='**********')
                OneTimePassword.objects.update_or_create(
                    email=private_email,
                    defaults={
                        "otp_code": otp,
                        "expiration_time": timezone.now() + timezone.timedelta(minutes=10),
                        "is_verified": False,
                        "is_link_verified_email": False,
                    },
                )
                send_blocked_account_info_email.delay(
                    receiver=email,
                    subject="Account Locked – Too Many Failed Login Attempts",
                )
                raise FailedLoginAttemptException(
                    _("Your account is locked due to too many failed login attempts. Please reset your password using the 'Forgot Password' option to unlock it."), attempt=login_attempts)
            else:
                user_obj.save(update_fields=["failed_login_attempts"])
            raise FailedLoginAttemptException(
                _("Invalid Credentials. Please try again."), attempt=login_attempts)

        # Successful login: reset failed attempts and unlock if needed
        if user_obj.failed_login_attempts or user_obj.is_locked:
            user_obj.failed_login_attempts = 0
            user_obj.is_locked = False
            user_obj.locked_at = None
            user_obj.save(
                update_fields=["failed_login_attempts", "is_locked", "locked_at"])

        # 2FA: Only send OTP if enabled
        if getattr(user_obj, 'two_fa_enabled', True):
            from accounts.tasks import send_password_reset_otp_email
            from django.utils.crypto import get_random_string
            otp = get_random_string(6, allowed_chars='**********')
            OneTimePassword.objects.update_or_create(
                email=private_email,
                defaults={
                    "otp_code": otp,
                    "expiration_time": timezone.now() + timezone.timedelta(minutes=10),
                    "is_verified": False,
                },
            )
            send_password_reset_otp_email.delay(
                receiver=email,
                subject="Your Login OTP",
                otp=otp,
                user_name=user_obj.get_full_name()
            )
            # Instead of raising AuthenticationFailed, return a response for OTP required
            raise serializers.ValidationError({
                "status": "success",
                "status_code": 200,
                "message": "OTP sent to your email. Please verify to complete login."
            })
        # If 2FA is not enabled, allow direct login
        return {"user": user_obj}


class UserRegistrationSerializer(serializers.ModelSerializer):
    class Meta:
        model = get_user_model()
        fields = [
            "email",
            "username",
            "country",
            # "private_email",
            # "password",
            # "password_confirmation",
            "authentication_method",
            # "country",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # Decrypt sensitive fields for output
        if instance.email:
            representation["email"] = cipher_suite.decrypt(
                instance.email.encode()
            ).decode()

        if instance.country:
            representation["country"] = instance.country

        return representation

    def create(self, validated_data):
        User = get_user_model()
        encrypted_email = cipher_suite.encrypt(
            validated_data["email"].encode()).decode()

        # Check for existing user (soft-deleted or not)
        user = User.objects.filter(
            private_email=generate_hmac_token(validated_data["email"])).first()

        if user:
            if user.is_deleted:
                # Revive the soft-deleted user
                user.is_deleted = False
                user.self_deleted = False
                user.is_active = True
                user.deleted_at = None
                # Update other fields as needed
                user.username = encrypted_email
                user.private_email = generate_hmac_token(validated_data["email"])
                user.country = validated_data.get("country", user.country)
                user.authentication_method = validated_data.get(
                    "authentication_method", user.authentication_method)
                user.save()
                return user
            else:
                raise serializers.ValidationError(
                    "A user with this email already exists.")
        user = get_user_model().objects.create_user(
            username=cipher_suite.encrypt(validated_data["email"].encode()).decode(),
            email=cipher_suite.encrypt(validated_data["email"].encode()).decode(),
            private_email=generate_hmac_token(validated_data["email"]),
        )
        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = get_user_model()
        fields = ["first_name", "last_name", "phone_number", "email", "password"]
        extra_kwargs = {
            "password": {"write_only": True},  # Ensure password is write-only
            "email": {"required": False},
            "phone_number": {"required": False},
        }

    def validate_email(self, value):
        if value:
            # Get the current instance being updated
            instance = getattr(self, "instance", None)

            # Check if email exists for any other user
            existing_user = (
                get_user_model().objects.filter(private_email=generate_hmac_token(value))
                .exclude(id=instance.id if instance else None)
                .first()
            )

            if existing_user:
                raise serializers.ValidationError("This email is already registered.")
        return value

    def to_representation(self, instance):
        """
        Decrypt sensitive fields when returning data to the client.
        """
        representation = super().to_representation(instance)
        if instance.first_name:
            representation["first_name"] = instance.first_name
        if instance.last_name:
            representation["last_name"] = instance.last_name
        if instance.phone_number:
            representation["phone_number"] = cipher_suite.decrypt(
                instance.phone_number.encode()
            ).decode()
        if instance.email:
            representation["email"] = cipher_suite.decrypt(
                instance.email.encode()
            ).decode()
        return representation

    def update(self, instance, validated_data):
        """
        Encrypt sensitive fields and update the user instance.
        """
        if "first_name" in validated_data:
            instance.first_name = validated_data["first_name"]
        if "last_name" in validated_data:
            instance.last_name = validated_data["last_name"]
        if "phone_number" in validated_data:
            instance.phone_number = cipher_suite.encrypt(
                validated_data["phone_number"].encode()
            ).decode()
            instance.private_phone = generate_hmac_token(validated_data["phone_number"])
        if "email" in validated_data:
            instance.email = cipher_suite.encrypt(
                validated_data["email"].encode()
            ).decode()
            instance.private_email = generate_hmac_token(validated_data["email"])
        if "password" in validated_data:
            instance.set_password(validated_data["password"])  # Hash password

        instance.save()
        return instance


class EmailOtpVerifyViewSerilizer(serializers.ModelSerializer):
    otp_code = serializers.CharField(max_length=6)
    email = serializers.CharField(max_length=100)

    class Meta:
        model = OneTimePassword
        fields = ("otp_code", "email")


class PasswordChangeSerializer(serializers.Serializer):
    old_password = serializers.CharField(write_only=True, required=True)
    new_password = serializers.CharField(write_only=True, required=True)
    # confirm_password = serializers.CharField(write_only=True, required=True)
