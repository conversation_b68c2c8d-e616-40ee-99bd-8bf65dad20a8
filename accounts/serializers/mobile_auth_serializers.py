# from accounts.serializers.organisation_serializers import OrganizationSerializer
from accounts.models import Organization
from accounts.models.otp import OneTimePassword
from accounts.models.users import User
from accounts.services.jwt_authentication.authentication import (
    fernet_decrypt,
    generate_hmac_token,
)
from cryptography.fernet import Fernet
from django.conf import settings
from django.contrib.auth.hashers import make_password
from rest_framework import serializers
from django.utils import timezone

from utils_app.services.user_permissions_helpers import get_user_details_with_permissions
from accounts.services.contact_update_service import ContactUpdateService

cipher_suite = Fernet(settings.ENCRYPTION_KEY)


class MobileSerializer(serializers.Serializer):
    phone_number = serializers.Char<PERSON>ield(max_length=15)


class OtpVerifyViewSerilizer(serializers.ModelSerializer):
    otp_code = serializers.Char<PERSON>ield(max_length=6)
    phone_number = serializers.CharField(max_length=15)

    class Meta:
        model = OneTimePassword
        fields = ("otp_code", "phone_number")


class UserProfileVerifySerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["first_name", "last_name", "email", "phone_number"]

    def to_representation(self, instance):
        # Modify the data representation to include decrypted fields
        representation = super().to_representation(instance)
        if (
            "first_name" in representation
            and "last_name" in representation
            and "email" in representation
        ):
            # Add decrypted data
            representation["first_name"] = fernet_decrypt(instance.first_name.encode())
            representation["last_name"] = fernet_decrypt(instance.last_name.encode())
            representation["email"] = fernet_decrypt(instance.email.encode())

        return representation

    def update(self, instance, validated_data):
        # Encrypt the fields before updating
        if "first_name" in validated_data:
            instance.first_name = cipher_suite.encrypt(
                validated_data["first_name"].encode()
            ).decode()
        if "last_name" in validated_data:
            instance.last_name = cipher_suite.encrypt(
                validated_data["last_name"].encode()
            ).decode()
        if "email" in validated_data:
            instance.email = cipher_suite.encrypt(
                validated_data["email"].encode()
            ).decode()

        if instance.is_deleted:
            instance.is_deleted = False
            instance.deleted_at = None

        instance.save()

        return instance


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    profile_picture = serializers.ImageField(required=False, allow_null=True)
    password = serializers.CharField(write_only=True, required=False, allow_blank=True)

    class Meta:
        model = User
        fields = [

            "first_name",
            "last_name",
            "phone_number",
            "residency",
            "organisation_number",
            "address1",
            "address2",
            "zip_code",
            "state",
            "is_active",
            "country",
            "profile_picture",
            "private_email",
            # "private_number",
            "email",
            "password",
        ]
        extra_kwargs = {"password": {"write_only": True, "required": False}}

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        # Decrypt fields in the representation (if encrypted)
        for field in [
            "email",
            "phone_number",

        ]:
            if (
                field in representation and representation[field]
            ):  # check for existence and value
                try:
                    representation[field] = cipher_suite.decrypt(
                        representation[field].encode()
                    ).decode()
                except Exception as e:
                    print(e)
                    # Handle decryption errors gracefully
                    # logger.error(f"Decryption error for field '{field}': {e}")
                    representation[field] = None  # or some other default value

        return representation

    def validate_email(self, value):
        if value:
            # Get the current instance being updated
            instance = getattr(self, "instance", None)

            # Check if email exists for any other user
            existing_user = (
                User.objects.filter(private_email=generate_hmac_token(value))
                .exclude(id=instance.id if instance else None)
                .first()
            )

            if existing_user:
                raise serializers.ValidationError("This email is already registered.")

            # Check if user can update email this month
            contact_service = ContactUpdateService(instance)
            if not contact_service.can_update_contact('email'):
                raise serializers.ValidationError(
                    "You can only update your email once every 30 days")

        return value

    def validate_phone_number(self, value):
        if value:
            # Get the current instance being updated
            instance = getattr(self, "instance", None)

            # Check if phone exists for any other user
            existing_user = (
                User.objects.filter(private_phone=generate_hmac_token(value))
                .exclude(id=instance.id if instance else None)
                .first()
            )

            if existing_user:
                raise serializers.ValidationError(
                    "This phone number is already registered.")

            # Check if user can update phone this month
            contact_service = ContactUpdateService(instance)
            if not contact_service.can_update_contact('phone'):
                raise serializers.ValidationError(
                    "You can only update your phone number once every 30 days")

        return value

    def update(self, instance, validated_data):
        # Handle image upload
        password = validated_data.pop("password", None)
        if password:
            instance.password = make_password(password)
        private_email = generate_hmac_token(validated_data.get("email"))
        instance.private_email = private_email
        profile_picture = validated_data.pop("profile_picture", None)
        if profile_picture:
            instance.profile_picture = profile_picture

        # Encrypt fields before saving
        for field in ["email", "phone_number"]:
            if (
                field in validated_data and validated_data[field]
            ):  # check for existence and value
                try:
                    validated_data[field] = cipher_suite.encrypt(
                        validated_data[field].encode()
                    ).decode()
                except Exception as e:
                    # Handle encryption errors gracefully
                    # logger.error(f"Encryption error for field '{field}': {e}")
                    print(e)
                    raise serializers.ValidationError(
                        f"Encryption failed for field '{field}'"
                    )  # or some other appropriate action

        return super().update(instance, validated_data)


class UserProfileRetrieveSerializer(serializers.ModelSerializer):
    profile_picture = serializers.SerializerMethodField()
    user_type = serializers.SerializerMethodField()
    subscription = serializers.DictField(read_only=True)
    organisation_details = serializers.SerializerMethodField()
    is_email_verified = serializers.SerializerMethodField()
    is_phone_verified = serializers.SerializerMethodField()
    user_reference_id = serializers.SerializerMethodField()
    user_permissions = serializers.SerializerMethodField()
    able_to_change_email = serializers.SerializerMethodField()
    able_to_change_phone = serializers.SerializerMethodField()
    days_until_email_change = serializers.SerializerMethodField()
    days_until_phone_change = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id",
            "reference_id",
            "first_name",
            "last_name",
            "user_type",
            "phone_number",
            "residency",
            "organisation_number",
            "organisation_details",
            "address1",
            "address2",
            "zip_code",
            "is_active",
            "state",
            "country",
            "profile_picture",
            "email",
            "is_email_verified",
            "is_phone_verified",
            "user_reference_id",
            "subscription",
            "user_permissions",
            "able_to_change_email",
            "able_to_change_phone",
            "days_until_email_change",
            "days_until_phone_change",
        ]

    def get_user_permissions(self, instance):
        user_data = get_user_details_with_permissions(instance.reference_id)
        return user_data.get("permissions", "")

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        # Decrypt each field individually using if-else statements

        if "first_name" in representation and representation["first_name"]:
            try:
                representation["first_name"] = representation["first_name"]
            except Exception as e:
                print(f"Decryption error for field 'first_name': {e}")
                representation["first_name"] = None  # Set to None if decryption fails

        if "last_name" in representation and representation["last_name"]:
            try:
                representation["last_name"] = representation["last_name"]
            except Exception as e:
                print(f"Decryption error for field 'last_name': {e}")
                representation["last_name"] = None

        if "email" in representation and representation["email"]:
            try:
                representation["email"] = cipher_suite.decrypt(
                    representation["email"].encode()
                ).decode()
            except Exception as e:
                print(f"Decryption error for field 'email': {e}")
                representation["email"] = None

        if "phone_number" in representation and representation["phone_number"]:
            try:
                representation["phone_number"] = cipher_suite.decrypt(
                    representation["phone_number"].encode()
                ).decode()
            except Exception as e:
                print(f"Decryption error for field 'phone_number': {e}")
                representation["phone_number"] = None

        if "residency" in representation and representation["residency"]:
            try:
                representation["residency"] = representation["residency"]
            except Exception as e:
                print(f"Decryption error for field 'residency': {e}")
                representation["residency"] = None

        if "bank_id" in representation and representation["bank_id"]:
            try:
                representation["bank_id"] = representation["bank_id"]
            except Exception as e:
                print(f"Decryption error for field 'bank_id': {e}")
                representation["bank_id"] = None

        if "is_active" in representation and representation["is_active"]:
            try:
                representation["is_active"] = representation["is_active"]
            except Exception as e:
                print(f"Decryption error for field 'is_active': {e}")
                representation["is_active"] = None

        if (
            "organisation_number" in representation
            and representation["organisation_number"]
        ):
            try:
                representation["organisation_number"] = representation[
                    "organisation_number"
                ]
            except Exception as e:
                print(f"Decryption error for field 'organisation_number': {e}")
                representation["organisation_number"] = None

        if "address1" in representation and representation["address1"]:
            try:
                representation["address1"] = representation["address1"]
            except Exception as e:
                print(f"Decryption error for field 'address1': {e}")
                representation["address1"] = None

        if "address2" in representation and representation["address2"]:
            try:
                representation["address2"] = representation["address2"]
            except Exception as e:
                print(f"Decryption error for field 'address2': {e}")
                representation["address2"] = None

        if "zip_code" in representation and representation["zip_code"]:
            try:
                representation["zip_code"] = representation["zip_code"]
            except Exception as e:
                print(f"Decryption error for field 'zip_code': {e}")
                representation["zip_code"] = None

        if "state" in representation and representation["state"]:
            try:
                representation["state"] = representation["state"]
            except Exception as e:
                print(f"Decryption error for field 'state': {e}")
                representation["state"] = None

        if "country" in representation and representation["country"]:
            try:
                representation["country"] = representation["country"]
            except Exception as e:
                print(f"Decryption error for field 'country': {e}")
                representation["country"] = None

        # Add subscription data here
        subscription = self.context.get("subscription", {})
        representation["subscription"] = subscription

        # Add enable_2fa just below email, using the correct model field
        representation["enable_2fa"] = instance.two_fa_enabled

        return representation

    def get_organisation_details(self, obj):
        try:
            # Assuming 'organizations' is a ManyToManyField
            # org_obj =Organization.objects.filter(created_by=obj.reference_id).first()
            org_obj = obj.organizations.filter(is_primary=True).first()

            if not org_obj:
                return None

            # Safely get decrypted name
            try:
                name = org_obj.get_decrypted_name() if org_obj.name else None
            except Exception as e:
                name = None

            # Safely get decrypted number
            try:
                number = org_obj.get_decrypted_number() if org_obj.number else None
            except Exception as e:
                number = None

            # Safely get decrypted number
            try:
                tax_id = org_obj.get_decrypted_tax_id() if org_obj.tax_id else None
            except Exception as e:
                tax_id = None

            try:
                decrypted_reference_id = org_obj.get_decrypted_reference_id() if org_obj.reference_id else None
            except Exception as e:
                decrypted_reference_id = None

            return {
                "name": name,
                "number": number,
                "tax_id": tax_id,
                "org_domain": org_obj.org_domain,
                "reference_id": org_obj.reference_id if org_obj.reference_id else None,
                "decrypted_reference_id": decrypted_reference_id if decrypted_reference_id else None,
                "is_primary": org_obj.is_primary if org_obj.is_primary else None,
                "created_by": org_obj.created_by if org_obj.created_by else None,
            }
        except Exception as e:
            print(e)
            return None

    def get_user_reference_id(self, instance):
        return instance.reference_id

    def get_profile_picture(self, instance):
        """Get the profile picture URL."""
        if hasattr(instance, "profile_picture") and instance.profile_picture:
            return instance.profile_picture.url  # Return URL if image exists
        return None  # Return None otherwise

    def get_user_type(self, instance):
        """Get the user reference id."""
        if hasattr(instance, "user_type") and instance.user_type:
            return instance.user_type  # Return URL if image exists
        return None  # Return None if user_type is not set

    def get_is_email_verified(self, obj):
        """Get the is_email_verified status."""
        # decrypted_email = generate_hmac_token(obj.get_decrypted_email())
        if obj.is_invited:
            return True

        if obj:
            otp_record = OneTimePassword.objects.filter(
                email=generate_hmac_token(obj.get_decrypted_email())
            ).first()
            return otp_record.is_link_verified_email if otp_record else False
        return False

    def get_is_phone_verified(self, obj):
        if obj:
            otp_record = OneTimePassword.objects.filter(
                phone_number=generate_hmac_token(obj.get_decrypted_phone_number())
            ).first()
            return otp_record.is_link_verified_phone if otp_record else False
        return False

    def get_able_to_change_email(self, obj):
        contact_service = ContactUpdateService(obj)
        return contact_service.can_update_contact('email')

    def get_able_to_change_phone(self, obj):
        contact_service = ContactUpdateService(obj)
        return contact_service.can_update_contact('phone')

    def get_days_until_email_change(self, obj):
        contact_service = ContactUpdateService(obj)
        last_update = contact_service.get_contact_history('email').first()
        if last_update:
            days_passed = (timezone.now() - last_update.changed_at).days
            remaining_days = max(0, 30 - days_passed)
            return remaining_days
        return 0

    def get_days_until_phone_change(self, obj):
        contact_service = ContactUpdateService(obj)
        last_update = contact_service.get_contact_history('phone').first()
        if last_update:
            days_passed = (timezone.now() - last_update.changed_at).days
            remaining_days = max(0, 30 - days_passed)
            return remaining_days
        return 0


class InvitedUserSerializer(serializers.ModelSerializer):
    # organizations = OrganizationSerializer(many=True)  # Serialize organizations linked to the user

    class Meta:
        model = User
        fields = [
            "id",
            "reference_id",
            "email",
            "first_name",
            "last_name",
            "phone_number",
            "role",
            "is_active",
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if "phone_number" in representation and representation["phone_number"]:
            try:
                representation["phone_number"] = cipher_suite.decrypt(
                    representation["phone_number"].encode()
                ).decode()
            except Exception as e:
                print(f"Decryption error for field 'phone_number': {e}")
                representation["phone_number"] = None

        if "email" in representation and representation["email"]:
            try:
                representation["email"] = cipher_suite.decrypt(
                    representation["email"].encode()
                ).decode()
            except Exception as e:
                print(f"Decryption error for field 'email': {e}")
                representation["email"] = None

        return representation


class UserUpdateSerializer(serializers.ModelSerializer):
    profile_picture = serializers.ImageField(required=False, allow_null=True)
    password = serializers.CharField(write_only=True, required=False, allow_blank=True)
    enable_2fa = serializers.BooleanField(write_only=True, required=False)

    class Meta:
        model = User
        fields = [
            'first_name',
            'last_name',
            'phone_number',
            'email',
            'password',
            'address1',
            'address2',
            'zip_code',
            'state',
            'country',
            'profile_picture',
            'residency',
            'is_active',
            'enable_2fa'
        ]
        extra_kwargs = {
            'first_name': {'required': False},
            'last_name': {'required': False},
            'phone_number': {'required': False},
            'email': {'required': False},
            'password': {'write_only': True, 'required': False},
            'address1': {'required': False},
            'address2': {'required': False},
            'zip_code': {'required': False},
            'state': {'required': False},
            'country': {'required': False},
            'profile_picture': {'required': False},
            'residency': {'required': False},
            'is_active': {'required': False},
            'enable_2fa': {'write_only': True, 'required': False}
        }

    def validate_email(self, value):
        if value:
            # Get the current instance being updated
            instance = getattr(self, "instance", None)

            # Check if email exists for any other user
            existing_user = (
                User.objects.filter(private_email=generate_hmac_token(value))
                .exclude(id=instance.id if instance else None)
                .first()
            )

            if existing_user:
                raise serializers.ValidationError("This email is already registered.")

            # Check if user can update email this month
            contact_service = ContactUpdateService(instance)
            if not contact_service.can_update_contact('email'):
                raise serializers.ValidationError(
                    "You can only update your email once every 30 days")

        return value

    def validate_phone_number(self, value):
        if value:
            # Get the current instance being updated
            instance = getattr(self, "instance", None)

            # Check if phone exists for any other user
            existing_user = (
                User.objects.filter(private_phone=generate_hmac_token(value))
                .exclude(id=instance.id if instance else None)
                .first()
            )

            if existing_user:
                raise serializers.ValidationError(
                    "This phone number is already registered.")

            # Check if user can update phone this month
            contact_service = ContactUpdateService(instance)
            if not contact_service.can_update_contact('phone'):
                raise serializers.ValidationError(
                    "You can only update your phone number once every 30 days")

        return value

    def update(self, instance, validated_data):
        # Handle password update
        password = validated_data.pop("password", None)
        if password:
            instance.set_password(password)

        # Handle email update
        if "email" in validated_data:
            contact_service = ContactUpdateService(instance)
            try:
                contact_service.update_contact(
                    'email', validated_data["email"], instance)
            except ValueError as e:
                raise serializers.ValidationError(str(e))

        # Handle phone number update
        if "phone_number" in validated_data:
            contact_service = ContactUpdateService(instance)
            try:
                contact_service.update_contact(
                    'phone', validated_data["phone_number"], instance)
            except ValueError as e:
                raise serializers.ValidationError(str(e))

        # Handle profile picture
        profile_picture = validated_data.pop("profile_picture", None)
        if profile_picture:
            instance.profile_picture = profile_picture

        # Handle 2FA update
        enable_2fa = validated_data.pop("enable_2fa", None)
        if enable_2fa is not None:
            instance.two_fa_enabled = enable_2fa

        # Update all other fields
        for field in ["first_name", "last_name", "address1", "address2", "zip_code",
                      "state", "country", "residency", "is_active"]:
            if field in validated_data:
                setattr(instance, field, validated_data[field])

        instance.save()
        return instance


class InvitedUserProfileUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            'first_name',
            'last_name',
            'email',
            'role',
            'phone_number',
        ]
        extra_kwargs = {
            'first_name': {'required': False},
            'last_name': {'required': False},
            'email': {'required': False},
            'role': {'required': False},
            'phone_number': {'required': False},
        }

    def validate_email(self, value):
        if value:
            instance = getattr(self, "instance", None)
            if instance and value == instance.get_decrypted_email():
                return value
            existing_user = (
                User.objects.filter(private_email=generate_hmac_token(value))
                .exclude(id=instance.id if instance else None)
                .first()
            )
            if existing_user:
                raise serializers.ValidationError("This email is already registered.")
            contact_service = ContactUpdateService(instance)
            if not contact_service.can_update_contact('email'):
                raise serializers.ValidationError(
                    "Email can only be updated once every 30 days. Please try again later.")
        return value

    def validate_phone_number(self, value):
        if value:
            instance = getattr(self, "instance", None)
            if instance and value == instance.get_decrypted_phone_number():
                return value
            existing_user = (
                User.objects.filter(private_phone=generate_hmac_token(value))
                .exclude(id=instance.id if instance else None)
                .first()
            )
            if existing_user:
                raise serializers.ValidationError(
                    "This phone number is already registered.")
            contact_service = ContactUpdateService(instance)
            if not contact_service.can_update_contact('phone'):
                raise serializers.ValidationError(
                    "Phone number can only be updated once every 30 days. Please try again later.")
        return value

    def update(self, instance, validated_data):
        updated = False
        # Update first_name and last_name if provided and different
        for field in ['first_name', 'last_name', 'role']:
            if field in validated_data:
                new_value = validated_data[field]
                if new_value and getattr(instance, field) != new_value:
                    setattr(instance, field, new_value)
                    updated = True
        # Update email if provided and different
        if 'email' in validated_data:
            new_email = validated_data['email']
            if new_email and instance.get_decrypted_email() != new_email:
                contact_service = ContactUpdateService(instance)
                contact_service.update_contact('email', new_email)
                updated = True
        # Update phone_number if provided and different
        if 'phone_number' in validated_data:
            new_phone = validated_data['phone_number']
            if new_phone and instance.get_decrypted_phone_number() != new_phone:
                contact_service = ContactUpdateService(instance)
                contact_service.update_contact('phone', new_phone)
                updated = True
        if updated:
            instance.save()
        return instance
