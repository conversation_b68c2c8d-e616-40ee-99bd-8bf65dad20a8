from cryptography.fernet import Fernet
from django.conf import settings
from rest_framework import serializers

from accounts.models.organisation import Organization
from accounts.models.users import User
from utils_app.logger_utils import log_user_action
# from accounts.services.jwt_authentication.authentication import fernet_decrypt

cipher_suite = Fernet(settings.ENCRYPTION_KEY)


class UserListSerializer(serializers.ModelSerializer):

    class Meta:
        model = User
        fields = [
            "reference_id",
            "email",
            "first_name",
            "last_name",
            "phone_number",
            "role",
            "is_active",
            "date_joined",
        ]


class OrganizationSerializer(serializers.ModelSerializer):
    # users = UserListSerializer(many=True, read_only=True)
    users = (
        serializers.SerializerMethodField()
    )  # This will allow us to customize the users field
    user_count = serializers.SerializerMethodField()
    user_count_active = serializers.SerializerMethodField()
    user_permissions = serializers.SerializerMethodField()

    class Meta:
        model = Organization
        fields = [
            "id",
            "name",
            "number",
            "tax_id",
            "created_at",
            "is_primary",
            "is_active",
            "show_on_invoice",
            "created_by",
            "updated_at",
            "reference_id",
            "user_count",
            "users",
            "user_count_active",
            "user_permissions",
            "org_domain"
        ]
        # Number field is optional for updates - can be None, empty string, or a valid value
        extra_kwargs = {
            "number": {"required": False, "allow_blank": True, "allow_null": True},
        }

    def __init__(self, *args, **kwargs):
        # Ensure that request is passed to the serializer
        self.request = kwargs.get("context", {}).get("request", None)
        super().__init__(*args, **kwargs)

    def perform_create(self, serializer):
        """Override to add the logged-in user to the organization."""
        organization = serializer.save()  # Save the organization instance
        organization.users.add(
            self.request.user
        )  # Associate the user with the organization
        log_user_action(
            request=self.request,
            action='Organization created',
            status='success',
            details={'type': 'organization_created'}
        )

    def to_representation(self, instance):
        """Decrypt the title field when retrieving data."""
        representation = super().to_representation(instance)
        encrypted_name = instance.name
        encrypted_number = instance.number
        encrypted_tax_id = instance.tax_id
        if encrypted_name:
            try:
                decrypted_name = cipher_suite.decrypt(encrypted_name.encode()).decode()
                representation["name"] = decrypted_name
            except Exception:
                representation["name"] = None  # Handle any decryption errors
        if encrypted_number:
            try:
                decrypted_number = cipher_suite.decrypt(
                    encrypted_number.encode()
                ).decode()
                representation["number"] = decrypted_number
            except Exception:
                representation["number"] = None  #
        if encrypted_tax_id:
            try:
                representation["tax_id"] = cipher_suite.decrypt(
                    encrypted_tax_id.encode()).decode()
            except Exception:
                representation["tax_id"] = None
        return representation

    def get_users(self, instance):
        # Manually serialize each user and include the organization reference_id
        users_with_org_ref_id = []
        for user in instance.users.filter(is_deleted=False):
            if user and user.email:
                user.email = cipher_suite.decrypt(user.email.encode()).decode()
            if user and user.phone_number:
                user.phone_number = cipher_suite.decrypt(
                    user.phone_number.encode()
                ).decode()
            user_data = UserListSerializer(user).data
            user_data["organization_reference_id"] = (
                instance.reference_id
            )  # Add the organization reference_id to each user
            users_with_org_ref_id.append(user_data)
        return users_with_org_ref_id

    def get_user_count(self, instance):
        return instance.users.filter(is_deleted=False).count()

    def get_user_count_active(self, instance):
        return instance.total_active_user

    def get_user_permissions(self, instance):
        """Get user permissions based on their role."""
        request = self.context.get('request')
        if not request or not request.user:
            return {}

        user_role = request.user.role

        permissions = {
            'can_view': True,  # All authenticated users can view
            'can_manage_users': user_role in ['org_superadmin'],
            'can_invite_users': user_role in ['org_superadmin', 'org_admin'],
            'role': user_role,
            'is_readonly': user_role == 'org_member'
        }

        # Check if user belongs to this specific organization
        is_member = instance.users.filter(id=request.user.id).exists()
        permissions['is_member'] = is_member

        return permissions

    # def validate_name(self, value):
    #     """Validate the name before encrypting it."""
    #     if value and not value.strip():
    #         raise serializers.ValidationError("name cannot be blank.")
    #     return value

    def create(self, validated_data):
        """Encrypt the name during creation and add created_by."""
        name = validated_data.pop("name", None)
        number = validated_data.pop("number", None)
        tax_id = validated_data.pop("tax_id", None)
        validated_data["is_active"] = True
        created_by = (
            self.request.user.reference_id if self.request else None
        )  # Ensure request is available
        validated_data["created_by"] = created_by

        if name:
            validated_data["name"] = cipher_suite.encrypt(name.encode()).decode()
        if number:
            validated_data["number"] = cipher_suite.encrypt(number.encode()).decode()
        if tax_id:
            validated_data["tax_id"] = cipher_suite.encrypt(tax_id.encode()).decode()

        return super().create(validated_data)

    def validate(self, data):
        """Validate the organization data"""
        # Check if name exists and is not empty
        if 'name' in data and not data['name'].strip():
            raise serializers.ValidationError(
                {"name": "Organization name cannot be empty"})

        return data

    def update(self, instance, validated_data):
        """Decrypt the name and number fields and encrypt them before saving."""
        try:
            name = validated_data.get("name")
            number = validated_data.get("number")
            tax_id = validated_data.get("tax_id")
            org_domain = validated_data.get("org_domain")
            
            # Encrypt only if new values are provided and not empty
            if name is not None and name.strip():
                validated_data["name"] = cipher_suite.encrypt(name.encode()).decode()

            if number is not None and number.strip():
                validated_data["number"] = cipher_suite.encrypt(
                    number.encode()).decode()
            elif number == "":  # Handle empty string case
                validated_data["number"] = None  # Set to None in database

            if tax_id:  # 🔹 Add this
                validated_data["tax_id"] = cipher_suite.encrypt(
                    tax_id.encode()).decode()

            # Update the updated_by field
            if self.context.get('request'):
                validated_data["updated_by"] = self.context['request'].user.reference_id

            return super().update(instance, validated_data)
        except Exception as e:
            raise serializers.ValidationError(f"Error updating organization: {str(e)}")
