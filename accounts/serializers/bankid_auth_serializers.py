import base64
import logging
import os
import uuid
from typing import Dict

# import PyPDF2  # type: ignore
from rest_framework import serializers

from accounts.models.users import User
from documents.models.document_models import UserDocument
from documents.views.document_views import download_and_decrypt_from_s3

logger = logging.getLogger("app")


class AuthSerializer(serializers.Serializer):
    end_user_ip = serializers.IPAddressField()
    document_content = serializers.CharField(required=False, allow_null=True)
    flow_type = serializers.ChoiceField(
        choices=[('qr', 'QR Code'), ('in_device', 'In Device')],
        required=False,
        default='qr'
    )
    device_type = serializers.ChoiceField(
        choices=[('mobile', 'Mobile'), ('desktop', 'Desktop')],
        required=False,
        default='desktop'
    )
    return_url = serializers.URLField(required=False, allow_null=True)

    def to_representation(self, instance: Dict):
        result = {"end_user_ip": str(instance["end_user_ip"])}
        
        # Add flow type and device type to result
        result["flow_type"] = instance.get("flow_type", "qr")
        result["device_type"] = instance.get("device_type", "desktop")
        
        # Generate nonce and return URL for in-device flow
        if instance.get("flow_type") == "in_device":
            nonce = str(uuid.uuid4())
            base_return_url = instance.get("return_url")
            
            if base_return_url:
                host = f"{os.getenv('FE_BASE_URL')}"
                simple_return_url = f"{host}/bankid-callback/"
                
                # Add nonce parameter to the simple return URL
                result["return_url"] = f"{simple_return_url}?nonce={nonce}"
                result["nonce"] = nonce
                result["original_return_url"] = base_return_url  # Store original for later use
                logger.info(f"Generated simple return URL for BankID: {result['return_url']}")
            else:
                logger.warning("In-device flow selected but no return_url provided")

        # document_content = None
        if instance.get("document_content"):
            document = UserDocument.objects.get(
                reference_id=instance["document_content"]
            )
            user_obj = User.objects.get(reference_id=document.owner.reference_id)
            str_date_joined = user_obj.date_joined.isoformat()
            # document_response = get_document_or_404(reference_id)
            str_created_obj = document.created_at.isoformat()
            s3_url = document.document_file.name
            # s3_url = request.data["document_path"]
            final_str_datetime = str_date_joined + "/" + str_created_obj
            logger.debug(f"Generated final string for decryption: {final_str_datetime}")

            logger.info(
                f"""Attempting to download and decrypt the
                document data from S3 for reference_id: {document.reference_id}"""
            )
            decrypted_data = download_and_decrypt_from_s3(s3_url, final_str_datetime)
            encoded_file_data = base64.b64encode(decrypted_data).decode("utf-8")
            result["user_visible_data"] = encoded_file_data[:22500]
            result["document_content"] = instance["document_content"]
            logger.debug(
                f"Decrypted document data for reference_id: {document.reference_id}."
            )
        # if instance.get("document_content"):
        #     document = UserDocument.objects.get(
        #         reference_id=instance["document_content"]
        #     )

        #     logger.info("Fetching document ...")
        #     with document.document_file.file.open(mode="rb") as file:
        #         # file_content = file.read()

        #         # Extract summary from the document
        #         file_summary = self.extract_document_summary(file)
        #         # Base64 encode the summary
        #         result["user_visible_data"] = file_summary
        #         result["document_content"] = instance["document_content"]
        #         logger.info(
        #             f"""Document summary extracted for:
        #                     {instance['document_content']}"""
        #         )
        # else:
        #     logger.error("Document not found.")
        #     result["user_visible_data"] = base64.b64encode(
        #         "No document provided".encode("utf-8")
        #     ).decode("ascii")

        return result

    # def extract_document_summary(self, file):
    #     """
    #     Extracts text from a PDF file and returns the first 22500
    #     characters as a summary.
    #     You can adjust the logic for other document types.
    #     """
    #     try:
    #         logger.info("Extracting text from the document.")
    #         # Assuming the document is a PDF, we extract the text
    #         pdf_reader = PyPDF2.PdfReader(file)
    #         text = ""
    #         for page in pdf_reader.pages:
    #             text += page.extract_text() + "\n"

    #         # Create a summary (first 500 characters of text)
    #         summary_length = 22500
    #         summary = (
    #             text[:summary_length] + "..." if len(text) > summary_length else text
    #         )
    #         logger.info(
    #             f"Document summary extracted with length: {len(summary)} characters."
    #         )
    #         return summary

    #     except Exception as e:
    #         logger.error(f"Error extracting document summary: {e}")
    #         # If unable to extract the text, return a default summary
    #         return "Unable to extract summary from document."


class VerifySerializer(serializers.Serializer):
    verification_order_id = serializers.CharField(required=False, allow_null=True)
    nonce = serializers.CharField(required=False, allow_null=True)
    document_content = serializers.CharField(required=False, allow_null=True)
    
    def validate(self, data):
        """
        Validate that either verification_order_id or nonce is provided.
        """
        verification_order_id = data.get('verification_order_id')
        nonce = data.get('nonce')
        
        if not verification_order_id and not nonce:
            raise serializers.ValidationError(
                "Either verification_order_id or nonce must be provided."
            )
        
        if verification_order_id and nonce:
            raise serializers.ValidationError(
                "Only one of verification_order_id or nonce should be provided."
            )
        
        return data


class CancelSerializer(serializers.Serializer):
    verification_order_id = serializers.CharField()


class MailSerializer(serializers.Serializer):
    emails = serializers.CharField()
    document_content = serializers.FileField()
