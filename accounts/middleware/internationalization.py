from django.conf import settings
from django.http import JsonResponse
from django.utils import translation
from django.utils.translation import gettext as _
import logging

logger = logging.getLogger('app')


class TranslationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        try:
            path_parts = request.path.strip("/").split("/")
            language_prefix = path_parts[0]

            if language_prefix and language_prefix in dict(settings.LANGUAGES):
                translation.activate(language_prefix)
                logger.debug(
                    "Language activated",
                    extra={
                        'language': language_prefix,
                        'path': request.path
                    }
                )
            elif language_prefix == "media":
                translation.activate(settings.LANGUAGE_CODE)
            else:
                if language_prefix:
                    logger.warning(
                        "Unsupported language code",
                        extra={
                            'language': language_prefix,
                            'path': request.path
                        }
                    )
                    return JsonResponse(
                        {"error": _("Unsupported language code.")},
                        status=400
                    )
                translation.activate(settings.LANGUAGE_CODE)

            request.LANGUAGE_CODE = translation.get_language()
            response = self.get_response(request)
            translation.deactivate()
            return response

        except Exception as e:
            logger.error(
                "Translation middleware error",
                extra={
                    'path': request.path,
                    'error': str(e)
                },
                exc_info=True
            )
            raise
