# Generated by Django 5.1.1 on 2025-08-26 06:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0020_organization_add_on_user_limit"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="organization",
            options={
                "verbose_name": "Organization",
                "verbose_name_plural": "Organizations",
            },
        ),
        migrations.AddField(
            model_name="organization",
            name="tax_id_object",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="organization",
            name="tax_id_type",
            field=models.CharField(
                blank=True,
                choices=[
                    ("ad_nrt", "Andorra NRT"),
                    ("ae_trn", "UAE TRN"),
                    ("al_tin", "Albania TIN"),
                    ("am_tin", "Armenia TIN"),
                    ("ao_tin", "Angola TIN"),
                    ("ar_cuit", "Argentina CUIT"),
                    ("au_abn", "Australia ABN"),
                    ("au_arn", "Australia ARN"),
                    ("aw_tin", "Aruba TIN"),
                    ("az_tin", "Azerbaijan TIN"),
                    ("ba_tin", "Bosnia and Herzegovina TIN"),
                    ("bb_tin", "Barbados TIN"),
                    ("bd_bin", "Bangladesh BIN"),
                    ("bf_ifu", "Burkina Faso IFU"),
                    ("bg_uic", "Bulgaria UIC"),
                    ("bh_vat", "Bahrain VAT"),
                    ("bj_ifu", "Benin IFU"),
                    ("bo_tin", "Bolivia TIN"),
                    ("br_cnpj", "Brazil CNPJ"),
                    ("br_cpf", "Brazil CPF"),
                    ("bs_tin", "Bahamas TIN"),
                    ("by_tin", "Belarus TIN"),
                    ("ca_bn", "Canada BN"),
                    ("ca_gst_hst", "Canada GST/HST"),
                    ("ca_pst_bc", "Canada PST (BC)"),
                    ("ca_pst_mb", "Canada PST (MB)"),
                    ("ca_pst_sk", "Canada PST (SK)"),
                    ("ca_qst", "Canada QST"),
                    ("cd_nif", "DR Congo NIF"),
                    ("ch_uid", "Switzerland UID"),
                    ("ch_vat", "Switzerland VAT"),
                    ("cl_tin", "Chile TIN"),
                    ("cm_niu", "Cameroon NIU"),
                    ("cn_tin", "China TIN"),
                    ("co_nit", "Colombia NIT"),
                    ("cr_tin", "Costa Rica TIN"),
                    ("cv_nif", "Cape Verde NIF"),
                    ("de_stn", "Germany STN"),
                    ("do_rcn", "Dominican Republic RCN"),
                    ("ec_ruc", "Ecuador RUC"),
                    ("eg_tin", "Egypt TIN"),
                    ("es_cif", "Spain CIF"),
                    ("et_tin", "Ethiopia TIN"),
                    ("eu_oss_vat", "EU OSS VAT"),
                    ("eu_vat", "EU VAT"),
                    ("gb_vat", "UK VAT"),
                    ("ge_vat", "Georgia VAT"),
                    ("gn_nif", "Guinea NIF"),
                    ("hk_br", "Hong Kong BR"),
                    ("hr_oib", "Croatia OIB"),
                    ("hu_tin", "Hungary TIN"),
                    ("id_npwp", "Indonesia NPWP"),
                    ("il_vat", "Israel VAT"),
                    ("in_gst", "India GST"),
                    ("is_vat", "Iceland VAT"),
                    ("jp_cn", "Japan CN"),
                    ("jp_rn", "Japan RN"),
                    ("jp_trn", "Japan TRN"),
                    ("ke_pin", "Kenya PIN"),
                    ("kg_tin", "Kyrgyzstan TIN"),
                    ("kh_tin", "Cambodia TIN"),
                    ("kr_brn", "South Korea BRN"),
                    ("kz_bin", "Kazakhstan BIN"),
                    ("la_tin", "Laos TIN"),
                    ("li_uid", "Liechtenstein UID"),
                    ("li_vat", "Liechtenstein VAT"),
                    ("ma_vat", "Morocco VAT"),
                    ("md_vat", "Moldova VAT"),
                    ("me_pib", "Montenegro PIB"),
                    ("mk_vat", "North Macedonia VAT"),
                    ("mr_nif", "Mauritania NIF"),
                    ("mx_rfc", "Mexico RFC"),
                    ("my_frp", "Malaysia FRP"),
                    ("my_itn", "Malaysia ITN"),
                    ("my_sst", "Malaysia SST"),
                    ("ng_tin", "Nigeria TIN"),
                    ("no_vat", "Norway VAT"),
                    ("no_voec", "Norway VOEC"),
                    ("np_pan", "Nepal PAN"),
                    ("nz_gst", "New Zealand GST"),
                    ("om_vat", "Oman VAT"),
                    ("pe_ruc", "Peru RUC"),
                    ("ph_tin", "Philippines TIN"),
                    ("ro_tin", "Romania TIN"),
                    ("rs_pib", "Serbia PIB"),
                    ("ru_inn", "Russia INN"),
                    ("ru_kpp", "Russia KPP"),
                    ("sa_vat", "Saudi Arabia VAT"),
                    ("sg_gst", "Singapore GST"),
                    ("sg_uen", "Singapore UEN"),
                    ("si_tin", "Slovenia TIN"),
                    ("sn_ninea", "Senegal NINEA"),
                    ("sr_fin", "Suriname FIN"),
                    ("sv_nit", "El Salvador NIT"),
                    ("th_vat", "Thailand VAT"),
                    ("tj_tin", "Tajikistan TIN"),
                    ("tr_tin", "Turkey TIN"),
                    ("tw_vat", "Taiwan VAT"),
                    ("tz_vat", "Tanzania VAT"),
                    ("ua_vat", "Ukraine VAT"),
                    ("ug_tin", "Uganda TIN"),
                    ("us_ein", "US EIN"),
                    ("uy_ruc", "Uruguay RUC"),
                    ("uz_tin", "Uzbekistan TIN"),
                    ("uz_vat", "Uzbekistan VAT"),
                    ("ve_rif", "Venezuela RIF"),
                    ("vn_tin", "Vietnam TIN"),
                    ("za_vat", "South Africa VAT"),
                    ("zm_tin", "Zambia TIN"),
                    ("zw_tin", "Zimbabwe TIN"),
                ],
                help_text="Type of tax ID (e.g., eu_vat, in_gst, gb_vat)",
                max_length=20,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="tax_id_verification_status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("verified", "Verified"),
                    ("unverified", "Unverified"),
                ],
                default="pending",
                help_text="Verification status from Stripe (pending, verified, unverified)",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="tax_id_verification_updated_at",
            field=models.DateTimeField(
                blank=True,
                help_text="When the tax ID verification status was last updated",
                null=True,
            ),
        ),
        migrations.AddIndex(
            model_name="organization",
            index=models.Index(
                fields=["tax_id_verification_status"],
                name="accounts_or_tax_id__5a3e56_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="organization",
            index=models.Index(
                fields=["tax_id_type"], name="accounts_or_tax_id__963bc3_idx"
            ),
        ),
    ]
