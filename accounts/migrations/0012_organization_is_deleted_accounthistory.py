# Generated by Django 5.1.1 on 2025-06-26 17:00

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0011_alter_user_two_fa_enabled"),
        ("subscriptions", "0023_enterprisesubscriptionrequest_is_deleted_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="organization",
            name="is_deleted",
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name="AccountHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.Char<PERSON>ield(max_length=255)),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("role", models.Char<PERSON>ield(max_length=50)),
                ("deleted_at", models.DateTimeField()),
                ("plan_name", models.Char<PERSON>ield(blank=True, max_length=100, null=True)),
                (
                    "stripe_subscription_id",
                    models.Char<PERSON>ield(blank=True, max_length=255, null=True),
                ),
                ("plan_id", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "deleted_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="deleted_users",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="accounts.organization",
                    ),
                ),
                (
                    "subscription",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="subscriptions.usersubscription",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="account_history",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-deleted_at"],
            },
        ),
    ]
