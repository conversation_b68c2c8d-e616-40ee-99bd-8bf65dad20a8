# Generated by Django 5.1.1 on 2025-03-24 17:09

import accounts.models.users
import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="BlacklistedToken",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("token", models.CharField(max_length=1024, unique=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name="OneTimePassword",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("otp_code", models.CharField(max_length=10)),
                ("expiration_time", models.DateTimeField()),
                ("is_verified", models.BooleanField(default=False)),
                ("is_link_verified_email", models.BooleanField(default=False)),
                ("is_link_verified_phone", models.BooleanField(default=False)),
                ("is_phone_verified", models.BooleanField(default=False)),
                ("is_email_verified", models.BooleanField(default=False)),
                ("is_download", models.BooleanField(default=False)),
                ("is_sent", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                        unique=True,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=255, null=True, unique=True
                    ),
                ),
                (
                    "number",
                    models.CharField(
                        blank=True, max_length=255, null=True, unique=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reference_id",
                    models.CharField(
                        blank=True,
                        editable=False,
                        max_length=255,
                        null=True,
                        unique=True,
                    ),
                ),
                ("is_primary", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("created_by", models.CharField(blank=True, max_length=255, null=True)),
            ],
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                ("password", models.CharField(max_length=128, verbose_name="password")),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        error_messages={
                            "unique": "A user with that username already exists."
                        },
                        help_text="Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.",
                        max_length=150,
                        unique=True,
                        validators=[
                            django.contrib.auth.validators.UnicodeUsernameValidator()
                        ],
                        verbose_name="username",
                    ),
                ),
                (
                    "first_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="first name"
                    ),
                ),
                (
                    "last_name",
                    models.CharField(
                        blank=True, max_length=150, verbose_name="last name"
                    ),
                ),
                (
                    "email",
                    models.EmailField(
                        blank=True, max_length=254, verbose_name="email address"
                    ),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                        verbose_name="staff status",
                    ),
                ),
                (
                    "date_joined",
                    models.DateTimeField(
                        default=django.utils.timezone.now, verbose_name="date joined"
                    ),
                ),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                (
                    "reference_id",
                    models.CharField(editable=False, max_length=255, unique=True),
                ),
                ("private_number", models.CharField(max_length=100)),
                ("private_phone", models.CharField(max_length=100)),
                ("private_email", models.CharField(max_length=100)),
                (
                    "authentication_method",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("kyc", "KYC"),
                            ("otp", "OTP"),
                            ("sweden_bank_id", "Sweden Bank ID"),
                            ("email", "email"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("super_admin", "super_admin"),
                            ("app_admin", "app_admin"),
                            ("org_superadmin", "org_superadmin"),
                            ("org_admin", "org_admin"),
                            ("org_member", "org_member"),
                            ("others", "others"),
                        ],
                        default="org_superadmin",
                        max_length=50,
                    ),
                ),
                ("user_type", models.CharField(blank=True, max_length=50, null=True)),
                ("is_invited", models.BooleanField(default=False)),
                (
                    "phone_number",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("residency", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "stripe_customer_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("bank_id", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "organisation_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("address1", models.CharField(blank=True, max_length=255, null=True)),
                ("address2", models.CharField(blank=True, max_length=255, null=True)),
                ("zip_code", models.CharField(blank=True, max_length=20, null=True)),
                ("state", models.CharField(blank=True, max_length=100, null=True)),
                ("country", models.CharField(blank=True, max_length=100, null=True)),
                ("last_login", models.DateTimeField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "profile_picture",
                    models.ImageField(
                        blank=True, null=True, upload_to="profile_pictures/"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("deleted_by", models.TextField(blank=True, null=True)),
                ("is_deleted", models.BooleanField(default=False)),
                ("reactivated_at", models.DateTimeField(blank=True, null=True)),
                ("reactivated_by", models.TextField(blank=True, null=True)),
                (
                    "activation_token",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "activation_token_created_at",
                    models.DateTimeField(blank=True, null=True),
                ),
                (
                    "last_login_ip",
                    models.CharField(blank=True, max_length=45, null=True),
                ),
                ("meta_data", models.JSONField(blank=True, null=True)),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
                (
                    "organizations",
                    models.ManyToManyField(
                        blank=True, related_name="users", to="accounts.organization"
                    ),
                ),
            ],
            options={
                "ordering": ["-date_joined"],
            },
            managers=[
                ("objects", accounts.models.users.CustomUserManager()),
            ],
        ),
        migrations.CreateModel(
            name="SessionTrack",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("ip_address", models.CharField(max_length=45)),
                ("is_logged_in", models.BooleanField(default=True)),
                ("last_active", models.DateTimeField(auto_now=True)),
                ("last_logged_out_at", models.DateTimeField(blank=True, null=True)),
                ("user_agent", models.CharField(max_length=255)),
                (
                    "generated_token",
                    models.CharField(blank=True, max_length=1024, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "session_track",
            },
        ),
    ]
