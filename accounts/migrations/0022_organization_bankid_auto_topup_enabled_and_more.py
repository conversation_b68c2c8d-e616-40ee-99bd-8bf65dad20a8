# Generated by Django 5.1.1 on 2025-09-08 07:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0021_alter_organization_options_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="organization",
            name="bankid_auto_topup_enabled",
            field=models.BooleanField(
                default=True,
                help_text="Enable automatic token top-up when tokens are low",
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="bankid_auto_topup_quantity",
            field=models.IntegerField(
                default=100, help_text="Number of tokens to purchase during auto top-up"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="bankid_auto_topup_threshold",
            field=models.IntegerField(
                default=10, help_text="Minimum tokens before auto top-up triggers"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="bankid_last_topup_date",
            field=models.DateTimeField(
                blank=True, help_text="Last automatic token top-up date", null=True
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="bankid_tokens_purchased",
            field=models.IntegerField(
                default=0, help_text="Total BankID tokens purchased"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="bankid_tokens_remaining",
            field=models.IntegerField(
                default=0, help_text="Remaining BankID signature tokens"
            ),
        ),
        migrations.AddField(
            model_name="organization",
            name="bankid_tokens_used",
            field=models.IntegerField(default=0, help_text="Total BankID tokens used"),
        ),
    ]
