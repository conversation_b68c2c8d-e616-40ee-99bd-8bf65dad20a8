# Generated by Django 5.1.1 on 2025-05-27 11:19

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0003_organization_user_added_count"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserContactHistory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "contact_type",
                    models.CharField(
                        choices=[("email", "Email"), ("phone", "Phone Number")],
                        max_length=10,
                    ),
                ),
                ("old_value", models.CharField(max_length=255)),
                ("new_value", models.Char<PERSON>ield(max_length=255)),
                ("changed_at", models.DateTimeField(auto_now_add=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "changed_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="contact_changes_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="contact_history",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-changed_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "contact_type", "changed_at"],
                        name="accounts_us_user_id_b16788_idx",
                    ),
                    models.Index(
                        fields=["user", "contact_type", "is_active"],
                        name="accounts_us_user_id_4b5ed4_idx",
                    ),
                ],
            },
        ),
    ]
