import logging

import jwt  # type: ignore
# from django.conf import settings
from django.utils.translation import gettext as _
from rest_framework.permissions import AllowAny
from rest_framework.views import APIView

from accounts.services.jwt_authentication.authentication import (
    JWTTokenManager,
    fernet_decrypt,
)
from esign.utils.custom_response import api_response
from accounts.models.users import User

logger = logging.getLogger(__name__)


class RefreshTokenAPIView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            refresh_token = request.data.get("refresh_token")
            if not refresh_token:
                return api_response(
                    action="unauthorized_access",
                    message=_("Refresh token is required"),
                )

            jwt_manager = JWTTokenManager()
            # Decode the refresh token to get user reference_id
            reference_id = jwt_manager.decode_token(refresh_token)

            if not reference_id:
                return api_response(
                    action="unauthorized_access",
                    message=_("Invalid refresh token"),
                )

            user_obj = User.objects.filter(reference_id=reference_id).first()
            if not user_obj:
                return api_response(
                    action="unauthorized_access",
                    message=_("Invalid refresh token"),
                )

            # Generate new access token
            access_token = jwt_manager.generate_token(reference_id)
            refresh_token = jwt_manager.generate_token(
                reference_id, token_type="refresh"
            )
            return api_response(
                action="data_fetched",
                data={
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                },
                status="success",
            )

        except jwt.ExpiredSignatureError:
            return api_response(
                action="data_not_retrieved",
                message=_("Refresh token has expired"),
                status="error",
            )
        except jwt.InvalidTokenError:
            return api_response(
                action="unauthorized_access",
                message=_("Invalid refresh token"),
                status="error",
            )
        except Exception as e:
            return api_response(
                action="unauthorized_access",
                message=_(f"Failed to refresh token: {str(e)}"),
                status="error",
            )
