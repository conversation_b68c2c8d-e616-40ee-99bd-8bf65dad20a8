import logging
import os

from accounts.exceptions import FailedLoginAttemptException
from accounts.models.organisation import Organization
from accounts.models.otp import OneTimePassword
from accounts.models.users import SessionTrack, User
from accounts.serializers.email_auth_serializers import (
    EmailOtpVerifyViewSerilizer,
    LoginSerializer,
    PasswordChangeSerializer,
    UserRegistrationSerializer,
    UserUpdateSerializer,
)
from accounts.services.bankid_authentication.bankid_utils import (
    get_client_ip,
    get_country_info,
)
from accounts.services.jwt_authentication.authentication import (
    JWTTokenManager,
    fernet_decrypt,
    generate_hmac_token,
)
from accounts.services.mobile_authentication.mobile_utils import generate_otp
from cryptography.fernet import Fernet
from django.conf import settings

# from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import make_password
from django.core.cache import cache
from django.db import transaction
from django.db.models import Q
from django.utils import timezone
from django.utils.crypto import get_random_string
from django.utils.translation import gettext as _

from accounts.tasks import send_verification_email, send_welcome_email, send_welcome_user_email, \
    send_password_reset_otp_email, send_background_verification_email, send_register_user_email, \
    send_background_email
from documents.utils.jwt_verifier import JWTAccessTokenAuthentication
from dotenv import load_dotenv
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from esign.utils.custom_response import api_response
from rest_framework import generics
from rest_framework.exceptions import AuthenticationFailed, ValidationError
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

# from datetime import timedelta
from subscriptions.services.subscription_feature_service import (
    SubscriptionFeatureManager,
)
from utils_app.logger_utils import log_user_action
from accounts.services.contact_update_service import ContactUpdateService
from accounts.utils.account_deletion_cache import is_deletion_in_progress

logger = logging.getLogger("app")

cipher_suite = Fernet(settings.ENCRYPTION_KEY)

load_dotenv()


def save_user_details(email, is_onetime=False, request=None):
    context = {}
    jwt_manager = JWTTokenManager()

    existing_user = User.objects.filter(
        private_email=generate_hmac_token(email),
    ).first()
    # if request:
    #     ip_address = get_client_ip(request)
    # else:
    #     ip_address = None
    if existing_user:
        logger.info(f"User already exists with email: {email}")
        profile_updated = bool(
            existing_user.first_name
            and existing_user.last_name
            and existing_user.get_decrypted_email()
        )
        if not existing_user.organizations.filter(is_deleted=False).exists():
            organisation = Organization.objects.create(
                created_by=existing_user.reference_id, is_primary=True
            )
            existing_user.organizations.set([organisation])
            existing_user.save()
        else:
            # if existing_user.role == "org_superadmin":
            organisation = Organization.objects.filter(
                users__reference_id=existing_user.reference_id, is_deleted=False
            ).first()

        access_token = jwt_manager.generate_token(existing_user.reference_id)
        refresh_token = jwt_manager.generate_token(
            existing_user.reference_id, token_type="refresh"
        )

        # If this is a one-time user, append "onetime" to the access token
        logger.info("Generated access and refresh tokens for existing user.")
        if is_onetime:
            access_token = "onetime" + access_token

        existing_user.user_type = "otp"
        existing_user.save()

        context["user_details"] = {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "profile_updated": profile_updated,
            "first_name": (
                existing_user.first_name if existing_user.first_name else None
            ),
            "last_name": (existing_user.last_name if existing_user.last_name else None),
            "email": (
                fernet_decrypt(existing_user.email) if existing_user.email else None
            ),
            "phone_number": (
                fernet_decrypt(existing_user.phone_number)
                if existing_user.phone_number
                else None
            ),
            "user_refernce_id": existing_user.reference_id,
        }

        context["organisation_details"] = {
            "name": organisation.get_decrypted_name() if organisation.name else None,
            "number": organisation.get_decrypted_number()
            if organisation.number
            else None,
            "reference_id": (
                organisation.reference_id if organisation.reference_id else None
            ),
            "is_primary": organisation.is_primary if organisation.is_primary else None,
            "created_by": organisation.created_by if organisation.created_by else None,
        }
        return context

    new_user = User.objects.create(
        private_email=generate_hmac_token(email),
        authentication_method="email",
        username=generate_hmac_token(email),
        # private_number=generate_hmac_token(email),
    )
    organisation = Organization.objects.create(
        created_by=new_user.reference_id, is_primary=True, is_active=True
    )
    new_user.organizations.set([organisation])
    new_user.user_type = "otp"
    new_user.save()

    access_token = jwt_manager.generate_token(new_user.reference_id)
    refresh_token = jwt_manager.generate_token(
        new_user.reference_id, token_type="refresh"
    )
    if is_onetime:
        access_token = "onetime" + access_token
    profile_updated = False

    context["user_details"] = {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "profile_updated": profile_updated,
        "user_refernce_id": new_user.reference_id,
        "first_name": (new_user.first_name if new_user.first_name else None),
        "last_name": (new_user.last_name if new_user.last_name else None),
        "email": fernet_decrypt(new_user.email) if new_user.email else None,
        "phone_number": (
            fernet_decrypt(new_user.phone_number) if new_user.phone_number else None
        ),
    }
    print(f"organisation {organisation}")
    context["organisation_details"] = {
        "name": organisation.get_decrypted_name() if organisation.name else None,
        "number": organisation.get_decrypted_number() if organisation.number else None,
        "reference_id": (
            organisation.reference_id if organisation.reference_id else None
        ),
        "is_primary": organisation.is_primary if organisation.is_primary else None,
        "created_by": organisation.created_by if organisation.created_by else None,
    }
    return context


class UserVerificationRegistrationView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        email = kwargs.get("email", None)
        if not email:
            logger.warning("Email not provided.")
            return api_response(
                action="validation_error",
                message=_("Email is required."),
                status="error",
            )

        try:
            # Check if the user exists in the system
            hmac_email = generate_hmac_token(email)
            existing_user = User.objects.filter(private_email=hmac_email).first()

            if not existing_user:
                logger.warning(f"User with email {email} not found.")
                return api_response(
                    action="data_not_retrieved",
                    message=_("User with this email does not exist."),
                    status="error",
                )
            # Generate OTP
            otp = generate_otp()
            if not otp:
                logger.error("OTP generation failed for email: %s", email)
                return api_response(
                    action="data_not_created",
                    message=_("Failed to generate OTP. Please try again."),
                    status="failed",
                )
            # Handle OTP creation/update
            otp_user, created = OneTimePassword.objects.update_or_create(
                email=hmac_email,
                defaults={"otp_code": otp, "is_verified": False},
            )
            # Send OTP email
            try:
                send_background_verification_email.delay(
                    receiver=email, subject="Verify Email", otp=otp)
                logger.info(f"OTP sent to {email}")
            except Exception as e:
                logger.error("Error sending OTP email to %s: %s", email, str(e))
                return api_response(
                    action="data_not_created",
                    message=_("Failed to send OTP email. Please try again."),
                    status="failed",
                )

            return api_response(
                action="data_created",
                message=_("OTP sent successfully!"),
                status="success",
            )

        except Exception as e:
            logger.exception("Unexpected error: %s", str(e))  # Capture traceback
            return api_response(
                action="server_error",
                message=_("An unexpected error occurred. Please try again."),
                status="failed",
            )


class UserRegistrationView(APIView):
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        try:
            # Extract and validate email from request data
            signup_data = request.data.copy()
            email = signup_data.get("email")
            if not email:
                logger.warning("Email not provided for registration.")
                return api_response(
                    action="validation_error",
                    message=_("Email is required for registration."),
                    status="error",
                )

            if is_deletion_in_progress(email):
                return api_response(
                    action="validation_error",
                    message=_("Account deletion in progress. Please try again later."),
                    status="failed",
                )

            # Check if the user already exists in the system
            existing_user = User.objects.filter(
                private_email=generate_hmac_token(email),
                is_deleted=False
            ).first()

            if existing_user:
                # Find the OTP Instance for the existing user, and
                existing_otp_object = OneTimePassword.objects.filter(
                    email=generate_hmac_token(email)
                ).filter(
                    (Q(is_link_verified_email=True) | Q(is_phone_verified=True) | Q(
                        is_email_verified=True) | Q(is_link_verified_phone=True)) & Q(is_verified=True)
                )
                
                if existing_otp_object:
                    return api_response(
                        action="validation_error",
                        message=_("User with this email already exists."),
                        status="failed",
                    )

            # Check if we should send OTP via query parameter (e.g., ?send_otp=true)
            send_otp = request.query_params.get("send_otp", "false").lower() == "true"
            # If OTP should be sent (either during user registration or OTP request)
            otp = None
            if send_otp:
                otp = generate_otp()
                if not otp:
                    logger.error("OTP generation failed for email: %s", email)
                    return api_response(
                        action="data_not_created",
                        message=_("Failed to generate OTP. Please try again."),
                        status="failed",
                    )

                # Handle OTP creation for both existing and new users
                OneTimePassword.objects.update_or_create(
                    email=generate_hmac_token(email),
                    defaults={
                        "otp_code": otp,
                        "is_verified": False,
                        "is_link_verified_email": False,
                    },
                )

                # Send OTP email
                try:
                    send_register_user_email.delay(receiver=email,
                                                   subject="Your Skrivly Verification Code",
                                                   otp=otp)
                except Exception as e:
                    logger.error("Error sending OTP email to %s: %s", email, str(e))
                    return api_response(
                        action="data_not_created",
                        message=_("Failed to send OTP email. Please try again."),
                        status="failed",
                    )

            # If user does not exist, proceed with registration and user creation
            # if not existing_user:
                # Create the user (this would include creating the user record in your system)
                # You might want to create the user here based on the email, password, and other details

            return api_response(
                data={
                    "email": email,
                    **({"otp": otp} if settings.DEBUG and send_otp else {}),
                    "is_existing_user": False,  # New user
                },
                action="data_created",
                status="success",
                message=_(
                    "User registered successfully! An OTP email has been sent for verification."
                    if send_otp
                    else "User registered successfully!"
                ),
            )

        except Exception as e:
            logger.error(
                "Unexpected error during registration or OTP request: %s", str(e)
            )
            return api_response(
                action="error",
                message=_("An unexpected error occurred. Please try again."),
                status="failed",
            )


class EmailOtpVerifyView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = EmailOtpVerifyViewSerilizer
    authentication_classes = []
    permission_classes = [AllowAny]

    def get_object(self):
        email = self.kwargs.get("email")
        secured_email = generate_hmac_token(email)
        if not email:
            return api_response(
                action="validation_error",
                message="Email parameter is required.",
            )
        try:
            return OneTimePassword.objects.get(email=secured_email)
        except OneTimePassword.DoesNotExist:
            raise OneTimePassword("OTP instance not found.")

    def put(self, request, *args, **kwargs):
        # Check if authentication is required
        logged_in = request.query_params.get("logged_in", "false").lower() == "true"
        if logged_in:
            # Verify JWT token
            auth_header = request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                return api_response(
                    action="authentication_error",
                    message="Authentication token is required",
                    status="error"
                )

            try:
                # Verify the token using JWTAccessTokenAuthentication
                auth = JWTAccessTokenAuthentication()
                auth_result = auth.authenticate(request)
                if not auth_result:
                    return api_response(
                        action="authentication_error",
                        message="Invalid authentication token",
                        status="error"
                    )
                request.user = auth_result[0]  # Set the authenticated user
            except Exception as e:
                return api_response(
                    action="authentication_error",
                    message=str(e),
                    status="error"
                )

        email = self.kwargs.get("email")
        verify_link = request.query_params.get("verify_link", None)
        otp_instance = self.get_object()
        if not otp_instance:
            logger.error(f"OTP instance not found for email: {email}")
            return api_response(
                action="data_not_retrieved", message="OTP instance not found."
            )

        # Check for multiple users with the same email
        existing_users = User.objects.filter(
            private_email=generate_hmac_token(email), is_deleted=False)
        if existing_users.count() > 1:
            logger.warning(f"Multiple users found for email: {email}")
            return api_response(
                action="validation_error",
                message="Email already exists for multiple users.",
            )

        # Merge data for serializer validation
        merged_data = {
            "otp_code": request.data.get("otp_code"),
            "email": email,
        }
        serializer = self.get_serializer(otp_instance, data=merged_data)
        serializer.is_valid(raise_exception=True)

        otp_code = serializer.validated_data.get("otp_code")

        # Check OTP expiration
        if timezone.now() > otp_instance.expiration_time:
            logger.warning(f"OTP expired for email: {email}")
            return api_response(
                action="wrong_otp",
                message="The OTP has expired. Please request a new one.",
            )

        # Check OTP match
        if otp_instance.otp_code != otp_code:
            logger.warning(f"OTP mismatch for email: {email}")
            return api_response(
                action="wrong_otp",
                message="Invalid OTP. Please try again.",
            )

        # Check if OTP is already verified
        if (
            otp_instance.is_verified
            and otp_instance.is_link_verified_email
            and not verify_link
        ):
            logger.warning(f"OTP already verified for email: {email}")
            return Response({
                "data": None,
                "status": "failed",
                "message": "This OTP has already been used. Please request a new one.",
                "status_code": 400
            }, status=400)

        # Mark OTP as verified
        if not verify_link:
            otp_instance.is_verified = True
            otp_instance.is_link_verified_email = True
        else:
            otp_instance.is_link_verified_email = True
            otp_instance.is_email_verified = True

        otp_instance.save()

        logger.info(f"OTP verified for email: {email}")
        # If user exists, generate and return login tokens (login flow)
        user_exists = User.objects.filter(
            private_email=generate_hmac_token(email)).exists()
        if user_exists:
            token = save_user_details(email, request=request)
            if not token:
                return api_response(
                    action="data_not_created",
                    status="failed",
                    message=_("Failed to generate token. Please try again."),
                )
            return api_response(
                data=token,
                message="OTP verified successfully.",
                action="data_updated",
                status="success",
            )
        # For registration, just mark OTP as verified
        return api_response(
            data=None,
            message="OTP verified successfully.",
            action="data_updated",
            status="success",
        )


class UserUpdateView(APIView):
    permission_classes = [AllowAny]

    def put(self, request, *args, **kwargs):
        email = request.query_params.get("email")
        password = request.data.get("password")
        phone_number = request.data.get("phone_number")

        # Initial validation checks
        if not password:
            return api_response(
                action="validation_error",
                message="Password is required",
                status="failed"
            )
        if not email:
            return api_response(
                action="validation_error",
                message="Email is required",
                status="failed"
            )

        try:
            # Pre-transaction validations
            secured_email = generate_hmac_token(email)
            secured_phone = generate_hmac_token(phone_number) if phone_number else None

            # Check OTP verification
            otp_instance = OneTimePassword.objects.filter(email=secured_email).first()
            if not otp_instance or not otp_instance.is_link_verified_email:
                return api_response(
                    action="data_not_retrieved",
                    message="Email verification not completed."
                )

            # Check if email or phone number exists
            email_user = User.objects.filter(
                private_email=secured_email, is_deleted=False).first()
            phone_user = User.objects.filter(
                private_phone=secured_phone, is_deleted=False).first() if secured_phone else None

            if email_user:
                return api_response(
                    action="validation_error",
                    message="Email is already registered.",
                    status="failed"
                )
            if phone_user:
                return api_response(
                    action="validation_error",
                    message="Phone number is already registered.",
                    status="failed"
                )

            # Prepare registration data
            registration_data = {
                "email": email,
                "authentication_method": "email",
                "username": email,
                "password": password,
            }

            # Validate registration data before transaction
            registration_serializer = UserRegistrationSerializer(data=registration_data)
            if not registration_serializer.is_valid():
                return api_response(
                    action="validation_error",
                    message=registration_serializer.errors,
                    status="failed"
                )

            # Start transaction after all validations pass
            with transaction.atomic():
                # Create user
                user = registration_serializer.save()

                # Update OTP verification status
                otp_instance.is_email_verified = True
                otp_instance.save()

                # Update user details
                update_serializer = UserUpdateSerializer(
                    instance=user,
                    data=request.data,
                    partial=True
                )
                if not update_serializer.is_valid():
                    raise ValidationError(update_serializer.errors)

                user = update_serializer.save()

                # Generate token
                token = save_user_details(
                    user.get_decrypted_email(),
                    is_onetime=False,
                    request=request
                )
                if not token:
                    raise ValidationError("Failed to generate token")

                # Create session
                SessionTrack.objects.create(
                    user=user,
                    ip_address=get_client_ip(request),
                    is_logged_in=True,
                    generated_token=token["user_details"]["access_token"]
                )

                # if not was_soft_deleted:
                # Setup trial subscription
                subscribed_trial = SubscriptionFeatureManager(user)
                token["subscription"] = subscribed_trial.get_active_subscription_features()
                # else:
                #     token["subscription"] = None

                # Send welcome email
                send_welcome_user_email.delay(
                    receiver_name=user.get_full_name(),
                    receiver=user.get_decrypted_email(),
                    subject="Welcome to Skrivly"
                )

                # Log success
                log_user_action(
                    request=request,
                    user=user,
                    action="User registered and updated successfully.",
                    status="success",
                    details={"type": "user_created"}
                )

                return api_response(
                    data={
                        **update_serializer.data,
                        **token,
                        "email": user.get_decrypted_email(),
                    },
                    message="User registered and updated successfully.",
                    action="data_created",
                    status="success"
                )

        except ValidationError as e:
            log_user_action(
                request=request,
                action="User registration failed",
                status="failed",
                details={"type": "validation_error", "error": str(e)}
            )
            return api_response(
                action="validation_error",
                message=str(e),
                status="failed"
            )
        except Exception as e:
            logger.error(f"User registration failed: {str(e)}")
            return api_response(
                action="server_error",
                message="Registration failed. Please try again.",
                status="failed"
            )


class UserLoginView(APIView):
    def post(self, request, *args, **kwargs):
        errors = None
        try:

            serializer = LoginSerializer(data=request.data)

            # Validate serializer first
            if not serializer.is_valid():
                # Check for special OTP-required response
                err = serializer.errors
                if (
                    isinstance(err, dict)
                    and err.get("status") == ["success"]
                    and err.get("status_code") == ["200"]
                ):
                    # Get user by email to include enabled_2fa
                    email = request.data.get("email")
                    private_email = generate_hmac_token(email)
                    user = User.objects.filter(private_email=private_email).first()
                    enabled_2fa = getattr(user, "two_fa_enabled",
                                          True) if user else True
                    return Response({
                        "data": None,
                        "status": "success",
                        "message": err.get("message", [""])[0],
                        "status_code": 200,
                        "enabled_2fa": enabled_2fa
                    }, status=200)
                errors = serializer.errors
                return api_response(
                    action="data_not_retrieved",
                    status="failed",
                    message=serializer.errors,
                    data=serializer.errors,
                )

            user = serializer.validated_data["user"]

            token = save_user_details(user.get_decrypted_email(), is_onetime=False)

            otp_instance = OneTimePassword.objects.filter(
                email=generate_hmac_token(user.email)
            ).first()

            if otp_instance and not otp_instance.is_link_verified_email:
                return api_response(
                    action="data_not_retrieved", message="Please Verify your otp"
                )

            if not token:
                logger.error(f"Token generation failed for user: {user.email}")
                return api_response(
                    action="data_not_created",
                    status="failed",
                    message=_("Failed to generate token. Please try again."),
                )

            # Check session after token generation
            user_detail_dict = token.get("user_details", None)

            user_reference_id = user_detail_dict.get("user_refernce_id")
            token["user_details"]["meta_data"] = get_country_info()
            user = User.objects.filter(reference_id=user_reference_id).first()

            # Setup trial subscription
            subscribed_trial = SubscriptionFeatureManager(user)
            token["subscription"] = subscribed_trial.get_active_subscription_features()

            if user:
                existing_session = SessionTrack.objects.filter(
                    user=user,
                    is_logged_in=True,
                ).first()

                if existing_session:
                    return api_response(
                        data={"user_reference_id": user_reference_id},
                        action="validation_error",
                        message="Please logout from other devices and try again.",
                    )

                # Create new session
                SessionTrack.objects.update_or_create(
                    user=user,
                    ip_address=get_client_ip(request),
                    is_logged_in=True,
                    generated_token=token["user_details"]["access_token"],
                )

                log_user_action(
                    request=request,
                    user=user,
                    action="User login successful.",
                    status="success",
                    details={"type": "user_login"},
                )

            return api_response(
                action="data_retrieved",
                status="success",
                message=_("Login successful."),
                data=token,
            )

        except FailedLoginAttemptException as e:
            return api_response(
                action="data_not_retrieved",
                status="failed",
                message=str(e),
                data={
                    "attempts": getattr(e, 'attempt', None),
                }

            )
        except AuthenticationFailed as e:

            return api_response(
                action="data_not_retrieved",
                status="failed",
                message=str(e),
            )
        except Exception as e:
            logger.error(f"Unexpected error during login: {str(e)}")
            return api_response(
                action="server_error",
                status="failed",
                message=_("An unexpected error occurred. Please try again."),
            )


class PasswordChangeView(generics.UpdateAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = PasswordChangeSerializer

    def get_object(self):
        # We are assuming the user is authenticated and has an associated OTP
        email = self.request.user.get_decrypted_email()  # Or from the OTP instance
        try:
            secured_email = generate_hmac_token(email)
            return OneTimePassword.objects.get(email=secured_email, is_verified=True)
        except OneTimePassword.DoesNotExist:
            return api_response(
                action="validation_error",
                message="User is not verified.",
            )

    def update(self, request, *args, **kwargs):
        # otp_instance = self.get_object()
        # if not otp_instance:
        #     return api_response(
        #         action="validation_error",
        #         message="User is not verified.",
        #     )
        try:
            user = User.objects.get(
                private_email=generate_hmac_token(
                    self.request.user.get_decrypted_email()
                )
            )  # Fetch user by OTP email

            # Validate password change
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # Verify old password
            if not user.check_password(serializer.validated_data["old_password"]):
                return api_response(
                    action="data_not_updated",
                    message="Current password is incorrect.",
                )

            # Change user password
            new_password = serializer.validated_data["new_password"]
            user.set_password(new_password)
            user.failed_login_attempts = 0
            user.is_locked = False
            user.locked_at = None
            user.save()

            log_user_action(
                request=request,
                action="Password changed successfully.",
                status="success",
                details={"type": "password_changed"},
            )

            return api_response(
                action="data_updated",
                status="success",
                message="Password Successfully Changed.",
            )
        except User.DoesNotExist:
            return api_response(
                action="data_not_retrieved",
                message="User not found.",
            )
        except Exception as e:
            return api_response(
                action="server_error", message="Failed to change password.", status=500
            )


class ForgotPasswordView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        """Send OTP for password reset"""
        email = request.data.get("email")
        if not email:
            return api_response(
                action="validation_error", message="Email is required.", status="error"
            )

        # Check if user exists
        try:
            user = User.objects.get(private_email=generate_hmac_token(email))
        except User.DoesNotExist:
            return api_response(
                action="validation_error",
                message="No user found with this email address.",
                status="error",
            )

        # Generate OTP
        otp = generate_otp()

        # Store OTP in OneTimePassword model
        OneTimePassword.objects.update_or_create(
            email=generate_hmac_token(email),
            defaults={
                "otp_code": otp,
                "expiration_time": timezone.now() + timezone.timedelta(minutes=10),
                "is_verified": False,
                "is_link_verified_email": True,  # Keep existing verification status
            },
        )

        # Send email with OTP
        try:
            send_password_reset_otp_email.delay(
                receiver=email,
                subject="Password Reset OTP",
                otp=otp,
                user_name=user.get_full_name()
            )

        except Exception as e:
            logger.error("Error sending OTP email to %s: %s", email, str(e))
            return api_response(
                action="data_not_created",
                message="Failed to send OTP. Please try again.",
                status="failed",
            )

        return api_response(
            action="data_created",
            message="OTP has been sent to your email.",
            status="success",
        )


class VerifyPasswordResetOTP(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        """Verify OTP for password reset"""
        email = request.data.get("email")
        otp = request.data.get("otp")
        if not all([email, otp]):
            return api_response(
                action="validation_error",
                message="Email and OTP are required.",
                status="error",
            )

        try:
            otp_obj = OneTimePassword.objects.filter(
                email=generate_hmac_token(email),
                otp_code=otp,
                expiration_time__gt=timezone.now(),
            ).first()

            if not otp_obj:
                return api_response(
                    action="validation_error",
                    message="Invalid or expired OTP.",
                    status="error",
                )

            # Mark OTP as verified for password reset
            otp_obj.is_verified = True
            otp_obj.save()

            # Generate reset token for the next step
            reset_token = get_random_string(64)
            cache_key = f"password_reset_{reset_token}"

            # Store token and email in cache
            cache.set(cache_key, email, timeout=600)  # 10 minutes expiry

            return api_response(
                action="data_retrieved",
                message="OTP verified successfully.",
                status="success",
                data={"reset_token": reset_token},
            )

        except Exception as e:
            logger.error(f"Error verifying OTP: {str(e)}")
            return api_response(
                action="validation_error",
                message="Failed to verify OTP. Please try again.",
                status="error",
            )


class ResetPasswordView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        """Reset password after OTP verification"""
        token = request.data.get("token")
        new_password = request.data.get("new_password")
        if not all([token, new_password]):
            return api_response(
                action="validation_error",
                message="Token and new password are required.",
                status="error",
            )

        # Validate password
        if len(new_password) < 8:
            return api_response(
                action="validation_error",
                message="Password must be at least 8 characters long.",
                status="error",
            )

        # Get email from cache
        cache_key = f"password_reset_{token}"
        email = cache.get(cache_key)

        if not email:
            return api_response(
                action="validation_error",
                message="Invalid or expired reset token. Please try again.",
                status="error",
            )

        # Verify if OTP was verified
        try:
            otp_obj = OneTimePassword.objects.get(
                email=generate_hmac_token(email),
                is_verified=True,
                expiration_time__gt=timezone.now(),
            )
        except OneTimePassword.DoesNotExist:
            return api_response(
                action="validation_error",
                message="OTP verification required before password reset.",
                status="error",
            )

        try:
            # Get user and reset password
            user = User.objects.get(private_email=generate_hmac_token(email))
            user.set_password(new_password)
            user.failed_login_attempts = 0
            user.is_locked = False
            user.locked_at = None
            user.save()

            # Clean up
            cache.delete(cache_key)

            # Instead of deleting the OTP, just mark it as used
            otp_obj.is_verified = True
            otp_obj.is_link_verified_email = True  # Preserve email verification status
            otp_obj.save()

            return api_response(
                action="data_updated",
                message="Password has been reset successfully.",
                status="success",
            )

        except User.DoesNotExist:
            return api_response(
                action="validation_error",
                message="User not found.",
                status="error"
            )


class DocumentOtpVerifyView(generics.RetrieveUpdateDestroyAPIView):
    # authentication_classes = [JWTAccessTokenAuthentication]
    # permission_classes = [IsAuthenticated]
    serializer_class = EmailOtpVerifyViewSerilizer

    def get_object(self):
        email = self.kwargs.get("email")
        secured_email = generate_hmac_token(email)

        if not email:
            return api_response(
                action="validation_error",
                message="Email parameter is required.",
            )
        try:
            return OneTimePassword.objects.get(email=secured_email)
        except OneTimePassword.DoesNotExist:
            raise OneTimePassword("OTP instance not found.")

    def put(self, request, *args, **kwargs):
        email = self.kwargs.get("email")
        is_verify = request.query_params.get("is_verify", None)
        otp_instance = self.get_object()
        if not otp_instance:
            logger.error(f"OTP instance not found for email: {email}")
            return api_response(
                action="data_not_retrieved", message="OTP instance not found."
            )

        # Check for multiple users with the same email
        # existing_users = User.objects.filter(private_email=generate_hmac_token(email))
        # if existing_users.count() > 1:
        #     logger.warning(f"Multiple users found for email: {email}")
        #     return api_response(
        #         action="validation_error",
        #         message="Email already exists for multiple users.",
        #     )

        # exPisting_user = existing_users.first()
        # profile_updated = bool(
        #     existing_users and existing_users.first_name and existing_users.last_name and
        #     existing_users.email and existing_users.password
        # )

        # Merge data for serializer validation
        merged_data = {
            "otp_code": request.data.get("otp_code"),
            "email": email,
        }
        serializer = self.get_serializer(otp_instance, data=merged_data)
        serializer.is_valid(raise_exception=True)

        otp_code = serializer.validated_data.get("otp_code")

        # Check OTP `expiration`
        if timezone.now() > otp_instance.expiration_time:
            logger.warning(f"OTP expired for email: {email}")
            return api_response(
                action="validation_error",
                message="The OTP has expired. Please request a new one.",
            )

        # Check OTP match
        if otp_instance.otp_code != otp_code:
            logger.warning(f"OTP mismatch for email: {email}")
            return api_response(
                action="validation_error",
                message="Invalid OTP. Please try again.",
            )

        # Check if OTP is already verified
        if is_verify == "true":
            if otp_instance.is_verified:
                logger.warning(f"OTP already verified for email: {email}")
                return api_response(
                    action="validation_error",
                    message="This OTP has already been used. Please request a new one.",
                    status_code=400
                )

            # Mark OTP as verified
            otp_instance.is_verified = True
            otp_instance.save()

            logger.info(f"OTP verified for email: {email}")
            token = save_user_details(email, request=request)
            token["user_details"]["user_type"] = "otp"
            # Check if token was successfully generated
            if not token:
                logger.error(f"Token generation failed for user: {email}")
                return api_response(
                    action="data_not_created",
                    status="failed",
                    message=_("Failed to generate token. Please try again."),
                )

            # Send the token back in the response
            return api_response(
                action="data_retrieved",
                status="success",
                message=_("Login successful."),
                data=token,
            )
        elif is_verify == "false":
            otp_instance.is_download = True
            otp_instance.save()
            # Send the token back in the response
            return api_response(
                action="data_retrieved",
                status="success",
                message=_("Verification successful."),
                # data=token,
            )


class ContactHistoryView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get the contact update history for the authenticated user.
        Query parameters:
        - contact_type: Optional. Filter by contact type ('email' or 'phone')
        """
        contact_type = request.query_params.get('contact_type')

        contact_service = ContactUpdateService(request.user)
        history = contact_service.get_contact_history(contact_type)

        return Response({
            'status': 'success',
            'data': history
        }, status=status.HTTP_200_OK)


class EmailChangeVerifyView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAccessTokenAuthentication]

    def post(self, request, *args, **kwargs):
        """
        Send OTP to both old and new email addresses for verification
        """
        new_email = request.data.get('new_email')

        try:
            # Get user's current email
            current_email = request.user.get_decrypted_email()
            if not current_email:
                logger.error(
                    f"Email change failed - No existing email found for user: {request.user.id}")
                return api_response(
                    action="validation_error",
                    message="No existing email found",
                    status="error"
                )

            # Validate new email
            if not new_email:
                logger.error(
                    f"Email change failed - New email not provided for user: {request.user.id}")
                return api_response(
                    action="validation_error",
                    message="New email is required",
                    status="error"
                )

            # Check if user is org_superadmin
            if request.user.role != "org_superadmin":
                logger.error(
                    f"Email change failed - User is not org_superadmin: {request.user.id}")
                return api_response(
                    action="validation_error",
                    message="You do not have permission to change your email address",
                    status="error"
                )

            # Check if new email is already registered
            if User.objects.filter(private_email=generate_hmac_token(new_email), is_deleted=False).exists():
                logger.error(
                    f"Email change failed - Email already registered: {new_email}")
                return api_response(
                    action="validation_error",
                    message="This email is already registered",
                    status="error"
                )

            # Check if user can update contact
            try:
                contact_service = ContactUpdateService(user=request.user)
                if not contact_service.can_update_contact(contact_type='email'):
                    logger.error(
                        f"Email change failed - 30-day restriction not met for user: {request.user.id}")
                    return api_response(
                        action="validation_error",
                        message="You can only update your email once every 30 days",
                        status="error"
                    )
            except Exception as e:
                logger.error(
                    f"Error checking contact update eligibility - User: {request.user.id} - Error: {str(e)}")
                return api_response(
                    action="server_error",
                    message="Failed to check contact update eligibility. Please try again.",
                    status="error"
                )

            # Generate OTPs
            current_otp = generate_otp()
            new_otp = generate_otp()
            logger.info(f"Generated OTPs for email change - User: {request.user.id}")

            # Send OTPs via email
            try:
                # Send OTP to current email
                send_background_email.delay(
                    current_email,
                    otp=current_otp,
                    status='email_change_current_otp',  # New status for current email OTP
                    receiver_name=request.user.get_full_name() or 'User'
                )

                # Send OTP to new email
                send_background_email.delay(
                    new_email,
                    otp=new_otp,
                    status='email_change_new_otp',  # New status for new email OTP
                    receiver_name=request.user.get_full_name() or 'User'
                )

                # Store OTPs in OneTimePassword model
                # For current email
                otp_obj, _ = OneTimePassword.objects.update_or_create(
                    email=generate_hmac_token(current_email),
                    defaults={
                        'otp_code': current_otp,
                        'expiration_time': timezone.now() + timezone.timedelta(minutes=10),
                        'is_verified': False,
                    }
                )

                # For new email
                new_otp_obj, _ = OneTimePassword.objects.update_or_create(
                    email=generate_hmac_token(new_email),
                    defaults={
                        'otp_code': new_otp,
                        'expiration_time': timezone.now() + timezone.timedelta(minutes=10),
                        'is_verified': False,
                        'is_link_verified_email': False
                    }
                )

                logger.info(
                    f"OTPs stored successfully for email change - User: {request.user.id}")

                return api_response(
                    action="data_created",
                    message="OTPs sent successfully",
                    status="success",
                    data={
                        'current_email': current_email,
                        # 'current_otp': current_otp,
                        'new_email': new_email,
                        # 'new_otp': new_otp,
                        'expires_in': '10 minutes'
                    }
                )

            except Exception as e:
                logger.error(
                    f"Failed to send OTPs for email change - User: {request.user.id} - Error: {str(e)}")
                return api_response(
                    action="data_not_created",
                    message="Failed to send OTPs. Please try again.",
                    status="failed"
                )

        except Exception as e:
            logger.error(
                f"Unexpected error in email verification - User: {request.user.id} - Error: {str(e)}")
            return api_response(
                action="server_error",
                message="An unexpected error occurred",
                status="failed"
            )

    def put(self, request, *args, **kwargs):
        """
        Verify OTPs for both old and new email addresses and update contact history
        """
        current_otp = request.data.get('current_otp')
        new_otp = request.data.get('new_otp')
        new_email = request.data.get('new_email')

        if not all([current_otp, new_otp, new_email]):
            logger.error(
                f"Email verification failed - Missing required parameters for user: {request.user.id}")
            return api_response(
                action="validation_error",
                message="Current OTP, new OTP, and new email are required",
                status="error"
            )

        try:

            # Check if user is org_superadmin
            if request.user.role != "org_superadmin":
                logger.error(
                    f"Email change failed - User is not org_superadmin: {request.user.id}")
                return api_response(
                    action="validation_error",
                    message="You do not have permission to change your email address",
                    status="error"
                )

            current_email = request.user.get_decrypted_email()

            # Verify OTP for current email
            current_otp_obj = OneTimePassword.objects.filter(
                email=generate_hmac_token(current_email),
                otp_code=current_otp,
                expiration_time__gt=timezone.now(),
                is_verified=False
            ).first()

            if not current_otp_obj:
                logger.error(
                    f"Email verification failed - Invalid/expired OTP for current email - User: {request.user.id}")
                return api_response(
                    action="validation_error",
                    message="Invalid or expired OTP for current email",
                    status="error"
                )

            # Verify OTP for new email
            new_otp_obj = OneTimePassword.objects.filter(
                email=generate_hmac_token(new_email),
                otp_code=new_otp,
                expiration_time__gt=timezone.now(),
                is_verified=False
            ).first()

            if not new_otp_obj:
                logger.error(
                    f"Email verification failed - Invalid/expired OTP for new email - User: {request.user.id}")
                return api_response(
                    action="validation_error",
                    message="Invalid or expired OTP for new email",
                    status="error"
                )

            # Mark OTPs as verified
            current_otp_obj.is_verified = True
            current_otp_obj.save()

            new_otp_obj.is_verified = True
            new_otp_obj.is_link_verified_email = True
            new_otp_obj.save()

            logger.info(
                f"OTPs verified successfully for email change - User: {request.user.id}")

            # Update user's email and save contact history
            try:
                contact_service = ContactUpdateService(request.user)
                if not contact_service.can_update_contact(contact_type='email'):
                    logger.error(
                        f"Email update failed - 30-day restriction not met - User: {request.user.id}")
                    return api_response(
                        action="validation_error",
                        message="You can only update your email once every 30 days",
                        status="error"
                    )

                # Update email
                try:
                    logger.info(
                        f"Updating email to: {new_email} for user: {request.user.id}")
                    contact_service.update_contact(
                        contact_type='email',
                        new_value=new_email
                    )
                    logger.info(
                        f"Email updated and contact history saved successfully - User: {request.user.id}")
                except Exception as e:
                    logger.error(
                        f"Failed to update contact - User: {request.user.id} - Error: {str(e)}")
                    return api_response(
                        action="server_error",
                        message="Failed to update email. Please try again.",
                        status="error"
                    )

                return api_response(
                    action="data_updated",
                    message="Email updated successfully",
                    status="success",
                    data={
                        'email': new_email
                    }
                )

            except Exception as e:
                logger.error(
                    f"Failed to update email - User: {request.user.id} - Error: {str(e)}")
                return api_response(
                    action="server_error",
                    message="Failed to update email. Please try again.",
                    status="error"
                )

        except Exception as e:
            logger.error(
                f"Unexpected error in email verification - User: {request.user.id} - Error: {str(e)}")
            return api_response(
                action="server_error",
                message="An unexpected error occurred",
                status="failed"
            )


class LoginOtpVerifyView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get("email")
        otp_code = request.data.get("otp_code")
        if not email or not otp_code:
            return api_response(
                action="validation_error",
                message="Email and OTP are required.",
                status="error",
            )

        private_email = generate_hmac_token(email)
        otp_instance = OneTimePassword.objects.filter(email=private_email).first()
        if not otp_instance:
            return api_response(
                action="wrong_otp",
                message="OTP not found. Please request a new one.",
                status="error",
            )
        if timezone.now() > otp_instance.expiration_time:
            return api_response(
                action="wrong_otp",
                message="The OTP has expired. Please request a new one.",
                status="error",
            )
        if otp_instance.otp_code != otp_code:
            return api_response(
                action="wrong_otp",
                message="Invalid OTP. Please try again.",
                status="error",
            )
        if otp_instance.is_verified:
            return api_response(
                action="wrong_otp",
                message="This OTP has already been used. Please request a new one.",
                status="error",
            )
        # Mark OTP as verified
        otp_instance.is_verified = True
        otp_instance.is_link_verified_email = True
        otp_instance.save()
        # Generate and return login tokens
        token = save_user_details(email, request=request)
        token["user_details"]["user_type"] = "otp"
        if not token:
            return api_response(
                action="data_not_created",
                status="failed",
                message=_("Failed to generate token. Please try again."),
            )
        return api_response(
            action="data_retrieved",
            status="success",
            message=_("Login successful."),
            data=token,
        )
