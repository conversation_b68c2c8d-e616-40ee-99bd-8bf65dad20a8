from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
import base64
import logging
import time

from django.core.cache import cache

# from django.shortcuts import get_object_or_404
from django.utils.translation import gettext as _
from django.views.generic import TemplateView
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.permissions import AllowAny
from rest_framework.views import APIView

# from accounts.constants.config_constants import QR_WEBSOCKET_URL
from accounts.models.users import User
from accounts.services.jwt_authentication.authentication import JWTTokenManager
from accounts.serializers.bankid_auth_serializers import (  # MailSerializer,
    AuthSerializer,
    CancelSerializer,
    VerifySerializer,
)
from accounts.services.bankid_authentication.bankid_client import (
    bankid_authenticate,
    cancel_authentication,
    collect_status,
    sign_document,
    generate_autostart_url,
)
from accounts.services.bankid_authentication.bankid_utils import (  # send_email,
    get_bankid_message,
    get_client_ip,
    handle_user_creation_sync,
)
from documents.models import DocumentSigner
from documents.views.document_views import download_and_decrypt_from_s3
from esign.utils.custom_response import api_response
from utils_app.logger_utils import log_user_action

# from concurrent.futures import ThreadPoolExecutor, as_completed


logger = logging.getLogger("app")


def detect_device_type(request):
    """
    Detect device type from user agent and request data.
    Returns 'mobile' or 'desktop'
    """
    # First check if device_type is provided in request data
    device_type = request.data.get("device_type")
    if device_type in ["mobile", "desktop"]:
        return device_type

    # Fallback to user agent detection
    user_agent = request.META.get('HTTP_USER_AGENT', '').lower()

    mobile_keywords = ['mobile', 'android', 'iphone',
                       'ipad', 'ipod', 'blackberry', 'windows phone']
    if any(keyword in user_agent for keyword in mobile_keywords):
        return 'mobile'

    return 'desktop'


class StartAuthenticationView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_summary="Start BankID Authentication",
        operation_description="Initiates BankID authentication process",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "document_content": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Optional document content for signing",
                ),
                "document_ref_id": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Document reference ID for document signing",
                ),
                "flow_type": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Flow type: 'qr' or 'in_device'",
                    enum=["qr", "in_device"]
                ),
                "device_type": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Device type: 'mobile' or 'desktop'",
                    enum=["mobile", "desktop"]
                ),
                "return_url": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Return URL for in-device flow",
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Authentication Started",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(type=openapi.TYPE_STRING),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "verification_order_id": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "websocket_url": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                                "pdf_path": openapi.Schema(
                                    type=openapi.TYPE_STRING, nullable=True
                                ),
                                "autostart_url": openapi.Schema(
                                    type=openapi.TYPE_STRING, nullable=True
                                ),
                                "flow_type": openapi.Schema(
                                    type=openapi.TYPE_STRING
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: "Bad Request",
            401: "Unauthorized",
        },
        tags=["BankID Authentication"],
    )
    def post(self, request):

        logger.info(
            f"""Received authentication request from IP:
            {get_client_ip(request)} with data: {request.data}"""
        )

        # Detect device type
        # detected_device_type = detect_device_type(request)
        detected_device_type = request.data.get("device_type", "mobile")

        document_content_param = request.data.get("document_content", None)
        if document_content_param:
            document_content_param = request.data.get("document_content")
            serializer = AuthSerializer(
                data={
                    "document_content": document_content_param,
                    "end_user_ip": get_client_ip(request),
                    "flow_type": request.data.get("flow_type", "qr"),
                    "device_type": detected_device_type,
                    "return_url": request.data.get("return_url"),
                }
            )
        else:
            serializer = AuthSerializer(
                data={
                    **request.data,
                    "end_user_ip": get_client_ip(request),
                    "device_type": detected_device_type,
                }
            )
        if not serializer.is_valid():
            logger.warning(
                f"""Validation error for request from IP:
                {get_client_ip(request)}. Errors: {serializer.errors}"""
            )
            return api_response(action="validation_error", message=serializer.errors)

        logger.info(
            f"Request validated successfully for " f"IP: {get_client_ip(request)}"
        )

        end_user_ip = serializer.data["end_user_ip"]
        document_content = serializer.data.get("document_content")
        flow_type = serializer.data.get("flow_type", "qr")
        device_type = serializer.data.get("device_type", "desktop")
        # return_url = serializer.data.get("return_url")
        output_path = serializer.data.get("document_content")
        verification_start_time = time.time()

        # Validate BankID tokens before proceeding with authentication
        try:    
            # Get organization directly from organization_id parameter
            from accounts.models.organisation import Organization
            from documents.models.document_models import UserDocument
            from subscriptions.services.subscription_feature_service import SubscriptionFeatureManager
            try:
                document = UserDocument.objects.get(reference_id=document_content)
                subscription_feature = SubscriptionFeatureManager(document.owner)
                bankid_token_info = subscription_feature.get_bankid_token_info()

                logger.info(f"{'=='*50}\n\nBankID token info: {bankid_token_info}\n\n{'=='*50}")
                # organization = Organization.objects.get(id=organization_id, is_active=True, is_deleted=False)
                # logger.info(f"Found organization for document signing: {organization.id}")
            except Organization.DoesNotExist:
                logger.error(f"Organization not found for document: {document_content}")
                return api_response(
                    action="organization_not_found",
                    message="Organization not found. Please contact support.",
                    status=400
                )
            except Exception as e:
                logger.error(f"Error getting organization by ID: {str(e)}")
                return api_response(
                    action="validation_error",
                    message="Error validating organization. Please try again.",
                    status=400
                )
            
            # Validate BankID tokens for document signing
            if bankid_token_info.get('remaining') <= 0:
                logger.warning(f"No BankID tokens remaining for document owner {document_content}")
                return api_response(
                    action="insufficient_tokens",
                    message="No BankID tokens remaining. Please contact the document owner to purchase more tokens.",
                    status=400
                )
            else:
                logger.info(f"BankID tokens available for document owner: {bankid_token_info.get('remaining')} remaining")
        
        except Exception as e:
            logger.error(f"Error validating BankID tokens: {str(e)}", exc_info=True)
            return api_response(
                action="validation_error",
                message="Error validating BankID tokens. Please try again.",
                status=400
            )

        logger.info(
            f"Verification process started for IP: {end_user_ip}."
            f" Flow: {flow_type}, Device: {device_type}"
            f" Verification start time: {verification_start_time}"
        )

        if document_content:
            logger.info("Document content provided. Proceeding with document signing.")
            response_data = sign_document(serializer.data)
            log_user_action(
                request=request,
                action='start_bankid_signing',
                status='success',
                details={'type': 'document_signing', 'flow_type': flow_type}
            )
        else:
            logger.info(f"BankID authentication requested for IP: {end_user_ip}")
            response_data = bankid_authenticate(end_user_ip)
            log_user_action(
                request=request,
                action='start_bankid_auth',
                status='success',
                details={'type': 'authentication', 'flow_type': flow_type}
            )

        if not response_data:
            logger.error(
                f"Invalid response for QR code creation."
                f" Authentication failed for IP: {end_user_ip}"
            )
            return api_response(
                action="unauthorized_access",
                message=_("Invalid response for QR code creation"),
            )
        order_ref = response_data.get("orderRef")
        logger.info(f"QR code created successfully. Order reference: {order_ref}.")
        # Set session timeout based on flow type
        session_timeout = 300 if flow_type == "in_device" else 60
        session_key = f"bankid_auth_response_{order_ref}"

        # For in-device flow, also store with nonce for verification
        if flow_type == "in_device":
            nonce = serializer.data.get("nonce")
            original_return_url = serializer.data.get("original_return_url")
            if nonce:
                session_key = f"bankid_auth_response_{nonce}"
                cache.set(
                    session_key,
                    {
                        "response_data": response_data,
                        "verification_start_time": verification_start_time,
                        "order_ref": order_ref,
                        "document_content": document_content,
                        "original_ip": get_client_ip(request),
                        "original_return_url": original_return_url,  # Store for redirect after completion
                    },
                    timeout=session_timeout,
                )
                logger.info(f"Stored session with nonce: {session_key}")
        else:
            cache.set(
                session_key,
                {
                    "response_data": response_data,
                    "verification_start_time": verification_start_time,
                },
                timeout=session_timeout,
            )

        # Prepare response data
        response_data_dict = {
            "verification_order_id": order_ref,
            "pdf_path": output_path if output_path else None,
            "flow_type": flow_type,
        }

        # Add flow-specific data
        if flow_type == "qr":
            # Get the host dynamically from the request
            host = request.get_host()
            websocket_url = f"ws://{host}/ws/qr/{order_ref}/"
            response_data_dict["websocket_url"] = websocket_url
            logger.info(f"QR flow - WebSocket URL: {websocket_url}")
        elif flow_type == "in_device":
            # Generate autostart URL for in-device flow
            auto_start_token = response_data.get("autoStartToken")
            if auto_start_token:
                # Add order_ref to return URL if it exists
                autostart_url = generate_autostart_url(
                    auto_start_token,
                    device_type
                )
                response_data_dict["autostart_url"] = autostart_url
                logger.info(f"In-device flow - Autostart URL: {autostart_url}")
            else:
                logger.error(
                    "AutoStartToken not found in BankID response for in-device flow")
                return api_response(
                    action="unauthorized_access",
                    message=_("AutoStartToken not found in response"),
                )

        logger.info(f"BankID response cached for order reference: {order_ref}")
        return api_response(
            action="data_fetched",
            data=response_data_dict,
            status="success",
        )


@method_decorator(csrf_exempt, name='dispatch')
class BankIDCallbackView(APIView):
    """
    Simple callback view that handles BankID redirects and returns the original URL for React to handle.
    This is needed because BankID requires a simple returnUrl, but we need to redirect to complex document URLs.
    """
    authentication_classes = []
    permission_classes = [AllowAny]

    def get(self, request):
        """
        Handle BankID callback and return original URL for React to handle.
        """
        try:
            logger.info(
                f"BankID callback received - Method: {request.method}, GET params: {request.GET}, User-Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}, Referer: {request.META.get('HTTP_REFERER', 'Unknown')}")

            nonce = request.GET.get('nonce')
            if not nonce:
                logger.error("No nonce provided in BankID callback")
                return api_response(
                    action="unauthorized_access",
                    message=_("Invalid callback - no nonce provided"),
                )

            logger.info(f"BankID callback received with nonce: {nonce}")

            # Check if this looks like a legitimate BankID callback
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            referer = request.META.get('HTTP_REFERER', '')
            
            # Log additional info for debugging
            logger.info(f"User-Agent: {user_agent}")
            logger.info(f"Referer: {referer}")
            
            # Get session data using nonce
            session_key = f"bankid_auth_response_{nonce}"
            session_data = cache.get(session_key)

            if not session_data:
                logger.error(f"Session not found for nonce: {nonce}")
                return api_response(
                    action="unauthorized_access",
                    message=_("Session expired or invalid nonce."),
                )
            logger.info(f"Session data found for nonce: {session_data}")

            # Get the original return URL and order reference
            original_return_url = session_data.get("original_return_url")
            order_ref = session_data.get("order_ref")
            document_content = session_data.get("document_content")

            logger.info(f"Original return URL: {original_return_url}")
            logger.info(f"Order reference: {order_ref}")

            if not original_return_url:
                logger.error(f"Original return URL not found in session for nonce: {nonce}")
                return api_response(
                    action="unauthorized_access",
                    message=_("Invalid session data - no original return URL."),
                )

            if not order_ref:
                logger.error(f"Order reference not found in session for nonce: {nonce}")
                return api_response(
                    action="unauthorized_access",
                    message=_("Invalid session data - no order reference."),
                )

            # Also store session with order reference for WebSocket access
            order_ref_session_key = f"bankid_auth_response_{order_ref}"
            logger.info(f"Order reference session key: {order_ref_session_key}")
            cache.set(
                order_ref_session_key,
                session_data,
                timeout=300  # Same timeout as in-device flow
            )
            logger.info(
                f"Stored session with order reference for WebSocket access: {order_ref_session_key}")

            # Add nonce to the original URL for verification
            # Ensure proper URL formatting
            if "?" in original_return_url:
                # URL already has query parameters, use & as separator
                separator = "&"
                final_redirect_url = f"{original_return_url}{separator}nonce={nonce}"
            else:
                # URL has no query parameters, check if it ends with /
                if original_return_url.endswith('/'):
                    # URL ends with /, use ? as separator
                    final_redirect_url = f"{original_return_url}?nonce={nonce}"
                else:
                    # URL doesn't end with /, add / and then ?
                    final_redirect_url = f"{original_return_url}/?nonce={nonce}"

            logger.info(f"Final redirect URL: {final_redirect_url}")

            logger.info(
                f"BankID callback successful, returning redirect URL: {final_redirect_url}")

            # Return JSON response with redirect URL and order reference for React to handle
            return api_response(
                action="bankid_callback_success",
                data={
                    "redirect_url": final_redirect_url,
                    "nonce": nonce,
                    "order_ref": order_ref,  # Include order reference for WebSocket
                    "document_content": document_content,
                    "message": "BankID authentication completed successfully"
                },
                status="success",
            )
        except Exception as e:
            logger.error(f"Error in BankID callback: {str(e)}", stack_info=True)
            return api_response(
                action="server_error",
                message=_("Internal server error during callback processing"),
            )

class VerifyAuthenticationView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_summary="Verify BankID Authentication",
        operation_description="Verify BankID authentication status for in-device flow",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "nonce": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Nonce token from return URL (for in-device flow)",
                ),
                "verification_order_id": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Order reference ID (for QR flow)",
                ),
                "document_content": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Optional document content",
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Authentication Status",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(type=openapi.TYPE_STRING),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "status": openapi.Schema(type=openapi.TYPE_STRING),
                                "message": openapi.Schema(type=openapi.TYPE_STRING),
                                "completion_data": openapi.Schema(type=openapi.TYPE_OBJECT),
                            },
                        ),
                    },
                ),
            ),
            400: "Bad Request",
            401: "Unauthorized",
        },
        tags=["BankID Authentication"],
    )
    def post(self, request):
        logger.info("Received verification request with data: %s", request.data)
        serializer = VerifySerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(
                "Validation failed for verification request. Errors: %s",
                serializer.errors,
            )
            return api_response(action="validation_error", message=serializer.errors)

        # Log successful validation
        logger.info(
            "Verification request validated successfully for data: %s",
            serializer.validated_data,
        )

        # Determine if this is QR flow or in-device flow
        verification_order_id = serializer.validated_data.get("verification_order_id")
        nonce = serializer.validated_data.get("nonce")
        document_content = serializer.validated_data.get("document_content", None)

        if verification_order_id:
            # QR Flow - use order_ref directly
            logger.info(
                f"QR flow - Fetching status for verification_order_id: {verification_order_id}")
            order_ref = verification_order_id
            session_key = f"bankid_auth_response_{order_ref}"
        elif nonce:
            # In-Device Flow - use nonce to get order_ref from session
            logger.info(f"In-device flow - Fetching status using nonce: {nonce}")
            session_key = f"bankid_auth_response_{nonce}"

            # Get session data using nonce
            session_data = cache.get(session_key)
            if not session_data:
                logger.error(f"Session not found for nonce: {nonce}")
                return api_response(
                    action="unauthorized_access",
                    message=_("Session expired or invalid nonce."),
                )

            # Validate IP address hasn't changed
            original_ip = session_data.get("original_ip")
            current_ip = get_client_ip(request)
            if original_ip and original_ip != current_ip:
                logger.warning(f"IP address changed: {original_ip} -> {current_ip}")
                return api_response(
                    action="unauthorized_access",
                    message=_("IP address validation failed."),
                )

            order_ref = session_data.get("order_ref")
            if not order_ref:
                logger.error(f"Order reference not found in session for nonce: {nonce}")
                return api_response(
                    action="unauthorized_access",
                    message=_("Invalid session data."),
                )

            logger.info(f"Retrieved order_ref from session: {order_ref}")
        else:
            logger.error("Neither verification_order_id nor nonce provided")
            return api_response(
                action="validation_error",
                message=_("Either verification_order_id or nonce must be provided."),
            )

        # Collect status from BankID
        response_data = collect_status(order_ref)
        if not response_data:
            logger.error(f"Failed to retrieve status for order_ref: {order_ref}")
            return api_response(
                action="data_not_retrieved", message=_("Authentication failed.")
            )

        logger.info(
            f"Status fetched successfully for order_ref: {order_ref}. Response: {response_data}"
        )

        # Handle different status types
        if response_data.get("hintCode") and response_data.get("status"):
            response, status_code = self._handle_response_status(response_data)
            if response:
                logger.info(
                    "Processing response status for order_ref: %s. Status: %s",
                    order_ref,
                    response["status"],
                )
                return api_response(
                    action="status_update",
                    message=_(response["message"]),
                    data={"status": response["status"]}
                )

        if response_data.get("status") and response_data.get("completionData"):
            logger.info(
                "Completion data found for order_ref: %s. Proceeding with user creation.",
                order_ref,
            )
            return self._handle_completion_data(response_data, document_content, request)

        return api_response(action="status_pending", message=_("Authentication in progress"))

    def _handle_response_status(self, response_data):
        """Handles non-complete response statuses."""
        hint_code = response_data.get("hintCode")
        status_type = response_data.get("status")

        if status_type and status_type != "complete":
            response_message = get_bankid_message(status_type, hint_code)
            status_code = (
                status.HTTP_202_ACCEPTED
                if status_type == "pending"
                else status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            if response_message:
                logger.info(
                    "Handling response status. Status: %s, HintCode: %s",
                    status_type,
                    hint_code,
                )
                return (
                    {"message": response_message, "status": status_type},
                    status_code,
                )
        return None, None

    def _handle_completion_data(self, response_data, document_content, request):
        """Handles the completion data and creates a user."""
        completion_data = response_data.get("completionData")
        personal_details = completion_data.get("user")
        personal_number = personal_details.get("personalNumber")

        log_user_action(
            request=request,
            action='complete_bankid_auth',
            status='success',
            details={'personal_number': personal_number}
        )

        # Prepare personal info
        personal_info = {
            "given_name": personal_details.get("givenName"),
            "surname": personal_details.get("surname"),
        }

        # Prepare meta data
        transaction_id = response_data.get("orderRef")
        device_data = completion_data.get("device")
        ip_address = device_data.get(
            "ipAddress") if device_data else get_client_ip(request)
        date_time = time.strftime("%Y-%m-%d %H:%M:%S")
        meta_data = {
            "transaction_id": transaction_id,
            "ip_address": ip_address,
            "date_time": date_time,
        }

        # Use synchronous user creation
        jwt_manager = JWTTokenManager()
        try:
            context = handle_user_creation_sync(
                personal_number,
                jwt_manager,
                personal_info,
                False,
                get_client_ip(request),
                meta_data,
            )

            return api_response(
                action="data_retrieved",
                status="success",
                data=context,
            )

        except Exception as e:
            logger.error(f"Error in user creation: {str(e)}", exc_info=True)
        return api_response(
            action="error",
            message=_("Error creating user account."),
        )


class CancelAuthenticationView(APIView):
    authentication_classes = []
    permission_classes = [AllowAny]

    def post(self, request):
        logger.info(
            "Received cancel authentication request with data: %s", request.data
        )
        serializer = CancelSerializer(data=request.data)
        if serializer.is_valid():
            verification_order_id = serializer.validated_data["verification_order_id"]
            logger.info(
                f"""Attempting to cancel authentication for
                  verification_order_id: {verification_order_id}"""
            )
            response_data = cancel_authentication(verification_order_id)
            if not response_data:
                logger.error(
                    f"""Failed to cancel authentication for
                      verification_order_id: {verification_order_id}"""
                )
                return api_response(
                    action="validation_error",
                    message=_("Unable to cancel Order reference Id."),
                )
            logger.info(
                f"""Successfully canceled authentication for
                  verification_order_id: {verification_order_id}"""
            )
            log_user_action(
                request=request,
                action='cancel_bankid_auth',
                status='success',
                details={'verification_order_id': verification_order_id}
            )
            return api_response(
                action="data_retrieved",
                status="success",
                message=_("Cancellation of the order reference ID was successful."),
            )
        # Log validation errors
        logger.warning(
            f"""Validation failed for cancel
            authentication request.Errors: {serializer.errors}"""
        )
        return api_response(action="validation_error", message=serializer.errors)


# class SendMailToUser(APIView):
#     def post(self, request):
#         serializer = MailSerializer(data=request.data)
#         if serializer.is_valid():
#             emails = serializer.data.get("emails").split(",")
#             file = request.FILES.get("document_content")
#             create_file = FilesDocument.objects.create(file=file, name=str(file))

#             documents_responses = []

#             # Using ThreadPoolExecutor to send emails concurrently
#             # Adjust max_workers as needed
#             with ThreadPoolExecutor(max_workers=5) as executor:
#                 future_to_email = {}

#                 for email in emails:
#                     # Create the document and get its ID
#                     document = EmailDocuments.objects.create(
#                         file=create_file, email=email
#                     )

#                     # Submit the email sending task to the executor
#                     future = executor.submit(send_email, email, document)
#                     future_to_email[future] = email

#                 for future in as_completed(future_to_email):
#                     email, response = future.result()  # Get the email and response
#                     documents_responses.append((email, response))
#             return api_response(
#                 status="success",
#                 action="data_created",
#                 data=emails,
#             )

#         return api_response(action="validation_error", message=serializer.errors)


class DocumentPreviewView(TemplateView):
    template_name = "preview.html"

    def get_context_data(self, **kwargs):
        logger.info(
            f"""Accessing Document PreviewView with
              reference_signer_id: {self.kwargs.get('pk')}"""
        )
        context = super().get_context_data(**kwargs)
        reference_signer_id = self.kwargs.get("pk")

        # Fetch signer and related document
        signer = (
            DocumentSigner.objects.select_related("document")
            .filter(reference_signer_id=reference_signer_id)
            .first()
        )

        if not signer or not signer.document or not signer.document.document_file:
            logger.warning(
                f"""Signer or document not found for
                  reference_signer_id: {reference_signer_id}"""
            )
            context["error"] = "Signer or document not found"
            return context

        user_obj = User.objects.get(reference_id=signer.document.owner)
        str_date_joined = user_obj.date_joined.isoformat()
        # document_response = get_document_or_404(reference_id)
        str_created_obj = signer.document.created_at.isoformat()
        s3_url = signer.document.document_file.name
        # s3_url = request.data["document_path"]
        final_str_datetime = str_date_joined + "/" + str_created_obj
        logger.debug(f"Generated final string for decryption: {final_str_datetime}")

        logger.info(
            f"""Attempting to download and decrypt the
            document data from S3 for reference_id: {reference_signer_id}"""
        )
        decrypted_data = download_and_decrypt_from_s3(s3_url, final_str_datetime)
        encoded_file_data = base64.b64encode(decrypted_data).decode("utf-8")
        logger.debug(
            f"Decrypted document data for reference_id: {reference_signer_id}."
        )
        # ActivityLog.objects.create(
        #     document_refrence_id=signer.document.reference_id,
        #     event_type="Opened",
        #     from_user=signer.document.user,
        #     to_user=signer.email,
        #     category="Document Opened",
        #     description=f"Opened by {signer.get_decrypted_email()} by email.",
        #     user=signer.document.user  # User performing the action
        # # ip_address=document.ip_address,
        # # location=document.location,
        # )

        context.update(
            {
                "reference_signer_id": signer.reference_signer_id,
                "reference_id": signer.document.reference_id,
                "status": signer.status,
                "document_preview": encoded_file_data,
            }
        )

        return context

    # def get_base64_decrypted_file(self, document_file):
    #     """Decrypts the document file and returns it as a base64-encoded string."""
    #     try:
    #         logger.info(f"Decrypting document file: {document_file.name}")
    #         encryption_key = settings.ENCRYPTION_KEY
    #         fernet = Fernet(encryption_key)

    #         with default_storage.open(document_file.name, "rb") as encrypted_file:
    #             encrypted_data = encrypted_file.read()
    #             decrypted_data = fernet.decrypt(encrypted_data)
    #             logger.info(f"Decryption successful for file: {document_file.name}")
    #             return base64.b64encode(decrypted_data).decode("utf-8")
    #     except Exception as e:
    #         logger.error(f"Error decrypting file {document_file.name}: {e}")
    #         # Log the error in production, omit
    #  print in production for better security
    #         print(f"Error decrypting file: {e}")
    #         return None
