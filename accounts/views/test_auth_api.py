from rest_framework.views import APIView
from rest_framework.response import Response
from django.contrib.auth import get_user_model
from django.db.models import Q
from django.utils.translation import gettext as _

from accounts.services.jwt_authentication.authentication import <PERSON><PERSON><PERSON>okenManager, generate_hmac_token
from esign.utils.custom_response import api_response

User = get_user_model()
jwt_manager = JWTTokenManager()


class TestLoginAPI(APIView):
    """
    Test API endpoint for login with email or phone number
    """
    permission_classes = []  # Allow unauthenticated access for testing

    def post(self, request):
        try:
            identifier = request.data.get('identifier')  # email or phone
            password = request.data.get('password')

            if not identifier or not password:
                return api_response(
                    message=_("Please provide both identifier (email/phone) and password"),
                    status="failed",
                    action="validation_error",

                )

            # Try to find user by email or phone
            user = User.objects.filter(
                Q(private_email=generate_hmac_token(identifier)) | Q(
                    phone_number=generate_hmac_token(identifier))
            ).first()

            if not user:
                return api_response(
                    message=_("No user found with provided email/phone"),
                    status="failed",
                    action="validation_error",

                )

            # Verify password
            if not user.check_password(password):
                return api_response(
                    message=_("Invalid credentials"),
                    status="failed",
                    action="validation_error",

                )

            # Generate tokens
            access_token = jwt_manager.generate_token(
                str(user.id),
                token_type="access",
                exp_minutes=30  # 30 minutes expiry
            )
            refresh_token = jwt_manager.generate_token(
                str(user.id),
                token_type="refresh",
                exp_minutes=10080  # 7 days expiry
            )

            response_data = {
                "user": {
                    "id": user.id,
                    "email": user.email,
                    "phone_number": user.phone_number,
                    "full_name": user.get_full_name()
                },
                "tokens": {
                    "access": access_token,
                    "refresh": refresh_token
                }
            }

            return api_response(
                data=response_data,
                message=_("Login successful"),
                status="success",

            )

        except Exception as e:
            return api_response(
                message=str(e),
                status="failed",
                action="server_error",

            )
