
# from django.shortcuts import get_object_or_404
from decimal import Decimal
from django.utils.translation import gettext as _
from documents.utils.jwt_verifier import JWTAccessTokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from documents.utils.services import get_user_plan_storage_limit, user_storage_usages
from esign.utils.custom_response import api_response


class StorageAuthView(APIView):
    """
    View to handle storage authentication.
    """
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        storage_used = user_storage_usages(request.user)
        storage_limit = get_user_plan_storage_limit(request.user)
        print(f"Storage used: {storage_used}")
        print(f"Storage limit: {storage_limit}")
        storage_remaining = Decimal(storage_limit) - storage_used
        return api_response(
            action="data_retrieved",
            data={
                "storage_limit": storage_limit,
                "storage_used": storage_used,
                "storage_remaining": storage_remaining,
            },
            message=_("Storage information retrieved successfully"),
            status="success",
        )
