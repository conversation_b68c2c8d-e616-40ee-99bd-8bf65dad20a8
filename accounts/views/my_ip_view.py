"""
Simple view to show your device IP and location
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from ..utils.geo_hybrid import get_location_from_ip, get_comprehensive_ip_info

@csrf_exempt
@require_http_methods(["GET"])
def my_ip_view(request):
    """
    Show your device IP and location information.
    """
    try:
        # Get client IP from request
        client_ip = request.META.get('REMOTE_ADDR', 'Unknown')
        
        # Get location info
        location = get_location_from_ip(client_ip)
        comprehensive_info = get_comprehensive_ip_info(client_ip)
        
        return JsonResponse({
            "status": "success",
            "your_device_ip": client_ip,
            "location": location,
            "comprehensive_info": comprehensive_info,
            "note": "This shows the IP of the device making the request"
        })
        
    except Exception as e:
        return JsonResponse({
            "status": "error",
            "message": str(e)
        }, status=500)
