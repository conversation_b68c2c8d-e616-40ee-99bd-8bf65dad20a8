from accounts.serializers.user_serializers import UserFullDetailsRetrieveSerializer

import logging

import logging

from cryptography.fernet import <PERSON><PERSON><PERSON>
from django.conf import settings
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from accounts.models.users import User
from accounts.serializers.user_serializers import UserFullDetailsRetrieveSerializer
# from django.contrib.auth.hashers import set_password
from documents.utils.jwt_verifier import JWTAccessTokenAuthentication
from esign.utils.custom_response import api_response

logger = logging.getLogger(__name__)

cipher_suite = Fernet(settings.ENCRYPTION_KEY)


class OrganisationsUsersListView(APIView):
    """
    API View to get a list of all
    invited users (with activation token)
    and their associated organizations.
    """

    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        organisation_reference_id = request.query_params.get(
            "organisation_reference_id")
        if request.user.role not in ["org_superadmin", "org_admin"]:
            return api_response(
                data=[],
                action="unauthorized_access",
                status="success",
            )
        try:
            # Include the requesting user (self) in the queryset
            invited_users = User.objects.filter(
                organizations__reference_id=organisation_reference_id,
                organizations__is_active=True,
                is_deleted=False,
            ).select_related()

            # Add non_deleteable field to each user in serializer data
            serializer = UserFullDetailsRetrieveSerializer(invited_users, many=True)
            response_data = serializer.data

            # Mark the requesting user as non-deleteable
            for user_data in response_data:
                user_data['non_deleteable'] = (user_data['id'] == request.user.id)

            return api_response(
                data=response_data,
                action="data_retrieved",
                status="success",
                message="Invited users fetched successfully",
            )
        except Exception as e:
            logger.error(f"Error fetching invited users: {e}")
            return api_response(
                action="data_not_retrieved",
                message="An error occurred while fetching invited users.",
                status="error",
            )


class OrganisationDeletedUsersListView(APIView):
    """
    API View to get a list of all deleted users from an organization.
    """
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        organisation_reference_id = request.query_params.get(
            "organisation_reference_id")

        if not organisation_reference_id:
            return api_response(
                action="data_not_retrieved",
                message="Organisation reference ID is required",
                status="error"
            )

        try:
            # Get all deleted users for the organization
            deleted_users = User.objects.filter(
                organizations__reference_id=organisation_reference_id,
                organizations__is_active=True,
                is_deleted=True,
            ).select_related()

            # Serialize the users data
            serializer = UserFullDetailsRetrieveSerializer(deleted_users, many=True)
            response_data = serializer.data

            # Add additional deletion info to each user
            for user_data in response_data:
                # Add deletion timestamp and who deleted it
                user = deleted_users.get(id=user_data['id'])
                user_data['deleted_at'] = user.deleted_at

                # Get the name of the user who performed the deletion
                if user.deleted_by:
                    deleter = User.objects.filter(reference_id=user.deleted_by).first()
                    if deleter:
                        user_data['deleted_by'] = {
                            'id': deleter.reference_id,
                            'name': deleter.get_full_name()
                        }

                # Check if user is restorable (not an org_superadmin)
                user_data['can_restore'] = user.role != "org_superadmin"

            return api_response(
                data=response_data,
                action="data_retrieved",
                status="success",
                message="Deleted users fetched successfully"
            )

        except Exception as e:
            logger.error(f"Error fetching deleted users: {e}")
            return api_response(
                action="data_not_retrieved",
                message="An error occurred while fetching deleted users.",
                status="error"
            )
