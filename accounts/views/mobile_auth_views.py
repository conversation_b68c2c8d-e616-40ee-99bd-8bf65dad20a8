import re
import stripe
import json
import logging
from accounts.utils.geo_hybrid import get_location_from_ip
from cryptography.fernet import Ferne<PERSON>
from django.conf import settings

# from django.contrib.auth.hashers import set_password
from django.db import transaction
from django.db.models import Prefetch
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.translation import gettext as _
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_decode, urlsafe_base64_encode
from django.utils.encoding import force_bytes, force_str
from django.db.models import Q

from accounts.utils.stripe_utils import get_or_create_customer
from esign.utils.custom_response import api_response
from rest_framework import generics
from rest_framework.exceptions import NotFound, ValidationError
from rest_framework.filters import OrderingFilter
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.views import APIView


from documents.utils.jwt_verifier import JWTAccessTokenAuthentication
from accounts.tasks import send_welcome_email, send_invite_email, send_background_email, send_welcome_user_email, send_new_user_added_confirmation_email_task, send_user_removed_from_organization_email_task, send_admin_account_deleted_email_task, send_user_profile_updated_email_task
from accounts.models.organisation import Organization
from accounts.models.otp import OneTimePassword
from accounts.models.users import SessionTrack, User
from accounts.serializers.mobile_auth_serializers import (
    InvitedUserSerializer,
    MobileSerializer,
    OtpVerifyViewSerilizer,
    UserProfileRetrieveSerializer,
    UserProfileUpdateSerializer,
    UserProfileVerifySerializer,
    UserUpdateSerializer,
    UserVerifySerializer,
    InvitedUserProfileUpdateSerializer,
)
from accounts.serializers.organisation_serializers import OrganizationSerializer
from accounts.services.bankid_authentication.bankid_utils import (
    get_client_ip,
    get_country_info,
    save_user_details,
)
from accounts.services.jwt_authentication.authentication import (
    fernet_decrypt,
    fernet_encrypt,
    generate_hmac_token,
)
from accounts.services.mobile_authentication.mobile_clients import send_otp_via_sms
from accounts.services.mobile_authentication.mobile_utils import (
    blacklist_token,
    generate_otp,
)

from subscriptions.models import UserSubscription
from subscriptions.services.subscription_feature_service import (
    SubscriptionFeatureManager,
)
from utils_app.logger_utils import log_user_action
from utils_app.services.user_permissions_helpers import get_user_details_with_permissions
from accounts.services.contact_update_service import ContactUpdateService

logger = logging.getLogger("app")

cipher_suite = Fernet(settings.ENCRYPTION_KEY)

stripe.api_key = settings.STRIPE_SECRET_KEY




class MobileSignup(APIView):

    def post(self, request):
        # logger.info("Processing MobileSignup request")
        serializer = MobileSerializer(data=request.data)
        resend_otp = request.query_params.get("resend_otp", 0)

        if not serializer.is_valid():
            # logger.warning(f"Validation failed: {serializer.errors}")
            return api_response(action="validation_error", message=serializer.errors)
        phone_number = serializer.data["phone_number"]

        user = User.objects.filter(
            private_phone=generate_hmac_token(phone_number), is_deleted=True
        ).first()

        if user:
            return api_response(action="not_found", message="User does not exist.")
        one_time_verification = request.query_params.get("one_time_verification", None)
        otp_instance = OneTimePassword.objects.filter(
            phone_number=generate_hmac_token(phone_number)
        ).first()

        if not otp_instance and not one_time_verification:
            return api_response(
                action="data_not_retrieved",
                message="Please Register or Verify Your Account",
            )

        if otp_instance and not resend_otp and not otp_instance.is_sent:
            if not one_time_verification:
                if not otp_instance.is_link_verified_phone:
                    return api_response(
                        action="data_not_retrieved", message="Please Verify your otp"
                    )
        otp = generate_otp()
        # logger.info(f"Generated OTP: {otp} for phone number: {phone_number}")
        if not re.match(r"^\+\d{1,3}\d{10}$", phone_number):
            # logger.warning(f"Invalid phone number format: {phone_number}")
            return api_response(
                action="validation_error",
                message="Invalid phone number format. Please provide a valid mobile number.",
            )
        send_otp = send_otp_via_sms(phone_number=phone_number, otp=otp)

        if send_otp["status"] == "failed":
            # logger.error(f"Failed to send OTP: {send_otp['message']} to phone number: {phone_number}")
            return api_response(message=send_otp["message"])

        hmac_number = generate_hmac_token(phone_number)

        otp_user, created = OneTimePassword.objects.update_or_create(
            phone_number=hmac_number,
            defaults={
                "otp_code": otp,
                "is_verified": False,
                "is_sent": True,
                "is_link_verified_phone": False,
            },
        )

        if created:
            # logger.info(f"OTP sent and user created for phone number: {phone_number}")
            log_user_action(
                request=request,
                action="OTP sent and user created for phone number",
                status="success",
                details={"type": "user_created"},
            )
            return api_response(
                data={"otp": otp},
                action="data_created",
                status="success",
                message=_("OTP sent and user created"),
            )
        elif resend_otp:
            logger.info(f"OTP updated and resent for phone number: {phone_number}")
            return api_response(
                data={"otp": otp},
                action="data_updated",
                status="success",
                message=_("OTP updated and resent"),
            )
        else:
            return api_response(
                data={"otp": otp},
                action="data_updated",
                status="success",
                message=_("OTP Sent Successfully"),
            )


class SendOtpAuthenticatedUser(APIView):
    """
    API view for sending OTP to authenticated users.
    Requires JWT authentication and user must be authenticated.
    """
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = MobileSerializer(data=request.data)
        resend_otp = request.query_params.get("resend_otp", 0)

        if not serializer.is_valid():
            return api_response(action="validation_error", message=serializer.errors)

        phone_number = serializer.data["phone_number"]

        # Validate phone number format
        if not re.match(r"^\+\d{1,3}\d{10}$", phone_number):
            return api_response(
                action="validation_error",
                message="Invalid phone number format. Please provide a valid mobile number.",
            )

        # Check if phone number belongs to another user
        if User.objects.filter(private_phone=generate_hmac_token(phone_number)).exclude(id=request.user.id).exists():
            return api_response(
                action="validation_error",
                message="This phone number is already registered with another user.",
            )

        otp = generate_otp()

        # Send OTP via SMS
        send_otp = send_otp_via_sms(phone_number=phone_number, otp=otp)
        if send_otp["status"] == "failed":
            return api_response(message=send_otp["message"])

        hmac_number = generate_hmac_token(phone_number)

        # Create or update OTP record
        otp_user, created = OneTimePassword.objects.update_or_create(
            phone_number=hmac_number,
            defaults={
                "otp_code": otp,
                "is_verified": False,
                "is_sent": True,
                "is_link_verified_phone": False,
                "expiration_time": timezone.now() + timezone.timedelta(minutes=10)
            },
        )

        if created:
            log_user_action(
                request=request,
                action="OTP sent for phone verification",
                status="success",
                details={"type": "phone_verification"},
            )
            return api_response(
                data={"otp": otp},
                action="data_created",
                status="success",
                message=_("OTP sent successfully"),
            )
        elif resend_otp:
            return api_response(
                data={"otp": otp},
                action="data_updated",
                status="success",
                message=_("OTP updated and resent"),
            )
        else:
            return api_response(
                data={"otp": otp},
                action="data_updated",
                status="success",
                message=_("OTP Sent Successfully"),
            )


class SignupWithMobile(APIView):
    permission_classes = [AllowAny]

    def post(self, request):

        serializer = MobileSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Validation failed for signup data: {serializer.errors}")
            return api_response(action="validation_error", message=serializer.errors)

        phone_number = serializer.validated_data["phone_number"]

        hmac_number = generate_hmac_token(phone_number)
        logger.debug(f"Generated HMAC for phone: {hmac_number}")

        try:
            user_exist = User.objects.get(private_phone=hmac_number, is_deleted=False)
        except User.DoesNotExist:
            user_exist = None

        if user_exist:
            logger.info(f"User already exists with phone number: {phone_number}")
            return api_response(action="user_exist", message="User already exists with this phone number")

        # Validate phone number format
        if not re.match(r"^\+\d{1,3}\d{10}$", phone_number):
            logger.warning(f"Invalid phone number format: {phone_number}")
            return api_response(
                action="validation_error",
                message="Invalid phone number format. Please provide a valid mobile number.",
            )

        otp = generate_otp()
        logger.debug(f"Generated OTP: {otp} for phone number: {phone_number}")

        send_otp_result = send_otp_via_sms(phone_number=phone_number, otp=otp)
        if send_otp_result["status"] == "failed":
            logger.error(
                f"Failed to send OTP to {phone_number}: {send_otp_result['message']}")
            return api_response(message=send_otp_result["message"])

        otp_user, created = OneTimePassword.objects.update_or_create(
            phone_number=hmac_number,
            defaults={
                "otp_code": otp,
                "is_verified": False,
                "is_link_verified_phone": False,
            },
        )

        if created:
            logger.info(f"New OTP record created for phone number: {phone_number}")
            log_user_action(
                request=request,
                action="OTP sent and user created for phone number",
                status="success",
                details={"type": "user_created"},
            )
            return api_response(
                data={"otp": otp},
                action="data_created",
                status="success",
                message=_("OTP sent and user created"),
            )
        else:
            logger.info(
                f"OTP record updated and resent for phone number: {phone_number}")
            return api_response(
                data={"otp": otp},
                action="data_updated",
                status="success",
                message=_("OTP updated and resent"),
            )


class OtpVerifyView(generics.RetrieveUpdateDestroyAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = OtpVerifyViewSerilizer

    def get_object(self):
        """Retrieve the OTP instance using the phone number."""
        phone_number = self.kwargs.get("phone_number")
        # Validate the phone number format
        if not re.match(r"^\+\d{1,3}\d{10}$", phone_number):
            # logger.warning(f"Invalid phone number format: {phone_number}")
            return api_response(
                action="validation_error",
                message="Invalid phone number format. Please provide a valid mobile number.",
            )
        try:
            hmac_number = generate_hmac_token(phone_number)
            # logger.info(f"Retrieving OTP instance for phone number: {phone_number}")
            return OneTimePassword.objects.get(phone_number=hmac_number)
        except OneTimePassword.DoesNotExist:
            # logger.error(f"OTP instance not found for phone number: {phone_number}")
            return None

    def put(self, request, *args, **kwargs):
        otp_instance = self.get_object()
        verify_link = request.query_params.get("verify_link", None)
        context = {}
        context["is_verfied"] = False
        document_preview = request.data.get(
            "document_preview", None
        )  # Using .get() avoids errors if it's missing
        if not otp_instance:
            logger.error("OTP instance not retrieved")
            return api_response(action="data_not_retrieved")

        merged_data = {
            **request.data,
            **{"phone_number": self.kwargs.get("phone_number")},
        }
        serializer = self.get_serializer(otp_instance, data=merged_data)
        serializer.is_valid(raise_exception=True)
        otp_code = serializer.validated_data.get("otp_code")
        phone_number = serializer.validated_data.get("phone_number")
        current_time = timezone.now()
        if current_time > otp_instance.expiration_time:
            # logger.warning(f"OTP expired for phone number: {phone_number}")
            return api_response(
                message=_(
                    "The OTP has expired. Please request a new one and try again."
                )
            )

        if otp_instance.otp_code != otp_code:
            # logger.warning(f"OTP code mismatch for phone number: {phone_number}")
            return api_response(action="wrong_otp", message=serializer.errors)

        if (
            otp_instance.is_verified
            and otp_instance.is_link_verified_phone
            and not document_preview
        ):
            # logger.warning(f"OTP already verified for phone number: {phone_number}")
            return api_response(
                action="wrong_otp",
                message=_("This OTP has already been used. Please request a new one."),
            )

        if not verify_link:
            otp_instance.is_verified = True
            otp_instance.is_link_verified_phone = True
        else:
            otp_instance.is_link_verified_phone = True
            otp_instance.is_phone_verified = True
        otp_instance.is_sent = False
        otp_instance.save()
        context = save_user_details(phone_number, "otp", request=request)
        # Check if user is already logged in somewhere
        user_detail_dict = context.get(
            "user_details", None
        )  # Assuming save_user_details returns user in context

        user_reference_id = user_detail_dict.get("user_refernce_id")
        user = User.objects.filter(reference_id=user_reference_id).first()
        subscribed_trial = SubscriptionFeatureManager(user)
        context["subscription"] = subscribed_trial.get_active_subscription_features()

        if user and not verify_link:
            existing_session = SessionTrack.objects.filter(
                user=user, is_logged_in=True
            ).first()
            if existing_session:
                return api_response(
                    data={"user_reference_id": user_reference_id},
                    action="unauthorized_access",
                    message="Please logout from other devices and try again.",
                )

            # Create new session
            SessionTrack.objects.update_or_create(
                user=user,
                ip_address=get_client_ip(request),
                is_logged_in=True,
                generated_token=user_detail_dict["access_token"],
                # ip_address=request.META.get('REMOTE_ADDR'),
                # user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

        context["is_verfied"] = otp_instance.is_verified
        context["is_phone_verified"] = otp_instance.is_phone_verified
        context["user_details"]["user_type"] = "otp"
        context["user_details"]["meta_data"] = get_country_info()

        if not verify_link:
            log_user_action(
                request=request,
                action="OTP verified and user created for phone number",
                status="success",
                details={"type": "user_created"},
            )
        # logger.info(f"OTP verified and user created for phone number: {phone_number}")
        return api_response(
            data=context,
            action="data_updated",
            status="success",
        )


class OtpOneTimeAcessView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = OtpVerifyViewSerilizer

    def get_object(self):
        """Retrieve the OTP instance using the phone number."""
        phone_number = self.kwargs.get("phone_number")
        try:
            hmac_number = generate_hmac_token(phone_number)
            logger.info(f"Retrieving OTP instance for phone number: {phone_number}")
            return OneTimePassword.objects.get(phone_number=hmac_number)
        except OneTimePassword.DoesNotExist:
            logger.error(f"OTP instance not found for phone number: {phone_number}")
            return None

    def put(self, request, *args, **kwargs):
        otp_instance = self.get_object()
        if not otp_instance:
            logger.error("OTP instance not retrieved")
            return api_response(action="data_not_retrieved")

        merged_data = {
            **request.data,
            **{"phone_number": self.kwargs.get("phone_number")},
        }
        serializer = self.get_serializer(otp_instance, data=merged_data)
        serializer.is_valid(raise_exception=True)

        otp_code = serializer.validated_data.get("otp_code")
        phone_number = serializer.validated_data.get("phone_number")

        current_time = timezone.now()
        if current_time > otp_instance.expiration_time:
            logger.warning(f"OTP expired for phone number: {phone_number}")
            return api_response(
                message=_(
                    "The OTP has expired. Please request a new one and try again."
                )
            )

        if otp_instance.otp_code != otp_code:
            logger.warning(f"OTP code mismatch for phone number: {phone_number}")
            return api_response(action="validation_error", message=serializer.errors)

        if otp_instance.is_verified:
            logger.warning(f"OTP already verified for phone number: {phone_number}")
            return api_response(
                action="validation_error",
                message=_("This OTP has already been used. Please request a new one."),
            )

        otp_instance.is_verified = True
        otp_instance.save()
        token = save_user_details(phone_number, user_type="otp", is_onetime=True)
        token["user_details"]["user_type"] = "otp"
        logger.info(
            f"""OTP verified and user granted
            one-time access for phone number: {phone_number}"""
        )
        return api_response(
            data=token,
            action="data_updated",
            status="success",
        )


class UserProfileRetrieveUpdateAPIView(generics.RetrieveUpdateAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = UserProfileUpdateSerializer

    def get_object(self):
        """Retrieve the user profile instance."""
        logger.info(f"Retrieving profile for user: {self.request.user}")
        # Get reference_id from query parameters instead of request data
        user_reference_id = self.request.query_params.get("reference_id", None)
        if not user_reference_id:
            user_reference_id = self.request.user.reference_id
        logger.info(
            f"Using user_reference_id: {user_reference_id} to fetch user object")
        user_obj = User.objects.filter(reference_id=user_reference_id).first()
        if not user_obj:
            logger.error(f"User object not found for reference_id: {user_reference_id}")
        return user_obj

    def get_serializer_class(self):
        """
        Dynamically return the correct serializer based on the HTTP method:
        - Use `UserProfileRetrieveSerializer` for GET requests.
        - Use `UserProfileUpdateSerializer` for PUT requests.
        - Use `UserProfileVerifySerializer` for PUT requests.
        """
        if self.request.method == "GET":
            return UserProfileRetrieveSerializer
        elif self.request.method == "PUT":
            return UserProfileVerifySerializer
        elif self.request.method == "PATCH":
            return UserProfileUpdateSerializer
        return super().get_serializer_class()

    def get(self, request, *args, **kwargs):
        """Handle GET request to retrieve the user profile."""
        logger.info(
            f"UserProfileRetrieveUpdateAPIView GET: Starting profile retrieval for user {request.user.id}")
        try:
            user_reference_id = self.request.query_params.get("reference_id", None)
            if not user_reference_id:
                user_reference_id = self.request.user.reference_id
            logger.info(
                f"UserProfileRetrieveUpdateAPIView GET: Fetching user object with reference_id: {user_reference_id}")
            user_obj = User.objects.filter(reference_id=user_reference_id).first()

            if not user_obj:
                logger.error(
                    f"UserProfileRetrieveUpdateAPIView GET: User profile not found for reference_id: {user_reference_id}")
                return api_response(action="data_not_retrieved", status="error", message="User profile not found")

            logger.info(
                "UserProfileRetrieveUpdateAPIView GET: User object found. Serializing data.")
            # Convert user_obj to serialized data first
            serializer = self.get_serializer(user_obj)
            response_data = serializer.data
            logger.info(
                f"UserProfileRetrieveUpdateAPIView GET: Serialized data: {response_data}")

            # Subscription Feature Manager
            logger.info(
                "UserProfileRetrieveUpdateAPIView GET: Getting subscription features and user permissions.")
            subscription_feature_manager = SubscriptionFeatureManager(user_obj)
            response_data["user_permissions"] = response_data.get(
                "user_permissions", {})
            response_data["subscription"] = subscription_feature_manager.get_active_subscription_features()
            logger.info(
                f"UserProfileRetrieveUpdateAPIView GET: Added user_permissions and subscription data. response_data: {response_data}")

            logger.info(
                f"UserProfileRetrieveUpdateAPIView GET: User profile retrieved successfully for reference_id: {user_obj.reference_id}"
            )

            return api_response(
                data=response_data,
                action="data_retrieved",
                status="success",
            )
        except Exception as e:
            logger.error(
                f"UserProfileRetrieveUpdateAPIView GET: Error retrieving user profile: {str(e)}", exc_info=True)
            return api_response(
                action="server_error",
                message=f"An error occurred while retrieving user profile: {str(e)}",
                status="error",
            )

    def update(self, request, *args, **kwargs):
        user_obj = self.get_object()
        if not user_obj:
            logger.error("User profile not found for user")
            return api_response(action="data_not_retrieved")

        serializer = self.get_serializer(user_obj, data=request.data)
        serializer.is_valid(raise_exception=True)
        # Perform Update
        self.perform_update(serializer)
        logger.info("User profile updated for user")

        return api_response(
            data=serializer.data,
            action="data_updated",
            status="success",
        )

    def partial_update(self, request, *args, **kwargs):
        user_reference_id = self.request.user.reference_id
        user_obj = User.objects.filter(reference_id=user_reference_id).first()
        otp_instance = OneTimePassword.objects.filter(
            phone_number=generate_hmac_token(user_obj.get_decrypted_phone_number())
        ).first()
        user_obj.private_phone = generate_hmac_token(
            user_obj.get_decrypted_phone_number()
        )
        if otp_instance:
            otp_instance.is_phone_verified = True
            otp_instance.save()
        if not user_obj:
            logger.error("User profile not found for user")
            return api_response(action="data_not_retrieved")

        serializer = self.get_serializer(user_obj, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        # Perform Update
        self.perform_update(serializer)
        logger.info("User profile updated for user")
        send_welcome_user_email.delay(receiver_name=user_obj.get_full_name(),
                                      receiver=user_obj.get_decrypted_email(),
                                      subject="Welcome to Skrivly",)

        SubscriptionFeatureManager(user_obj)
        return api_response(
            data=serializer.data,
            action="data_updated",
            status="success",
        )


class UserUpdateAPIView(generics.UpdateAPIView):
    
    serializer_class = UserUpdateSerializer
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]  # Only authenticated users can update

    def get_object(self):
        # Use self.request.user.reference_id to get the current user
        user_reference_id = self.request.user.reference_id
        user = get_object_or_404(User, reference_id=user_reference_id)
        return user

    def patch(self, request, *args, **kwargs):
        # Ensure that partial updates are allowed (PATCH)
        return self.partial_update(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()

        # Capture old values before update
        old_data = {
            'first_name': instance.first_name,
            'last_name': instance.last_name,
            'email': instance.get_decrypted_email(),
            'phone_number': instance.get_decrypted_phone_number(),
            'address1': instance.address1,
            'address2': instance.address2,
            'zip_code': instance.zip_code,
            'state': instance.state,
            'country': instance.country,
            'residency': instance.residency,
            'is_active': instance.is_active,
            'two_fa_enabled': instance.two_fa_enabled,  # Add this field for enable_2fa tracking
        }

        serializer = self.get_serializer(
            instance, data=request.data, partial=True
        )  # partial=True
        if serializer.is_valid():
            updated_instance = serializer.save()

            # Track only the fields that were actually changed
            changed_fields = {}
            for field in request.data.keys():
                # Skip fields that shouldn't be tracked for changes
                if field in ['password', 'profile_picture']:
                    continue
                    
                # Map serializer field names to model field names for proper comparison
                if field == 'enable_2fa':
                    model_field = 'two_fa_enabled'
                else:
                    model_field = field
                
                # Get the new value from validated data (this ensures validation was passed)
                if field in serializer.validated_data:
                    new_value = serializer.validated_data[field]
                    old_value = old_data.get(model_field)
                    
                    if new_value != old_value:
                        # Additional check: don't track if both values are essentially empty
                        old_is_empty = old_value is None or old_value == "" or old_value == "N/A"
                        new_is_empty = new_value is None or new_value == "" or new_value == "N/A"
                        
                        if not old_is_empty and not new_is_empty:
                            # Use the model field name for consistency
                            changed_fields[model_field] = {
                                'old': old_value,
                                'new': new_value
                            }


            # Only send email if there are actual changes
            if changed_fields:
                # Get IP Address, Device/Browser, and Location
                ip_address = get_client_ip(request)
                device_data_str = request.data.get('device_data')
                device_data = json.loads(device_data_str)   
                device_browser = ":".join(device_data.values()) 
                location = get_location_from_ip(ip_address)  # Getting precise location requires a dedicated service/library

                # Send profile update confirmation email with changed fields data
                send_user_profile_updated_email_task.delay(
                    user_email=updated_instance.get_decrypted_email(),
                    user_first_name=updated_instance.first_name,
                    updated_by=request.user.get_full_name(),
                    update_date=timezone.now().strftime("%B %d, %Y"),
                    changed_fields=changed_fields,
                    ip_address=ip_address,
                    device_browser=device_browser,
                    location=location
                )

            return api_response(
                data=serializer.data, action="data_updated", status="success"
            )
        else:
            return api_response(
                data=serializer.errors, action="data_not_updated", status="error"
            )


class OrganizationListCreateView(generics.ListCreateAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    queryset = Organization.objects.all()
    serializer_class = OrganizationSerializer
    filter_backends = (OrderingFilter,)
    ordering_fields = ["name", "created_at"]
    ordering = ["created_at"]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["request"] = self.request

        return context

    def create(self, request, *args, **kwargs):
        try:
            # Check if user can create organization
            feature_manager = SubscriptionFeatureManager(request.user)
            organisation_management_info = feature_manager.check_feature_availability(
                "organisations")

            if not organisation_management_info.get("available") > 0 or not organisation_management_info.get("unlimited"):
                return api_response(
                    action="validation_error",
                    message="Organization limit reached",
                    data=[],
                )

            with transaction.atomic():
                # Create organization using existing logic
                serializer = self.get_serializer(data=request.data)
                serializer.is_valid(raise_exception=True)
                self.perform_create(serializer)

                # After successful creation, update the count
                result = feature_manager.update_feature_usage(
                    feature_key="organisations", increment=1
                )

                if not result.get("success"):
                    raise ValidationError(
                        result.get("error", "Failed to update organization count")
                    )

                # headers = self.get_success_headers(serializer.data)
                return api_response(
                    action="data_created",
                    message="Organization created successfully",
                    data={"organization": serializer.data, "subscription_info": result},
                    status="success",
                )

        except Exception as e:
            logger.error(f"Error creating organization: {str(e)}")
            return api_response(action="server_error", message=str(e))

    def get_queryset(self):
        """Override to filter organizations based on the current user."""
        user = self.request.user
        queryset = None
        if user.role in ["org_admin", "org_member"]:
            org = user.organizations.filter(is_active=True, is_deleted=False).first()

            if org:
                # Get the owner's (org_superadmin) subscription
                owner = User.objects.filter(
                    organizations=org, role="org_superadmin",
                ).first()

        if user.role in ["org_superadmin"]:
            queryset = Organization.objects.filter(
                created_by=user.reference_id, is_deleted=False)
        else:
            queryset = Organization.objects.filter(
                created_by=owner.reference_id, is_deleted=False)

        # i need only users belong to the organisati   on
        active_users = User.objects.filter(is_active=True, is_deleted=False)

        queryset = queryset.prefetch_related(
            Prefetch("users", queryset=active_users, to_attr="active_users")
        )

        # Apply search filter
        search_query = self.request.query_params.get("search", None)
        if search_query:
            logger.info(f"Search term provided: {search_query}")
            matching_docs = []

            for org_doc in queryset:
                try:
                    decrypted_name = (
                        fernet_decrypt(org_doc.name.encode()) if org_doc.name else None
                    )
                    decrypted_number = (
                        fernet_decrypt(org_doc.number.encode())
                        if org_doc.number
                        else None
                    )

                    if (
                        decrypted_name
                        and search_query.lower() in decrypted_name.lower()
                    ):
                        matching_docs.append(org_doc.id)
                        continue

                    if (
                        decrypted_number
                        and search_query.lower() in decrypted_number.lower()
                    ):
                        matching_docs.append(org_doc.id)

                except Exception as e:
                    logger.error(f"Error decrypting organization fields: {e}")
                    continue

            queryset = queryset.filter(id__in=matching_docs)
            logger.info(
                f"Found {len(matching_docs)} organizations matching the search term."
            )

        return queryset


class OrganizationSetPrimaryView(generics.UpdateAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = OrganizationSerializer

    def update(self, request, *args, **kwargs):
        try:
            reference_id = request.query_params.get("organisation_reference_id", None)
            if not reference_id:
                return api_response(
                    status="failed", message="No organization IDs provided"
                )
            if request.user.role not in ["org_superadmin", "org_admin"]:
                return api_response(
                    data=[],
                    action="unauthorized_access",
                    status="success",
                )
            # Using transaction.atomic() to ensure atomicity
            with transaction.atomic():
                # Find the document
                organization = Organization.objects.filter(
                    reference_id=reference_id,
                )

                if not organization.exists():
                    return api_response(
                        action="data_not_retrieved",
                        message="No valid organization found to move to trash",
                    )

                # Ensure the 'is_default' field is set to True
                # for the selected organization
                organization = organization.first()
                organization.is_primary = True
                organization.save()  # Get the first organization

                # Set 'is_default' to False for all other organizations
                Organization.objects.exclude(id=organization.reference_id).update(
                    is_primary=False
                )

                serializer = self.get_serializer(organization)

                logger.info(f"Moved {organization.reference_id} document to trash")

            # Success response after transaction is committed
            return api_response(
                data=serializer.data,
                action="data_updated",
                status="success",
                message=f"Organization {organization.reference_id} set as default.",
            )

        except Exception as e:
            # Log and return an error response
            logger.error(f"Error moving organization to trash: {e}")
            return api_response(
                action="data_not_updated",
                message="An error occurred while moving the organization to trash",
            )


class OrganizationUpdateView(generics.UpdateAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = OrganizationSerializer

    def update(self, request, *args, **kwargs):
        try:
            reference_id = request.query_params.get("organisation_reference_id", None)
            if not reference_id:
                return api_response(
                    status="failed", message="No organization IDs provided"
                )

            # Using transaction.atomic() to ensure atomicity
            with transaction.atomic():
                # Find the document
                organization = Organization.objects.filter(
                    reference_id=reference_id,
                )

                if not organization.exists():
                    return api_response(
                        action="data_not_retrieved",
                        message="No valid organization found to activate",
                    )

                # Update the 'is_active' field is set to True
                # for the selected organization
                organization = organization.first()
                organization.is_active = True
                organization.save()  # Get the first organization

                # Set 'is_default' to False for all other organizations
                # Organization.objects.exclude(id=organization.reference_id).update(
                #     is_primary=False
                # )
                # set users withn the organization to update.
                users = User.objects.filter(organizations=organization)
                users.update(is_active=True)

                serializer = self.get_serializer(organization)

                logger.info(f"Moved {organization.reference_id} document to trash")

            # Success response after transaction is committed
            return api_response(
                data=serializer.data,
                action="data_updated",
                status="success",
                message="Organization set as default. and activated",
            )

        except Exception as e:
            # Log and return an error response
            logger.error(f"Error moving organization to trash: {e}")
            return api_response(
                action="data_not_updated",
                message="An error occurred while moving the organization to trash",
            )


class OrganizationTrashView(generics.DestroyAPIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def destroy(self, request, *args, **kwargs):
        try:
            reference_id = request.query_params.get("organisation_reference_id", None)
            if not reference_id:
                return api_response(
                    status="failed",
                    message="No valid organization found to move to trash",
                )
            # Using transaction.atomic() to ensure atomicity
            with transaction.atomic():
                # Find the organization
                organization = Organization.objects.filter(
                    reference_id=reference_id,
                    # is_active=True,
                    # is_primary=True,  # Only move certain organisations to trash
                )

                if not organization.exists():
                    return api_response(
                        action="data_not_retrieved",
                        message="No valid organization found to move to trash",
                    )

                organization = organization.first()  # Get the first organization

                # Move to trash by setting the organization's active status to False
                organization.is_active = False
                organization.save()

                # Log that the organization is moved to trash
                logger.info(f"Moved {organization.reference_id} organization to trash")

                # Now handle the users associated with this organization
                users_to_remove = User.objects.filter(
                    organizations=organization
                ).distinct()

                if users_to_remove.exists():
                    # You can either deactivate or delete
                    # the users based on your use case
                    users_to_remove.update(
                        is_active=False
                    )  # Deactivate users (you can use a different approach if needed)

                    logger.info(
                        f"""Deactivated {users_to_remove.count()}
                        users associated with {organization.reference_id}"""
                    )

            # Success response after transaction is committed
            return api_response(
                action="data_deleted",
                status="success",
                message=f"""Organization {organization.reference_id}
                and associated users moved to trash""",
            )

        except Exception as e:
            # Log and return an error response
            logger.error(f"Error moving organization to trash: {e}")
            return api_response(
                action="data_not_updated",
                message="An error occurred while moving the organization to trash",
            )


def assign_organization_to_user(user, organization):
    # Check if the organization is not already assigned
    if organization not in user.organizations.all():
        user.organizations.add(organization)
        user.save()


class VerifyInviteTokenView(APIView):
    """
    View to verify the invitation token before proceeding with password reset.
    This is a pre-check endpoint that validates all parameters and token
    without making any changes to the user account.
    """
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            # Extract all required parameters
            organization_id = request.data.get("organization_id")
            user_id = request.data.get("user_reference_id")
            owner_user_id = request.data.get("owner_user_id")
            verification_token = request.data.get("verification_token")
            verification_uid = request.data.get("verification_uid")

            # Validate all required parameters are present
            if not all([organization_id, user_id, owner_user_id, verification_token, verification_uid]):
                return api_response(
                    action="validation_error",
                    message="Missing required parameters",
                    status_code=400,
                    data={
                        "required_params": {
                            "organization_id": bool(organization_id),
                            "user_id": bool(user_id),
                            "owner_user_id": bool(owner_user_id),
                            "verification_token": bool(verification_token),
                            "verification_uid": bool(verification_uid)
                        }
                    }
                )

            # Verify the user from the token
            try:
                user_pk = force_str(urlsafe_base64_decode(verification_uid))
                user = User.objects.get(pk=user_pk)
            except (TypeError, ValueError, OverflowError, User.DoesNotExist):
                logger.error(f"Invalid user ID in token: {verification_uid}")
                return api_response(
                    action="validation_error",
                    message="Invalid verification link",
                    status="error"
                )

            # Verify the token is valid
            if not default_token_generator.check_token(user, verification_token):
                logger.error(f"Invalid or expired token for user: {user.reference_id}")
                return api_response(
                    action="validation_error",
                    message="This verification link has expired or is invalid",
                    status="error"
                )

            # Verify user_id matches
            if user.reference_id != user_id:
                logger.error(
                    f"User ID mismatch. Token user: {user.reference_id}, Provided user: {user_id}")
                return api_response(
                    action="validation_error",
                    message="Invalid user reference",
                    status="error"
                )

            # Verify organization exists and is active
            try:
                organization = Organization.objects.get(
                    reference_id=organization_id,
                    is_active=True
                )
            except Organization.DoesNotExist:
                logger.error(f"Organization not found or inactive: {organization_id}")
                return api_response(
                    action="validation_error",
                    message="Invalid organization",
                    status="error"
                )

            # Verify owner user exists
            owner_user = User.objects.filter(reference_id=owner_user_id).first()
            if not owner_user:
                logger.error(f"Owner user not found: {owner_user_id}")
                return api_response(
                    action="validation_error",
                    message="Invalid owner user",
                    status="error"
                )

            # Verify user belongs to the organization
            if not organization.users.filter(id=user.id).exists():
                logger.error(
                    f"User {user.reference_id} not associated with organization {organization_id}")
                return api_response(
                    action="validation_error",
                    message="User not associated with the organization",
                    status="error"
                )

            # If all validations pass, return success
            return api_response(
                action="data_retrieved",
                message="Verification link is valid",
                status="success",
                data={
                    "is_valid": True,
                }
            )

        except Exception as e:
            logger.error(f"Error verifying invitation token: {str(e)}")
            return api_response(
                action="server_error",
                message="An error occurred while verifying the invitation",
                status="error"
            )


class InviteUserAPIView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def _validate_admin_permissions(self, request, target_user):
        """
        Validate that the admin has permission to update the target user's phone.
        """
        admin_role = request.user.role
        target_role = target_user.role

        # Check if admin has appropriate role
        if admin_role not in ["org_superadmin", "org_admin"]:
            return False, "You don't have permission to update member details"

        # Check if target user is in the same organization
        admin_orgs = request.user.organizations.filter(is_active=True, is_deleted=False)
        target_orgs = target_user.organizations.filter(is_active=True, is_deleted=False)

        if not admin_orgs.intersection(target_orgs).exists():
            return False, "User not in your organization"

         # Role-based restrictions
        if admin_role == "org_admin":
            # org_admin can only update org_member
            if target_role not in ["org_member"]:
                return False, "You do not have permission to update the manager."
            if target_role != "org_member":
                return False, "You do not have permission to update the details of this user"
        elif admin_role == "org_superadmin":
            # org_superadmin can update org_admin and org_member
            if target_role not in ["org_admin", "org_member"]:
                return False, "Super admin can only update admin and member phone numbers"

        if admin_role == "org_admin" and target_role == "org_admin":
            return False, "You do not have permission to add manager."
        return True, None

    def post(self, request, *args, **kwargs):
        """
        Handle requests to invite or add a user to the system.
        """
        try:
            # Extract the necessary data from the request body
            email = request.data.get("email")
            organisation_id = request.data.get("organisation_reference_id")
            role = request.data.get("role")
            mode = request.data.get("mode", "add_user")
            first_name = request.data.get("first_name")
            last_name = request.data.get("last_name")
            phone_number = request.data.get("phone_number")

            # Check if organization is required for business/enterprise plans

            feature_manager = SubscriptionFeatureManager(request.user)
            if mode == "add_user":
                # Get user's active subscription
                try:
                    user_subscription = feature_manager.get_active_subscription()

                    # Check if plan requires organization details
                    if user_subscription.plan.plan_category in ['business_monthly', 'business_yearly', 'enterprise_monthly', 'enterprise_yearly']:
                        if not organisation_id:
                            return api_response(
                                action="validation_error",
                                message="Organization is required for business and enterprise subscriptions",
                                status="error"
                            )

                        # Get organization details
                        organization = Organization.objects.filter(
                            reference_id=organisation_id,
                        ).first()

                        if not organization:
                            return api_response(
                                action="validation_error",
                                message="Organization not found",
                                status="error"
                            )

                        # Check if the requesting user belongs to the organization
                        if not request.user.organizations.filter(id=organization.id).exists():
                            return api_response(
                                action="permission_denied",
                                message="You do not have permission to add users to this organization.",
                                status="error"
                            )

                        # Check if organization has domain
                        if not organization.org_domain:
                            return api_response(
                                action="validation_error",
                                message="Organization domain is required for business and enterprise subscriptions",
                                status="error"
                            )

                        # Validate email domain matches organization domain
                        if email:
                            user_email_domain = email.split('@')[-1].lower()
                            org_domain = organization.org_domain.lower()

                            if user_email_domain != org_domain:
                                return api_response(
                                    action="validation_error",
                                    message=f"User email domain ({user_email_domain}) must match organization domain ({org_domain})",
                                    status="error"
                                )
                except UserSubscription.DoesNotExist:
                    return api_response(
                        action="validation_error",
                        message="No active subscription found",
                        status="error"
                    )

            # Initial validations outside transaction
            existing_user = User.objects.filter(
                Q(private_email=generate_hmac_token(email)) | Q(
                    private_phone=generate_hmac_token(phone_number))
            ).first()

            # Check the user status, and if user is active or not is_deleted , return validation error.
            if existing_user:
                # Check if user is currently active
                if existing_user.is_active:
                    return api_response(
                        action="validation_error",
                        message="A user with this email or mobile number already exists in the system",
                        status="error"
                    )

                # Check if user is not deleted
                if not existing_user.is_deleted:
                    return api_response(
                        action="validation_error",
                        message="User already exists in the system",
                        status="error"
                    )

                # Check if user is associated with any other organization
                other_orgs = existing_user.organizations.exclude(
                    reference_id=organisation_id)
                if other_orgs.exists():
                    return api_response(
                        action="validation_error",
                        message="User is already associated with another organization",
                        status="error"
                    )

                # Check if user role is org_superadmin
                if existing_user.role == "org_superadmin":
                    return api_response(
                        action="validation_error",
                        message="Cannot invite user with super admin role",
                        status="error"
                    )

            # Check if the organization exists
            organization = feature_manager.get_organization()

            # organization = organization.first()

            if not organization:
                return api_response(
                    action="data_not_retrieved", message="No valid organization found."
                )

            user_create_status = feature_manager.check_feature_availability("users")
            # Before creating/activating a user, check if activating would exceed the allowed user limit for the organization
            available_limit = user_create_status.get("limit")
            if available_limit is not None and not user_create_status.get("unlimited", False):
                if organization.total_active_user >= available_limit:
                    return api_response(
                        action="validation_error",
                        message="User limit reached. Please upgrade your plan or deactivate an existing user before adding a new one.",
                        status="error"
                    )

            if not email or not organisation_id or not role:
                return api_response(
                    action="validation_error",
                    message="Email, organization reference ID, and role are required.",
                    status="error",
                )

            # Validate add_user mode requirements
            if mode == "add_user":
                first_name = request.data.get("first_name")
                last_name = request.data.get("last_name")
                phone_number = request.data.get("phone_number")
                is_active = request.data.get("account_status", True)

                if not first_name or not last_name:
                    return api_response(
                        action="validation_error",
                        message="First name and last name are required for adding a user.",
                        status="error",
                    )

            with transaction.atomic():
                if existing_user:
                    # If the user already exists, activate and assign to org
                    existing_user.first_name = first_name
                    existing_user.last_name = last_name
                    existing_user.is_active = True
                    existing_user.is_deleted = False
                    existing_user.role = role
                    existing_user.phone_number = cipher_suite.encrypt(
                        phone_number.encode()).decode()
                    existing_user.private_phone = generate_hmac_token(phone_number)
                    existing_user.save(
                        update_fields=["is_active", "is_deleted", "role", "first_name", "last_name", "phone_number", "private_phone"])

                    # Assign organization if not already assigned
                    assign_organization_to_user(existing_user, organization)

                    logger.info(f"Existing user {email} re-added to organization.")
                    # organization.user_added_count += 1
                    # organization.total_active_user += 1
                    # organization.save(
                    #     update_fields=["user_added_count", "total_active_user"])

                    uid = urlsafe_base64_encode(force_bytes(existing_user.pk))
                    # Generate verification token
                    token = default_token_generator.make_token(existing_user)

                    send_new_user_added_confirmation_email_task.delay(
                        new_user_email=email,
                        new_user_first_name=first_name,
                        account_owner_organization_name=organization.get_decrypted_name(),
                        user_role=existing_user.role,
                        added_by=request.user.get_full_name(),
                        date_added=timezone.now().strftime("%B %d, %Y"),

                        # Details to build URL.
                        organisation_id=organisation_id,
                        user_id=existing_user.reference_id,
                        owner_user_id=request.user.reference_id,
                        verification_token=token,
                        verification_uid=uid
                    )
                    logger.info(f"Sent invitation email to {email}.")

                    user = existing_user
                    created = False
                else:
                    # Create user with appropriate defaults
                    defaults = {
                        "is_active": is_active if mode == "add_user" else True,
                        "is_invited": True if mode == "invite_user" else False,
                    }

                    if mode == "add_user":
                        defaults.update(
                            {
                                "first_name": first_name,
                                "last_name": last_name,
                            }
                        )
                    if user_create_status.get("unlimited") or user_create_status.get("remaining") > 0:
                        user, created = User.objects.get_or_create(
                            email=cipher_suite.encrypt(email.encode()).decode(),
                            username=cipher_suite.encrypt(email.encode()).decode(),
                            defaults=defaults,
                            phone_number=cipher_suite.encrypt(
                                phone_number.encode()).decode(),
                            private_email=generate_hmac_token(email),
                            private_phone=generate_hmac_token(phone_number),
                            role=role,
                        )

                    # Assign organization to the user
                    assign_organization_to_user(user, organization)

                    uid = urlsafe_base64_encode(force_bytes(user.pk))
                    # Generate verification token
                    token = default_token_generator.make_token(user)

                    if created and mode == "add_user" or "invite_user":
                        logger.info(
                            f"Sent new user added confirmation email to {email}.")
                        try:
                            # Map role to display name for email template
                            role_display_map = {
                                "org_member": "Member",
                                "org_admin": "Manager",
                                "org_superadmin": "Organization Super Admin",
                                "super_admin": "Platform Super Admin",
                                "app_admin": "Application Admin",
                            }
                            display_role = role_display_map.get(role, role)

                            # Send new user added confirmation email with auto-generated password
                            send_new_user_added_confirmation_email_task.delay(
                                new_user_email=email,
                                new_user_first_name=first_name,
                                account_owner_organization_name=organization.get_decrypted_name(),
                                user_role=display_role,
                                added_by=request.user.get_full_name(),
                                date_added=timezone.now().strftime("%B %d, %Y"),

                                # Details to build URL.
                                organisation_id=organisation_id,
                                user_id=user.reference_id,
                                owner_user_id=request.user.reference_id,
                                verification_token=token,
                                verification_uid=uid

                            )
                            logger.info(f"Sent invitation email to {email}.")
                        except Exception as e:
                            logger.error(
                                f"Failed to send invitation email to {email}: {str(e)}"
                            )
                            raise Exception("Failed to send invitation email")

                # Calculate subscription duration
                start_date = timezone.now()
                # Assign the same subscription which is on request.user (or super admin)
                organization_subscription = feature_manager.get_organisation_subscription()
                # Create user subscription
                UserSubscription.objects.create(
                    user=user,
                    plan=organization_subscription.plan,
                    status='active',
                    start_date=start_date,
                    end_date=organization_subscription.end_date,
                    stripe_subscription_id=organization_subscription.stripe_subscription_id,
                    add_on_user_limit=organization.add_on_user_limit
                )

                context = {
                    "email": email,
                    "role": role,
                    "first_name": first_name if mode == "add_user" else None,
                    "last_name": last_name if mode == "add_user" else None,
                    "organisation_id": organisation_id,
                    "user_id": user.reference_id,
                    "is_new_user": created,
                }

                feature_manager = SubscriptionFeatureManager(request.user)
                result = feature_manager.update_feature_usage(
                    feature_key="users"
                )

                if not result.get("success"):
                    return api_response(
                        action="validation_error", message=result.get("error"), data=result
                    )
                context.update({
                    "result": result,
                    "limit_info": user_create_status.get("remaining"),
                })

                organization.user_added_count += 1
                organization.total_active_user += 1
                organization.save(
                    update_fields=["user_added_count", "total_active_user"])
                return api_response(
                    data=context,
                    action="data_created" if created else "data_retrieved",
                    message=(
                        "User added successfully."
                        if mode == "add_user"
                        else "Invitation email sent successfully."
                    ),
                    status="success",
                )
        except Exception as e:
            logger.error(f"Error in InviteUserAPIView: {str(e)}")
            return api_response(
                action="server_error",
                message="Failed to create user and send invitation. Please try again.",
                status="error",
            )

    def patch(self, request, *args, **kwargs):
        """
        Allows org_superadmin to update the profile (first_name, last_name, email, role, phone_number) of an invited user.
        The org_superadmin cannot update their own profile via this endpoint.
        """
        user_reference_id = request.data.get("user_reference_id")
        if not user_reference_id:
            return api_response(
                action="validation_error",
                message="User reference ID is required.",
                status="error"
            )

        # Prevent self-update
        if user_reference_id == request.user.reference_id:
            return api_response(
                action="validation_error",
                message="You cannot update your own profile via this endpoint.",
                status="error"
            )

        # Find the invited user
        user = User.objects.filter(
            reference_id=user_reference_id, is_deleted=False).first()
        if not user:
            return api_response(
                action="data_not_retrieved",
                message="No valid invited user found to update.",
                status="error"
            )

        if not user.is_active:
            return api_response(
                action="data_not_retrieved",
                message="This user account is deactivated and cannot be updated.",
                status="error"
            )

        # Only allow updating allowed fields
        allowed_fields = {"first_name", "last_name", "email", "role", "phone_number"}
        update_data = {field: request.data[field]
                       for field in allowed_fields if field in request.data}

        if not update_data:
            return api_response(
                action="validation_error",
                message="At least one of first_name, last_name, email, role, or phone_number must be provided.",
                status="error"
            )

        # Validate admin permissions
        has_permission, error_message = self._validate_admin_permissions(
            request, user)
        if not has_permission:
            return api_response(
                action="validation_error",
                message=error_message,
                status="failed"
            )

        serializer = InvitedUserProfileUpdateSerializer(
            user, data=update_data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return api_response(
                data=serializer.data,
                action="data_updated",
                message="Invited user profile updated successfully.",
                status="success"
            )
        else:
            first_error = None
            for errors in serializer.errors.values():
                if isinstance(errors, list) and errors:
                    first_error = errors[0]
                    break
                elif errors:
                    first_error = errors
                    break
            message = first_error or "Failed to update invited user profile."
            return api_response(
                data=serializer.errors,
                action="validation_error",
                message=message,
                status="error"
            )


class UserVerifyView(generics.UpdateAPIView):
    # authentication_classes = [JWTAccessTokenAuthentication]
    # permission_classes = [IsAuthenticated]
    serializer_class = UserVerifySerializer

    def update(self, request, *args, **kwargs):
        try:
            # Extract parameters from the URL
            # email = request.data.get("email")
            # role = request.data.get("role")
            user_reference_id = request.data.get("user_reference_id")
            owner_user_reference_id = request.data.get("owner_user_id")
            organization_id = request.data.get("organization_id")
            password = request.data.get("password")
            verification_token = request.data.get("verification_token")
            verification_uid = request.data.get("verification_uid")

            if not all([verification_token, verification_uid]):
                return api_response(
                    action="validation_error",
                    message="Verification token is required",
                )

            # Verify the parameters (optional)
            if not user_reference_id or not password:
                return api_response(
                    action="data_not_retrieved",
                    message="organization_id and user_reference_id is required",
                )
            
            try:
                # Verify token
                user_id = force_str(urlsafe_base64_decode(verification_uid))
                user = User.objects.get(pk=user_id)
                if not default_token_generator.check_token(user, verification_token):
                    return api_response(
                        action="validation_error",
                        message="Invalid or expired verification token",
                        status_code=400,
                    )
            except (TypeError, ValueError, OverflowError, User.DoesNotExist):
                return api_response(
                    action="validation_error",
                    message="Invalid verification data"
                )

            owner_user = User.objects.filter(
                reference_id=owner_user_reference_id
            ).first()
            # confirm_password = request.data.get("confirm_password")
            # feature_manager = SubscriptionFeatureManager(owner_user)

            # Retrieve the user object based on the email (or other fields as necessary)
            users = User.objects.filter(reference_id=user_reference_id)

            if users.count() == 0:
                return api_response(
                    action="data_not_retrieved", message="No valid User found to update"
                )

            if users.count() > 1:
                return api_response(
                    action="data_not_retrieved",
                    message="Multiple users found please resolve the conflict",
                )
            user = users.first()
            # Verify the organization_id and add the organization
            # to the user (if needed)
            try:
                organization = Organization.objects.get(
                    reference_id=organization_id, is_active=True
                )
            except Organization.DoesNotExist:
                return api_response(
                    action="data_not_retrieved", message="No valid organization found"
                )

            # Check if the organization is in the user's organizations
            if not user.organizations.filter(id=organization.id).exists():
                return api_response(
                    action="data_not_retrieved", message="No valid organization found"
                )
            if password:
                user.set_password(password)
                user.is_active = True
                user.is_invited = True
                user.save()

            return api_response(
                action="data_updated",
                status="success",
                message=f"{user.get_decrypted_email()} user(s) successfully updated.",
                data=[],
            )

        except Exception as e:
            # Log and return an error response
            logger.error(f"Error verifying user: {e}")
            return api_response(
                action="data_not_updated",
                message="An error occurred while updating the user object",
            )


class InvitedUsersListView(APIView):
    """
    API View to get a list of all
    invited users (with activation token)
    and their associated organizations.
    """

    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]  # Modify as per your permission requirements

    def get(self, request, *args, **kwargs):
        organisation_reference_id = request.query_params.get(
            "organisation_reference_id"
        )
        if request.user.role not in ["org_superadmin", "org_admin"]:
            return api_response(
                data=[],
                action="unauthorized_access",
                status="success",
            )
        try:
            # Fetch all users who have an activation token (invited users)
            # invited_users = User.objects.filter(is_invited=True)
            invited_users = User.objects.all()
            # If an organization reference ID is provided,
            # filter the users by organization
            if organisation_reference_id:
                invited_users = invited_users.filter(
                    organizations__reference_id=organisation_reference_id,
                    organizations__is_active=True,
                )

            # Serialize the data
            serializer = InvitedUserSerializer(invited_users, many=True)
            return api_response(
                data=serializer.data,
                action="data_retrieved",
                status="success",
                message="Invited users fetched successfully",
            )
        except Exception as e:
            logger.error(f"Error fetching invited users: {e}")
            return api_response(
                action="data_not_retrieved",
                message="An error occurred while fetching invited users.",
                status="error",
            )


class DeleteUserFromTrashView(generics.DestroyAPIView):
    """
    View to permanently delete users from trash.
    For org_superadmin: Can delete themselves and all associated users/subscriptions
    For org_admin: Can delete other users but not org_superadmin
    """
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def destroy(self, request, *args, **kwargs):
        try:
            user_reference_id = request.query_params.get("user_reference_id")
            organisation_reference_id = request.query_params.get(
                "organisation_reference_id")

            if not user_reference_id or not organisation_reference_id:
                return api_response(
                    action="data_not_retrieved",
                    message="User reference ID and Organisation reference ID required"
                )

            # Find the user and verify organization association
            user = User.objects.filter(
                reference_id=user_reference_id,
                organizations__reference_id=organisation_reference_id,
            ).first()

            if not user:
                return api_response(
                    action="data_not_retrieved",
                    message="No valid user found"
                )

            # Check if user is trying to delete themselves
            is_self_deletion = user_reference_id == request.user.reference_id

            # Block self-deletion for everyone except org_superadmin
            if is_self_deletion and user.role != "org_superadmin":
                return api_response(
                    action="validation_error",
                    message="You are not allowed to delete your own account."
                )
            # Permission checks
            if user.role == "org_superadmin":
                if not is_self_deletion:
                    return api_response(
                        action="validation_error",
                        message="Only the organization super admin can delete themselves"
                    )

            if request.user.role not in ["org_superadmin", "org_admin"]:
                return api_response(
                    action="unauthorized_access",
                    message="You don't have permission to perform this action"
                )
            if request.user.role == "org_admin" and user.role == "org_superadmin":
                return api_response(
                    action="validation_error",
                    message="Organization admin cannot delete organization super admin"
                )

            # Use the service for deletion
            from accounts.services.soft_delete_service import soft_delete_user_and_related
            soft_delete_user_and_related(user.email, deleted_by=request.user)

            return api_response(
                action="data_deleted",
                message=f"User {user_reference_id} deleted successfully from organization {organisation_reference_id}",
                status="success"
            )

        except Exception as e:
            logger.error(
                f"Error deleting user from organization: {str(e)}", exc_info=True)
            return api_response(
                action="server_error",
                message="An error occurred while deleting the user from the organization",
                status="error"
            )


class OrganizationAllUpdateView(generics.UpdateAPIView):
    queryset = Organization.objects.all()
    serializer_class = OrganizationSerializer
    lookup_field = "reference_id"
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def partial_update(self, request, *args, **kwargs):
        if request.user.role not in ["org_superadmin"]:
            return api_response(
                data=[],
                action="unauthorized_access",
                message="You do not have permission to perform this action.",
                status="failed",
            )

        try:
            instance = self.get_object()

            # Check if the organization is soft-deleted
            if getattr(instance, "is_deleted", False):
                return api_response(
                    action="data_not_retrieved",
                    message="No active organization found with the provided reference ID.",
                    status="error",
                )
            # Copy request data to make it mutable
            data = request.data.copy()

            data.update({
                'is_primary': True,
                'is_active': True
            })

            serializer = self.get_serializer(
                instance, data=data, partial=True, context={"request": request}
            )

            if serializer.is_valid(raise_exception=True):
                self.perform_update(serializer)
                logger.info("Updating the organization details in the database.")
                # Get the decrypted name, number, and tax_id from the serializer validated data
                show_on_invoice = serializer.validated_data.get("show_on_invoice")
                if not show_on_invoice:
                    show_on_invoice = data.get('show_in_invoice')

                update_kwargs = {}
                decrypted_name = None
                decrypted_number = None
                decrypted_tax_id = None

                if show_on_invoice:
                    decrypted_name = instance.get_decrypted_name()
                    decrypted_number = instance.get_decrypted_number()
                    decrypted_tax_id = instance.get_decrypted_tax_id()

                # Prepare custom fields for Stripe invoice settings
                if show_on_invoice:
                    invoice_custom_fields = []
                    if decrypted_name:
                        invoice_custom_fields.append(
                            {"name": "Organization", "value": decrypted_name})
                    if decrypted_number:
                        invoice_custom_fields.append(
                            {"name": "Org ID", "value": decrypted_number})
                    if decrypted_tax_id:
                        invoice_custom_fields.append(
                            {"name": "VAT Number", "value": decrypted_tax_id})
                else:
                    invoice_custom_fields = [{"name": "VAT Number", "value": ""}, {
                        "name": "Org ID", "value": ""}, {"name": "Organization", "value": ""}]

                # Update Stripe customer details for the org_superadmin user
                try:
                    if show_on_invoice and invoice_custom_fields:
                        update_kwargs["invoice_settings"] = {
                            "custom_fields": invoice_custom_fields}
                        update_kwargs["metadata"] = {
                            "user_id": request.user.id, "organization": instance.id, "organization_name": decrypted_name if decrypted_name else ""}
                    else:
                        update_kwargs["invoice_settings"] = {
                            "custom_fields": ""}
                        update_kwargs["metadata"] = {"user_id": request.user.id, "organization": "", "organization_name": ""}

                    if update_kwargs:
                        stripe_customer = get_or_create_customer(request.user)
                        stripe.Customer.modify(
                            stripe_customer.id, **update_kwargs)
                except Exception as e:
                    logger.error(
                        f"Error updating Stripe customer details: {str(e)}", exc_info=True)
                return api_response(
                    action="data_updated",
                    message="Organization updated successfully",
                    data=serializer.data,
                )

        except NotFound:
            return api_response(
                action="data_not_retrieved",
                message="Organization not found",
            )

        except ValidationError as e:
            error_message = str(e.detail) if hasattr(e, "detail") else str(e)
            return api_response(
                action="validation_error",
                message=error_message
            )

        except Exception as e:
            logger.error(
                f"Server Error in organization update: {str(e)}", exc_info=True)
            return api_response(
                action="server_error",
                message="An error occurred while updating the organization",
            )

    def update(self, request, *args, **kwargs):
        request._full_data = request.data.copy()
        return self.partial_update(request, *args, **kwargs)


class OrganizationRetrieveView(generics.RetrieveAPIView):
    queryset = Organization.objects.all()
    serializer_class = OrganizationSerializer
    lookup_field = "reference_id"  # Retrieve by reference ID
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def retrieve(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return api_response(
                action="data_retrieved",
                data=serializer.data,
            )
        except NotFound as e:
            print(e)
            return api_response(
                action="data_not_retrieved",
                message="No valid documents found in trash",
            )
        except Exception as e:
            print(e)
            return api_response(
                action="server_error",
                message=f"Server Error: {e}",
            )


class LogoutView(APIView):
    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            # Find and update the user's active session
            active_session = SessionTrack.objects.filter(
                user=request.user.id, is_logged_in=True
            ).first()

            if active_session:
                active_session.is_logged_in = False
                active_session.last_logged_out_at = timezone.now()
                active_session.save()

                return api_response(
                    action="data_updated",
                    message="Successfully logged out",
                    status="success",
                )

            return api_response(
                action="data_not_updated",
                message="No active session found",
                status="error",
            )

        except Exception as e:
            return api_response(action="server_error", message=str(e))


class LogoutAllDevicesView(APIView):
    # No authentication required  No permissions required

    def post(self, request):
        try:
            user_reference_id = request.data.get("user_reference_id")

            if not user_reference_id:
                return api_response(
                    action="data_not_retrieved", message="User reference ID is required"
                )
            # Find user by reference ID
            user = User.objects.filter(reference_id=user_reference_id).first()

            if not user:
                return api_response(
                    action="data_not_retrieved", message="User not found"
                )

            # Update all active sessions for this user
            active_sessions = SessionTrack.objects.filter(user=user, is_logged_in=True)
            for session in active_sessions:
                blacklist_token(
                    session.generated_token, expiry_time=3600
                )  # Implement token revocation logic

            sessions_count = active_sessions.update(
                is_logged_in=False, last_logged_out_at=timezone.now()
            )

            return api_response(
                action="data_updated",
                message=f"Successfully logged out from all devices",
                data=sessions_count,
                status="success",
            )

        except Exception as e:
            return api_response(action="server_error", message=str(e))


class UserActiveStatusView(APIView):
    """
    API View to toggle user's active status based on email
    """

    authentication_classes = [JWTAccessTokenAuthentication]
    permission_classes = [IsAuthenticated]

    def patch(self, request):
        try:
            email = request.data.get("email")
            status = request.data.get("status")
            organisation_id = request.data.get("organisation_id")
            if request.user.role not in ["org_superadmin", "org_admin"]:
                return api_response(
                    data=[],
                    action="unauthorized_access",
                    status="success",
                )
            if not email:
                return api_response(
                    action="validation_error",
                    message="Email is required",
                    status="error",
                )

            if status not in [True, False]:
                return api_response(
                    action="validation_error",
                    message="Status must be true or false",
                    status="error",
                )

            # Find user by encrypted email
            target_user = User.objects.filter(
                private_email=generate_hmac_token(email), is_deleted=False).first()

            if not target_user:
                return api_response(
                    action="data_not_retrieved",
                    message="User not found",
                    status="error",
                )

            # Prevent users from modifying their own status
            if target_user.id == request.user.id:
                return api_response(
                    action="validation_error",
                    message="You cannot modify your own active status",
                    status="error",
                )

            # Prevent modifying org_superadmin users
            if target_user.role == "org_superadmin":
                return api_response(
                    action="validation_error",
                    message="Cannot modify status of organization super admin",
                    status="error",
                )

             # Get user's active subscription
            try:
                user_subscription = UserSubscription.objects.get(
                    user=request.user,
                    status__in=['active', 'trial'],
                    is_deleted=False
                )

                # Check if plan requires organization details
                if user_subscription.plan.plan_category in ['business_monthly', 'business_yearly', 'enterprise_monthly', 'enterprise_yearly']:
                    if not organisation_id:
                        return api_response(
                            action="validation_error",
                            message="Organization is required for business and enterprise subscriptions",
                            status="error"
                        )

                    # Get organization details
                    organization = Organization.objects.filter(
                        reference_id=organisation_id,
                        created_by=request.user.reference_id,
                    ).first()

                    if not organization:
                        return api_response(
                            action="validation_error",
                            message="Organization not found",
                            status="error"
                        )

                    # Check if organization has domain
                    if not organization.org_domain:
                        return api_response(
                            action="validation_error",
                            message="Organization domain is required for business and enterprise subscriptions",
                            status="error"
                        )
            except UserSubscription.DoesNotExist:
                return api_response(
                    action="validation_error",
                    message="No active subscription found",
                    status="error"
                )
            # TODO: Mange the total account limit based on this, If user status is active and marke is False, then decrese the

            # Before activating the user, check if activating would exceed the allowed user limit
            feature_manager = SubscriptionFeatureManager(request.user)
            user_create_status = feature_manager.check_feature_availability("users")
            if not target_user.is_active and status is True:
                available_limit = user_create_status.get("limit")
                if organization.total_active_user >= available_limit:
                    return api_response(
                        action="validation_error",
                        message="User limit reached. Please upgrade your plan to add more users.",
                        status="error"
                    )
            with transaction.atomic():
                # Check if status is same as current user status
                if target_user.is_active == status:
                    return api_response(
                        action="no_change",
                        message=f"User is already {'active' if status else 'inactive'}",
                        data={"email": email, "is_active": status},
                        status="success",
                    )

                # Update organization's total_active_user count
                if not target_user.is_active and status is True:
                    # Activating user
                    organization.total_active_user += 1
                    organization.save(update_fields=["total_active_user"])
                elif target_user.is_active and status is False:
                    # Deactivating user
                    if organization.total_active_user > 0:
                        organization.total_active_user -= 1
                        organization.save(update_fields=["total_active_user"])

                # Update user's active status
                target_user.is_active = status
                if status == True:
                    target_user.is_deleted = False
                    target_user.reactivated_at = timezone.now()
                    target_user.reactivated_by = request.user.reference_id
                target_user.save()

            return api_response(
                action="data_updated",
                message=f"User status successfully {'activated' if status else 'deactivated'}",
                data={"email": email, "is_active": status},
                status="success",
            )

        except Exception as e:
            logger.error(f"Error updating user status: {str(e)}")
            return api_response(
                action="server_error",
                message="An error occurred while updating user status",
                status="error",
            )


class MobileNumberChangeVerifyView(APIView):
    permission_classes = [IsAuthenticated]
    authentication_classes = [JWTAccessTokenAuthentication]

    def post(self, request, *args, **kwargs):
        """
        Send OTP to both old and new phone numbers for verification
        """
        new_phone_number = request.data.get('new_phone_number')

        try:
            # Get user's current phone number
            current_phone = request.user.get_decrypted_phone_number()
            if not current_phone:
                logger.error(
                    f"Phone number change failed - No existing phone number found for user: {request.user.id}")
                return api_response(
                    action="validation_error",
                    message="No existing phone number found",
                    status="error"
                )

            # Validate new phone number
            if not new_phone_number:
                logger.error(
                    f"Phone number change failed - New phone number not provided for user: {request.user.id}")
                return api_response(
                    action="validation_error",
                    message="New phone number is required",
                    status="error"
                )

            # Check if user is org_superadmin
            if request.user.role != "org_superadmin":
                logger.error(
                    f"Email change failed - User is not org_superadmin: {request.user.id}")
                return api_response(
                    action="validation_error",
                    message="You do not have permission to change your email address",
                    status="error"
                )

            # Check if new phone number is already registered
            if User.objects.filter(private_phone=generate_hmac_token(new_phone_number), is_deleted=False).exists():
                logger.error(
                    f"Phone number change failed - Phone number already registered: {new_phone_number}")
                return api_response(
                    action="validation_error",
                    message="This phone number is already registered",
                    status="error"
                )

            # Check if user can update contact
            try:
                contact_service = ContactUpdateService(user=request.user)
                if not contact_service.can_update_contact(contact_type='phone'):
                    logger.error(
                        f"Phone number change failed - 30-day restriction not met for user: {request.user.id}")
                    return api_response(
                        action="validation_error",
                        message="You can only update your phone number once every 30 days",
                        status="error"
                    )
            except Exception as e:
                logger.error(
                    f"Error checking contact update eligibility - User: {request.user.id} - Error: {str(e)}")
                return api_response(
                    action="server_error",
                    message="Failed to check contact update eligibility. Please try again.",
                    status="error"
                )

            # Generate OTPs
            current_otp = generate_otp()
            new_otp = generate_otp()
            logger.info(
                f"Generated OTPs for phone number change - User: {request.user.id}")

            # Send OTPs via SMS
            try:
                # Send OTP to current number
                send_otp = send_otp_via_sms(phone_number=current_phone, otp=current_otp)
                if send_otp["status"] == "failed":
                    logger.error(
                        f"Failed to send OTP to current number: {current_phone} - Error: {send_otp['message']}")
                    return api_response(message=send_otp["message"])

                # Send OTP to new number
                send_otp = send_otp_via_sms(phone_number=new_phone_number, otp=new_otp)
                if send_otp["status"] == "failed":
                    logger.error(
                        f"Failed to send OTP to new number: {new_phone_number} - Error: {send_otp['message']}")
                    return api_response(message=send_otp["message"])

                # Store OTPs in OneTimePassword model
                # For current number
                otp_obj, _ = OneTimePassword.objects.update_or_create(
                    phone_number=generate_hmac_token(current_phone),
                    defaults={
                        'otp_code': current_otp,
                        'expiration_time': timezone.now() + timezone.timedelta(minutes=10),
                    }
                )

                # For new number
                new_otp_obj, _ = OneTimePassword.objects.update_or_create(
                    phone_number=generate_hmac_token(new_phone_number),
                    defaults={
                        'otp_code': new_otp,
                        'expiration_time': timezone.now() + timezone.timedelta(minutes=10),
                        'is_verified': False,
                        'is_link_verified_phone': False
                    }
                )

                logger.info(
                    f"OTPs stored successfully for phone number change - User: {request.user.id}")

                return api_response(
                    action="data_created",
                    message="OTPs sent successfully",
                    status="success",
                    data={
                        'current_phone': current_phone,
                        'current_otp': current_otp,
                        'new_phone': new_phone_number,
                        'new_otp': new_otp,
                        'expires_in': '10 minutes'
                    }
                )

            except Exception as e:
                logger.error(
                    f"Failed to send OTPs for phone number change - User: {request.user.id} - Error: {str(e)}")
                return api_response(
                    action="data_not_created",
                    message="Failed to send OTPs. Please try again.",
                    status="failed"
                )

        except Exception as e:
            logger.error(
                f"Unexpected error in phone verification - User: {request.user.id} - Error: {str(e)}")
            return api_response(
                action="server_error",
                message="An unexpected error occurred",
                status="failed"
            )

    def put(self, request, *args, **kwargs):
        """
        Verify OTPs for both old and new phone numbers and update contact history
        """
        current_otp = request.data.get('current_otp')
        new_otp = request.data.get('new_otp')
        new_phone_number = request.data.get('new_phone_number')

        if not all([current_otp, new_otp, new_phone_number]):
            logger.error(
                f"Phone verification failed - Missing required parameters for user: {request.user.id}")
            return api_response(
                action="validation_error",
                message="Current OTP, new OTP, and new phone number are required",
                status="error"
            )

        try:
            # Check if user is org_superadmin
            if request.user.role != "org_superadmin":
                logger.error(
                    f"Email change failed - User is not org_superadmin: {request.user.id}")
                return api_response(
                    action="validation_error",
                    message="You do not have permission to change your phone number",
                    status="error"
                )

            current_phone = request.user.get_decrypted_phone_number()

            # Verify OTP for current number
            current_otp_obj = OneTimePassword.objects.filter(
                phone_number=generate_hmac_token(current_phone),
                otp_code=current_otp,
                expiration_time__gt=timezone.now(),
            ).first()

            if not current_otp_obj:
                logger.error(
                    f"Phone verification failed - Invalid/expired OTP for current number - User: {request.user.id}")
                return api_response(
                    action="validation_error",
                    message="Invalid or expired OTP for current number",
                    status="error"
                )

            # Verify OTP for new number
            new_otp_obj = OneTimePassword.objects.filter(
                phone_number=generate_hmac_token(new_phone_number),
                otp_code=new_otp,
                expiration_time__gt=timezone.now(),
                is_verified=False
            ).first()

            if not new_otp_obj:
                logger.error(
                    f"Phone verification failed - Invalid/expired OTP for new number - User: {request.user.id}")
                return api_response(
                    action="validation_error",
                    message="Invalid or expired OTP for new number",
                    status="error"
                )

            # Mark OTPs as verified
            current_otp_obj.is_verified = True
            current_otp_obj.is_link_verified_phone = True
            current_otp_obj.save()

            new_otp_obj.is_verified = True
            new_otp_obj.is_link_verified_phone = True
            new_otp_obj.save()

            logger.info(
                f"OTPs verified successfully for phone number change - User: {request.user.id}")

            # Update user's phone number and save contact history
            try:
                contact_service = ContactUpdateService(request.user)
                if not contact_service.can_update_contact(contact_type='phone'):
                    logger.error(
                        f"Phone number update failed - 30-day restriction not met - User: {request.user.id}")
                    return api_response(
                        action="validation_error",
                        message="You can only update your phone number once every 30 days",
                        status="error"
                    )

                # Update phone number
                try:
                    # Format phone number to ensure it has + prefix
                    if not new_phone_number.startswith('+'):
                        new_phone_number = '+' + new_phone_number

                    logger.info(
                        f"Formatted Phone Number: {new_phone_number} and it's type is {type(new_phone_number)}")

                    contact_service.update_contact(
                        contact_type='phone',
                        new_value=new_phone_number
                    )
                    logger.info(
                        f"Phone number updated and contact history saved successfully - User: {request.user.id}")
                except Exception as e:
                    logger.error(
                        f"Failed to update contact - User: {request.user.id} - Error: {str(e)}")
                    return api_response(
                        action="server_error",
                        message="Failed to update phone number. Please try again.",
                        status="error"
                    )

                return api_response(
                    action="data_updated",
                    message="Phone number updated successfully",
                    status="success",
                    data={
                        'phone': new_phone_number
                    }
                )

            except Exception as e:
                logger.error(
                    f"Failed to update phone number - User: {request.user.id} - Error: {str(e)}")
                return api_response(
                    action="server_error",
                    message="Failed to update phone number. Please try again.",
                    status="error"
                )

        except Exception as e:
            logger.error(
                f"Unexpected error in phone verification - User: {request.user.id} - Error: {str(e)}")
            return api_response(
                action="server_error",
                message="An unexpected error occurred",
                status="failed"
            )


class MobileSignupOtpVerifyView(generics.RetrieveUpdateDestroyAPIView):
    """
    API view for verifying mobile OTP during signup process.
    No authentication required as this is for new user registration.
    """
    permission_classes = [AllowAny]
    authentication_classes = []
    serializer_class = OtpVerifyViewSerilizer

    def get_object(self):
        """Retrieve the OTP instance using the phone number."""
        phone_number = self.kwargs.get("phone_number")
        # Validate the phone number format
        if not re.match(r"^\+\d{1,3}\d{10}$", phone_number):
            # logger.warning(f"Invalid phone number format: {phone_number}")
            return api_response(
                action="validation_error",
                message="Invalid phone number format. Please provide a valid mobile number.",
            )
        try:
            hmac_number = generate_hmac_token(phone_number)
            # logger.info(f"Retrieving OTP instance for phone number: {phone_number}")
            return OneTimePassword.objects.get(phone_number=hmac_number)
        except OneTimePassword.DoesNotExist:
            # logger.error(f"OTP instance not found for phone number: {phone_number}")
            return None

    def put(self, request, *args, **kwargs):
        otp_instance = self.get_object()
        verify_link = request.query_params.get("verify_link", None)
        context = {}
        context["is_verfied"] = False
        document_preview = request.data.get(
            "document_preview", None
        )  # Using .get() avoids errors if it's missing
        if not otp_instance:
            logger.error("OTP instance not retrieved")
            return api_response(action="data_not_retrieved")

        merged_data = {
            **request.data,
            **{"phone_number": self.kwargs.get("phone_number")},
        }
        serializer = self.get_serializer(otp_instance, data=merged_data)
        serializer.is_valid(raise_exception=True)
        otp_code = serializer.validated_data.get("otp_code")
        phone_number = serializer.validated_data.get("phone_number")
        current_time = timezone.now()
        if current_time > otp_instance.expiration_time:
            # logger.warning(f"OTP expired for phone number: {phone_number}")
            return api_response(
                message=_(
                    "The OTP has expired. Please request a new one and try again."
                )
            )

        if otp_instance.otp_code != otp_code:
            # logger.warning(f"OTP code mismatch for phone number: {phone_number}")
            return api_response(action="wrong_otp", message=serializer.errors)

        if (
                otp_instance.is_verified
                and otp_instance.is_link_verified_phone
                and not document_preview
        ):
            # logger.warning(f"OTP already verified for phone number: {phone_number}")
            return api_response(
                action="wrong_otp",
                message=_("This OTP has already been used. Please request a new one."),
            )

        if not verify_link:
            otp_instance.is_verified = True
            otp_instance.is_link_verified_phone = True
        else:
            otp_instance.is_link_verified_phone = True
            otp_instance.is_phone_verified = True
        otp_instance.is_sent = False
        otp_instance.save()
        context = save_user_details(phone_number, "otp", request=request)
        # Check if user is already logged in somewhere
        user_detail_dict = context.get(
            "user_details", None
        )  # Assuming save_user_details returns user in context

        user_reference_id = user_detail_dict.get("user_refernce_id")
        user = User.objects.filter(reference_id=user_reference_id).first()

        if user.is_deleted:
            user.is_deleted = False
            user.is_active = True
            user.deleted_at = None
            user.self_deleted = False
            user.save(update_fields=["is_deleted", "is_active",
                      "deleted_at", "self_deleted"])
            SessionTrack.objects.filter(user=user).update(is_logged_in=False)

        subscribed_trial = SubscriptionFeatureManager(user)
        context["subscription"] = subscribed_trial.get_active_subscription_features()

        if user and not verify_link:
            existing_session = SessionTrack.objects.filter(
                user=user, is_logged_in=True
            ).first()
            if existing_session:
                return api_response(
                    data={"user_reference_id": user_reference_id},
                    action="unauthorized_access",
                    message="Please logout from other devices and try again.",
                )

            # Create new session
            SessionTrack.objects.update_or_create(
                user=user,
                ip_address=get_client_ip(request),
                is_logged_in=True,
                generated_token=user_detail_dict["access_token"],
                # ip_address=request.META.get('REMOTE_ADDR'),
                # user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

        context["is_verfied"] = otp_instance.is_verified
        context["is_phone_verified"] = otp_instance.is_phone_verified
        context["user_details"]["user_type"] = "otp"
        context["user_details"]["meta_data"] = get_country_info()

        if not verify_link:
            log_user_action(
                request=request,
                action="OTP verified and user created for phone number",
                status="success",
                details={"type": "user_created"},
            )
        # logger.info(f"OTP verified and user created for phone number: {phone_number}")
        return api_response(
            data=context,
            action="data_updated",
            status="success",
        )
