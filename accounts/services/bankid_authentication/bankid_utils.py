import base64
import hashlib
import hmac
import io
import logging
# import uuid
import time

import qrcode  # type: ignore
import requests
from cryptography.fernet import Fernet
from django.conf import settings
from django.contrib.auth.hashers import check_password, make_password
# from django.db import transaction
from django.utils import translation
from django.utils.translation import gettext as _
from dotenv import load_dotenv

from accounts.constants.hintcode_responses import BANKID_RESPONSES
from accounts.models.organisation import Organization
from accounts.models.users import User
from accounts.services.jwt_authentication.authentication import (
    JWTTokenManager,
    fernet_decrypt,
)
from accounts.services.jwt_authentication.authentication import (
    generate_hmac_token,
)

load_dotenv()
# Set up logger
logger = logging.getLogger(__name__)

cipher_suite = Fernet(settings.ENCRYPTION_KEY)


def generate_qr_code(auth_response, verification_start_time):
    """
    Generates a QR code based on the authentication response.

    :param auth_response: Dictionary containing the authentication response
        with keys "qrStartToken" and "qrStartSecret".
    :param verification_start_time: time of the authentication order creation.
    :return: QR code image.
    """
    qr_start_token = auth_response["qrStartToken"]
    qr_start_secret = auth_response["qrStartSecret"]

    qr_time = str(int(time.time() - verification_start_time))

    qr_auth_code = hmac.new(
        qr_start_secret.encode(), qr_time.encode(), hashlib.sha256
    ).hexdigest()
    qr_data = str.join(".", ["bankid", qr_start_token, qr_time, qr_auth_code])

    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(qr_data)
    qr.make(fit=True)

    logger.info(f"Generated QR code with data: {qr_data}")

    img = qr.make_image(fill="black", back_color="white")
    return img


def get_bankid_message(status, hint_code):
    """
    Function to get the appropriate BankID message based on status and hint code.

    :param status: The status to match.
    :param hint_code: The hintcode to match.
    :return: The appropriate message based on the criteria.
    """
    logger.info(f"Fetching BankID message for status: {status}, hint_code: {hint_code}")
    for message in BANKID_RESPONSES:
        if message["status"] == status and hint_code in message.get("hintCode", []):
            logger.info(f"Found matching message: {message['message']}")
            return message["message"]
    logger.warning(
        f"No matching BankID message found for status: {status}, hint_code: {hint_code}"
    )
    return "Something went Wrong!!"


def generate_qr_code_base64(response_data, verification_start_time):
    """Generates a base64-encoded QR code image from response data."""
    logger.info("Generating base64-encoded QR code")
    auth_qrcode_image = generate_qr_code(response_data, verification_start_time)
    buffer = io.BytesIO()
    auth_qrcode_image.save(buffer, format="PNG")
    buffer.seek(0)
    logger.info("Generated base64 QR code")  # Log the first 30 chars for brevity
    return base64.b64encode(buffer.read()).decode("utf-8")


def base64_name(string):
    logger.info(f"Decoding base64 string: {string[:30]}...")
    return base64.b64decode(string).decode("utf-8")


def ensure_base64_padding(base64_string):
    logger.info(
        f"Ensuring base64 padding for string: {base64_string[:30]}..."
    )  # Log part of the string for privacy
    missing_padding = len(base64_string) % 4
    if missing_padding != 0:
        base64_string += "=" * (4 - missing_padding)
        logger.info("Added padding to base64 string")
    return base64_string


def get_client_ip(request):
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


def get_country_info():
    # Set a default value for ip
    ip = "Unknown"
    country = "Unknown"
    country_code = "Unknown"

    url_get_public_ip = "https://api.ipify.org?format=json"

    try:
        # Send a request to the API and get the response
        response = requests.get(url_get_public_ip)
        # Check if the request was successful
        if response.status_code == 200:
            data = response.json()
            ip = data.get("ip", "Unknown")
            url_country_info = f"https://get.geojs.io/v1/ip/geo/{ip}.json"
            response_country_info = requests.get(url_country_info)
            if response_country_info.status_code == 200:
                data_country_info = response_country_info.json()
                country = data_country_info.get("country", "Unknown")
                country_code = data_country_info.get("country_code", "Unknown")
                return ip, country, country_code
            else:
                logger.error(
                    f"Failed to retrieve country info for IP {ip}. Status code: {response_country_info.status_code}")
                return ip, "Unknown", "Unknown"


        else:
            logger.error(f"Failed to retrieve public IP. Status code: {response.status_code}")
            return "Unknown", "Unknown", "Unknown"
    except requests.exceptions.RequestException as e:
        logger.error(f"Error occurred while retrieving country info: {str(e)}")
        return "Unknown", "Unknown", "Unknown"
    except Exception as e:
        logger.error(f"Unexpected error while retrieving country info: {str(e)}")
        return "Unknown", "Unknown", "Unknown"


def i18n_message(message, language_code):
    """
    Translate a message to the specified language.

    :param message: The message to translate.
    :param language_code: The language code (e.g., 'es', 'fr').
    :return: Translated message.
    """
    # Activate the desired language
    logger.info(f"Translating message '{message}' to language: {language_code}")
    translation.activate(language_code)

    # Translate the message
    translated_message = _(message)

    # Deactivate the language to revert to the default
    translation.deactivate()
    logger.info(f"Translated message: {translated_message}")

    return translated_message


def save_user_details(
        personal_number, user_type=None, personal_info=None, is_onetime=False, request=None
):
    logger.info(f"Saving user details for personal number: {personal_number}")
    jwt_manager = JWTTokenManager()
    context = {}
    hash_password = make_password(personal_number)
    print("hash_password save_user_details", hash_password)
    phone_number = cipher_suite.encrypt(personal_number.encode()).decode()
    user_phone = generate_hmac_token(personal_number)
    existing_user = User.objects.filter(private_phone=user_phone).first()
    # if request:
    #     ip_address = get_client_ip(request)
    # else:
    #     ip_address = None

    if personal_info:
        first_name = personal_info.get("given_name", None)
        last_name = personal_info.get("surname", None)

    if existing_user:

        if existing_user.is_deleted:
            profile_updated = False
        else:
            profile_updated = bool(
                existing_user.first_name and existing_user.last_name and existing_user.email
            )
        if not existing_user.organizations.filter(is_deleted=False).exists():
            organisation = Organization.objects.create(
                created_by=existing_user.reference_id, is_primary=True
            )
            existing_user.organizations.set([organisation])
            existing_user.save()
        else:
            # org_superadmin
            if existing_user.role == "org_superadmin":
                organisation = Organization.objects.filter(
                    users__reference_id=existing_user.reference_id, is_primary=True
                ).first()
            else:
                organisation = Organization.objects.filter(
                    users__reference_id=existing_user.reference_id
                ).first()
        access_token = jwt_manager.generate_token(existing_user.reference_id)
        refresh_token = jwt_manager.generate_token(
            existing_user.reference_id, token_type="refresh"
        )
        if personal_info and not profile_updated:
            existing_user.first_name = first_name
            existing_user.last_name = last_name
            existing_user.save()

        if is_onetime:
            access_token = "onetime" + access_token

        if user_type:
            existing_user.user_type = user_type
            existing_user.save()

        context["user_details"] = {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "profile_updated": profile_updated,
            "first_name": (
                existing_user.first_name if existing_user.first_name else None
            ),
            "last_name": (existing_user.last_name if existing_user.last_name else None),
            "user_refernce_id": existing_user.reference_id,
            "email": (
                fernet_decrypt(existing_user.email) if existing_user.email else None
            ),
            "phone_number": (
                fernet_decrypt(existing_user.phone_number)
                if existing_user.phone_number
                else None
            ),
        }
        context["organisation_details"] = {
            "name": organisation.get_decrypted_name() if organisation.name else None,
            "number": organisation.get_decrypted_number() if organisation.number else None,
            "reference_id": (
                organisation.reference_id if organisation.reference_id else None
            ),
            "is_primary": organisation.is_primary if organisation.is_primary else None,
            "created_by": organisation.created_by if organisation.created_by else None,
        }
        return context
    # If User is not created then create new One
    new_user = User.objects.create(
        private_number=hash_password,
        phone_number=phone_number if user_type == "otp" else None,
        authentication_method=user_type,
        username=phone_number,
        private_phone=user_phone,
    )
    organisation = Organization.objects.create(
        created_by=new_user.reference_id, is_primary=True
    )
    new_user.organizations.set([organisation])
    new_user.save()
    # if new_user and user_type == "otp":
    #     new_user.username = phone_number
    #     new_user.save()
    access_token = jwt_manager.generate_token(new_user.reference_id)
    refresh_token = jwt_manager.generate_token(
        new_user.reference_id, token_type="refresh"
    )
    profile_updated = False
    if personal_info and not profile_updated:
        profile_updated = True
        new_user.first_name = first_name
        new_user.last_name = last_name
        new_user.save()

    if user_type:
        new_user.user_type = user_type
        new_user.save()

    context["user_details"] = {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "profile_updated": profile_updated,
        "user_refernce_id": new_user.reference_id,
        "first_name": (new_user.first_name if new_user.first_name else None),
        "last_name": (new_user.last_name if new_user.last_name else None),
        "email": fernet_decrypt(new_user.email) if new_user.email else None,
        "phone_number": (
            fernet_decrypt(new_user.phone_number) if new_user.phone_number else None
        ),
    }
    context["organisation_details"] = {
        "name": organisation.get_decrypted_name() if organisation.name else None,
        "number": organisation.get_decrypted_number() if organisation.number else None,
        "reference_id": (
            organisation.reference_id if organisation.reference_id else None
        ),
        "is_primary": organisation.is_primary if organisation.is_primary else None,
        "created_by": organisation.created_by if organisation.created_by else None,
    }

    return context


def handle_user_creation_sync(
    personal_number,
    jwt_manager,
    personal_info=None,
    is_onetime=False,
    client_ip=None,
    meta_data=None,
):
    """
    Synchronous version of handle_user_creation for use in Django views.
    """
    try:
        logger.info("Handling user creation synchronously", extra={"is_onetime": is_onetime})
        
        # Call the existing save_user_details function
        context = save_user_details(
            personal_number, "sweden_bank_id", personal_info, is_onetime
        )
        
        logger.info("User creation handled successfully")
        user_detail_dict = context.get("user_details", None)
        user_reference_id = user_detail_dict.get("user_refernce_id")
        
        # Update user meta_data if provided
        if user_reference_id and meta_data:
            user = User.objects.filter(reference_id=user_reference_id).first()
            if user:
                user.meta_data = meta_data
                user.save()
                logger.info(f"Updated meta_data for user {user_reference_id}")

        context["user_details"]["user_type"] = "sweden_bank_id"
        context["bank_id_info"] = meta_data
        
        return context
        
    except Exception as e:
        logger.error(
            "Error handling user creation synchronously", 
            extra={"error": str(e)}, 
            exc_info=True
        )
        raise


# def save_user_details(user_details, request=None):
#     try:
#         logger.info(
#             "Saving BankID user details",
#             extra={
#                 'personal_number': user_details.get('personal_number'),
#                 'ip_address': get_client_ip(request) if request else None
#             }
#         )
#         jwt_manager = JWTTokenManager()
#         context = {}
#         hash_password = make_password(user_details.get('personal_number'))
#         phone_number = cipher_suite.encrypt(user_details.get('personal_number').encode()).decode()
#         existing_user = User.objects.filter(private_number=hash_password).first()

#         if user_details.get('personal_info'):
#             first_name = user_details.get('personal_info').get("given_name", None)
#             last_name = user_details.get('personal_info').get("surname", None)

#         if existing_user:
#             profile_updated = bool(
#                 existing_user.first_name and existing_user.last_name and existing_user.email
#             )
#             if not existing_user.organizations:
#                 organisation = Organization.objects.create(
#                     created_by=existing_user.reference_id, is_primary=True
#                 )
#                 existing_user.organizations.set([organisation])
#                 existing_user.save()
#             else:
#                 # org_superadmin
#                 if existing_user.role == "others":
#                     organisation = Organization.objects.filter(
#                         users__reference_id=existing_user.reference_id, is_primary=True
#                     ).first()
#                 else:
#                     organisation = Organization.objects.filter(
#                         users__reference_id=existing_user.reference_id
#                     ).first()
#             access_token = jwt_manager.generate_token(existing_user.reference_id)
#             refresh_token = jwt_manager.generate_token(
#                 existing_user.id, token_type="refresh"
#             )
#             if user_details.get('personal_info') and not profile_updated:
#                 existing_user.first_name = first_name
#                 existing_user.last_name = last_name
#                 existing_user.save()

#             if user_details.get('is_onetime'):
#                 access_token = "onetime" + access_token
#             context["user_details"] = {
#                 "access_token": access_token,
#                 "refresh_token": refresh_token,
#                 "profile_updated": profile_updated,
#                 "first_name": (
#                     existing_user.first_name if existing_user.first_name else None
#                 ),
#                 "last_name": (existing_user.last_name if existing_user.last_name else None),
#                 "user_refernce_id": existing_user.reference_id,
#                 "email": (
#                     fernet_decrypt(existing_user.email) if existing_user.email else None
#                 ),
#                 "phone_number": (
#                     existing_user.phone_number if existing_user.phone_number else None
#                 ),
#             }
#             context["organisation_details"] = {
#                 "name": organisation.name if organisation.name else None,
#                 "number": organisation.number if organisation.number else None,
#                 "reference_id": (
#                     organisation.reference_id if organisation.reference_id else None
#                 ),
#                 "is_primary": organisation.is_primary if organisation.is_primary else None,
#                 "created_by": organisation.created_by if organisation.created_by else None,
#             }
#             return context

#         for user in User.objects.all():
#             if check_password(user_details.get('personal_number'), user.private_number):
#                 profile_updated = bool(user.first_name and user.last_name and user.email)
#                 if not user.organizations:
#                     organisation = Organization.objects.create(
#                         created_by=user.reference_id, is_primary=True
#                     )
#                     user.organizations.set([organisation])
#                     user.save()
#                 else:
#                     if user.role == "others":
#                         organisation = Organization.objects.filter(
#                             users__reference_id=user.reference_id, is_primary=True
#                         ).first()
#                     else:
#                         organisation = Organization.objects.filter(
#                             users__reference_id=user.reference_id
#                         ).first()
#                 access_token = jwt_manager.generate_token(user.reference_id)
#                 refresh_token = jwt_manager.generate_token(
#                     user.reference_id, token_type="refresh"
#                 )

#                 if user_details.get('personal_info') and not profile_updated:
#                     user.first_name = first_name
#                     user.last_name = last_name
#                     user.save()

#                 if user_details.get('is_onetime'):
#                     access_token = "onetime" + access_token
#                 context["user_details"] = {
#                     "access_token": access_token,
#                     "refresh_token": refresh_token,
#                     "profile_updated": profile_updated,
#                     "first_name": (user.first_name if user.first_name else None),
#                     "user_refernce_id": user.reference_id,
#                     "last_name": user.last_name if user.last_name else None,
#                     "email": fernet_decrypt(user.email) if user.email else None,
#                     "phone_number": (
#                         fernet_decrypt(user.phone_number) if user.phone_number else None
#                     ),
#                 }
#                 context["organisation_details"] = {
#                     "name": organisation.name if organisation.name else None,
#                     "number": organisation.number if organisation.number else None,
#                     "reference_id": (
#                         organisation.reference_id if organisation.reference_id else None
#                     ),
#                     "is_primary": organisation.is_primary if organisation.is_primary else None,
#                     "created_by": organisation.created_by if organisation.created_by else None,
#                 }
#                 return context

#         new_user = User.objects.create(
#             private_number=hash_password,
#             phone_number=phone_number if user_details.get('user_type') == "otp" else None,
#             authentication_method=user_details.get('user_type'),
#             username=phone_number,
#         )
#         organisation = Organization.objects.create(
#             created_by=new_user.reference_id, is_primary=True
#         )
#         new_user.organizations.set([organisation])
#         new_user.save()
#         # if new_user and user_details.get('user_type') == "otp":
#         #     new_user.username = phone_number
#         #     new_user.save()
#         access_token = jwt_manager.generate_token(new_user.reference_id)
#         refresh_token = jwt_manager.generate_token(
#             new_user.reference_id, token_type="refresh"
#         )
#         profile_updated = False
#         if user_details.get('personal_info') and not profile_updated:
#             profile_updated = True
#             new_user.first_name = first_name
#             new_user.last_name = last_name
#             new_user.save()

#         context["user_details"] = {
#             "access_token": access_token,
#             "refresh_token": refresh_token,
#             "profile_updated": profile_updated,
#             "user_refernce_id": new_user.reference_id,
#             "first_name": (new_user.first_name if new_user.first_name else None),
#             "last_name": (new_user.last_name if new_user.last_name else None),
#             "email": fernet_decrypt(new_user.email) if new_user.email else None,
#             "phone_number": (
#                 fernet_decrypt(new_user.phone_number) if new_user.phone_number else None
#             ),
#         }
#         context["organisation_details"] = {
#             "name": organisation.name if organisation.name else None,
#             "number": organisation.number if organisation.number else None,
#             "reference_id": (
#                 organisation.reference_id if organisation.reference_id else None
#             ),
#             "is_primary": organisation.is_primary if organisation.is_primary else None,
#             "created_by": organisation.created_by if organisation.created_by else None,
#         }
#         # return {
#         #     "access_token": access_token,
#         #     "refresh_token": refresh_token,
#         #     "profile_updated": profile_updated,
#         #     "first_name" : fernet_decrypt(new_user.first_name)
#         #     if new_user.first_name else None,
#         #     "last_name": fernet_decrypt(new_user.last_name)
#         #     if new_user.last_name else None,
#         #     "email": fernet_decrypt(new_user.email) if new_user.email else None,
#         #     "phone_number": fernet_decrypt(new_user.phone_number)
#         #     if new_user.phone_number else None,
#         # }
#         return context

#     except Exception as e:
#         logger.error(
#             "Error saving BankID user details",
#             extra={
#                 'error': str(e),
#                 'personal_number': user_details.get('personal_number')
#             },
#             exc_info=True
#         )
#         raise


# def get_bankid_message(hint_code, error_code=None):
#     try:
#         logger.debug(
#             "Getting BankID message",
#             extra={
#                 'hint_code': hint_code,
#                 'error_code': error_code
#             }
#         )
#         for message in BANKID_RESPONSES:
#             if message["status"] == error_code and hint_code in message.get("hintCode", []):
#                 logger.info(f"Found matching message: {message['message']}")
#                 return message["message"]
#         logger.warning(
#             f"No matching BankID message found for status: {error_code}, hint_code: {hint_code}"
#         )
#         return "Something went Wrong!!"
#     except Exception as e:
#         logger.error(
#             "Error getting BankID message",
#             extra={
#                 'hint_code': hint_code,
#                 'error_code': error_code,
#                 'error': str(e)
#             }
#         )
#         raise
