# import base64
# import hashlib
import logging
from typing import Dict, Any, Union

from bankid import BankIDClient
from bankid.exceptions import InvalidParametersError  # type: ignore
from django.conf import settings

# from documents.models.document_models import UserDocument

logger = logging.getLogger("app")


class CustomBankIDClient(BankIDClient):
    """
    Custom BankID client that extends the base BankIDClient to support returnUrl parameter
    for in-device flow authentication and signing.
    """

    def sign(
        self,
        end_user_ip: str,
        user_visible_data: str,
        requirement: Union[Dict[str, Any], None] = None,
        user_non_visible_data: Union[str, None] = None,
        user_visible_data_format: Union[str, None] = None,
        return_url: Union[str, None] = None,
    ):
        """
        Request a signing order with support for returnUrl parameter.

        This method extends the base sign method to support the returnUrl parameter
        for in-device flow authentication.

        :param end_user_ip: The user IP address as seen by RP. String. IPv4 and IPv6 is allowed.
        :param user_visible_data: Text to be displayed to the user.
        :param requirement: Requirements on how the sign order must be performed.
        :param user_non_visible_data: Data is not displayed to the user.
        :param user_visible_data_format: Format of user visible data.
        :param return_url: Return URL for in-device flow (optional).
        :return: The order response parsed to a dict.
        :raises BankIDError: raises a subclass of this error when error has been returned from server.
        """
        # Create the base payload using the parent class method
        data = self._create_payload(
            end_user_ip,
            requirement=requirement,
            user_visible_data=user_visible_data,
            user_non_visible_data=user_non_visible_data,
            user_visible_data_format=user_visible_data_format,
        )

        # Add returnUrl if provided
        if return_url:
            data["returnUrl"] = return_url
            logger.info(f"Added returnUrl to sign request: {return_url}")

        logger.info(f"Sign Data: {data}")
        response = self.client.post(self._sign_endpoint, json=data)

        logger.info(f"Sign response: {response}")
        logger.info(f"Sign Response data: {response.json()}")
        logger.info(f"Sign Response data as text: {response.text}")
        if response.status_code == 200:
            return response.json()  # type: ignore[no-any-return]
        else:
            from bankid.exceptions import get_json_error_class
            raise get_json_error_class(response)


# Create the custom client instance
client = CustomBankIDClient(
    certificates=(
        settings.BANKID_CERT_PATH,
        settings.BANKID_KEY_PATH,
    ),
    test_server=getattr(settings, "BANK_ID_TEST_MODE",
                        True),  # Set to False for production
)


def bankid_authenticate(user_ip):
    try:
        logger.info(f"Attempting to authenticate user with IP: {user_ip}")
        response = client.authenticate(end_user_ip=user_ip)
        logger.info(f"BankID authentication successful for IP: {user_ip}")
        return response
    except Exception as e:
        logger.error(f"Authentication failed for IP: {user_ip}, Error: {str(e)}")
        return False


def collect_status(order_ref):
    logger.info(f"Starting collect_status for order reference: {order_ref}")

    # while True:
    try:
        response = client.collect(order_ref)
        logger.info(f"Collect status response: {response}")
        # print("inside collect status",response)
        if response["status"] == "complete":
            logger.info(f"Authentication complete for order reference: {order_ref}")
            return response
        elif response["status"] == "failed":
            logger.error(
                f"""Authentication failed for order reference: {order_ref},
                  HintCode: {response.get('hintCode')}"""
            )
            return {"status": response["status"], "hintCode": response["hintCode"]}
        elif response["status"] == "pending":
            logger.info(
                f"""Authentication pending for order reference: {order_ref},
                  HintCode: {response.get('hintCode')}"""
            )
            return {"status": response["status"], "hintCode": response["hintCode"]}
        # time.sleep(2)
    except Exception:
        return False


def cancel_authentication(order_ref):
    try:
        logger.info(
            f"Attempting to cancel authentication for" f" order reference: {order_ref}"
        )
        client.cancel(order_ref)
        logger.info(
            f"Authentication canceled successfully " f"for order reference: {order_ref}"
        )
        return True
    except Exception as e:
        logger.error(
            f"Error canceling authentication for order"
            f" reference: {order_ref}, Error: {str(e)}"
        )
        return False


def generate_autostart_url(auto_start_token, device_type):
    """
    Generate the appropriate autostart URL based on device type.
    Note: return_url is handled by the BankID API directly, not in the autostart URL.

    Args:
        auto_start_token: The autostart token from BankID response
        device_type: 'mobile' or 'desktop'

    Returns:
        str: The autostart URL
    """
    if device_type == "mobile":
        # For mobile devices, use the web URL
        base_url = "https://app.bankid.com/"
        params = f"autostarttoken={auto_start_token}"
        return f"{base_url}?{params}"
    else:
        # For desktop devices, use the bankid:// protocol
        base_url = "bankid:///"
        params = f"autostarttoken={auto_start_token}"
        return f"{base_url}?{params}"


def sign_document(dict_data: Dict):
    try:
        end_user_ip = dict_data["end_user_ip"]
        user_visible_data = dict_data.get("user_visible_data")
        return_url = dict_data.get("return_url")
        flow_type = dict_data.get("flow_type", "qr")

        document_content = dict_data.get("document_content")
        # Log the start of the document signing process
        logger.info(
            f"Starting document sign for IP: {end_user_ip},"
            f" Document: {document_content}, Flow: {flow_type}"
        )

        # Prepare sign parameters
        sign_params = {
            "end_user_ip": end_user_ip,
            "user_visible_data": user_visible_data,
        }

        # Add returnUrl as direct parameter for in-device flow
        if flow_type == "in_device" and return_url:
            sign_params["return_url"] = return_url
            logger.info(f"Adding returnUrl for in-device flow: {return_url}")
        elif flow_type == "in_device" and not return_url:
            logger.warning("In-device flow selected but no return_url provided")

        # Making the API call
        response = client.sign(**sign_params)
        logger.info(f"Document signing response: {response}")

        return response

    except InvalidParametersError as e:
        logger.error(f"Invalid parameters error during sign: {str(e)}")
        raise

    except Exception as e:
        logger.error(f"Error in sign_document: {str(e)}")
        raise
