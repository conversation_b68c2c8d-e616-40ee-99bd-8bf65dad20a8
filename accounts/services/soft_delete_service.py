import logging
from django.db import transaction
from django.utils import timezone

from accounts.models.users import AccountHistory, User, SessionTrack
from accounts.utils.account_deletion_cache import clear_deletion_in_progress, set_deletion_in_progress
from documents.models.document_models import DirectoryDocument, OrganizationDocument, UserDocument, UserDocumentDirectory
from subscriptions.models.subscription_masters import PaymentTransaction, SubscriptionHistory, UserSubscription


logger = logging.getLogger("app")


class SoftDeleteError(Exception):
    """Raised when an error occurs during soft deletion."""
    pass


class UserNotFoundOrDeleted(Exception):
    """Raised when the user to be soft deleted does not exist or is already deleted."""
    pass


def create_account_history(user, deleted_by, org=None, subscription=None):
    """Create an AccountHistory record for the user being deleted."""
    AccountHistory.objects.create(
        user=user,
        email=user.email,
        private_email=user.private_email,
        phone_number=user.phone_number,
        role=user.role,
        deleted_at=timezone.now(),
        deleted_by=deleted_by or user,
        is_self_deleted=(deleted_by is None or deleted_by == user),
        organization=org,
        subscription=subscription,
        plan_name=getattr(subscription.plan, "name",
                          None) if subscription and subscription.plan else None,
        stripe_subscription_id=getattr(
            subscription, "stripe_subscription_id", None) if subscription else None,
    )


def soft_delete_user_and_related(email, deleted_by=None):
    """
    Soft delete a user and all related data based on their role.
    - For org_superadmin: soft delete org, all users under org, and all related data.
    - For manager: soft delete all members under manager and their related data.
    - For member/add-on user: only soft delete their own data.
    - If deleted_by is None or deleted_by == user, it's a self-deletion.
    """
    user = User.objects.filter(email=email, is_deleted=False).first()
    if not user:
        return  # Or raise error

    set_deletion_in_progress(email)
    try:
        with transaction.atomic():
            # Get user's org and active subscription for history
            org = user.organizations.filter(is_active=True).first()
            active_user_subscription = UserSubscription.objects.filter(
                user=user, status="active").order_by('-end_date').first()
             
            if org and org.total_active_user > 0:
                org.total_active_user -= 1
                org.save()

            # Create AccountHistory for this user
            create_account_history(user, deleted_by, org=org,
                                   subscription=active_user_subscription)

            # Role-based logic
            if user.role == "org_superadmin":
                # a. Soft-delete org(s)
                orgs = user.organizations.all()
                for org in orgs:
                    org.is_deleted = True
                    org.is_active = False
                    org.id_primary = False
                    org.deactivated_at = timezone.now()
                    org.save()

                # b. Get all users in org(s)
                org_users = User.objects.filter(
                    organizations__in=orgs, is_deleted=False).distinct()
                for org_user in org_users:
                    if org_user != user:
                        soft_delete_user_and_related(org_user.email, deleted_by=user)

                # Soft-delete org-level documents, directories, etc.
                OrganizationDocument.objects.filter(organization__in=orgs, is_deleted=False).update(
                    is_deleted=True, deleted_at=timezone.now())
                UserDocumentDirectory.objects.filter(organization__in=user.organizations.all(
                ), is_deleted=False).update(is_deleted=True, deleted_at=timezone.now())
                DirectoryDocument.objects.filter(directory__organization__in=user.organizations.all(
                ), is_deleted=False).update(is_deleted=True, deleted_at=timezone.now())
                UserDocument.objects.filter(owner=user, is_deleted=False).update(
                    is_deleted=True, deleted_at=timezone.now())

            UserDocumentDirectory.objects.filter(created_by=user, is_deleted=False).update(
                is_deleted=True, deleted_at=timezone.now())
            # Optionally, also:
            # UserDocumentDirectory.objects.filter(organization__in=user.organizations.all(), is_deleted=False).update(is_deleted=True, deleted_at=timezone.now())

            DirectoryDocument.objects.filter(added_by=user, is_deleted=False).update(
                is_deleted=True, deleted_at=timezone.now())
            # Remove Stripe customer if exists
            try:
                if hasattr(user, "stripe_customer_id") and user.stripe_customer_id:
                    import stripe
                    from django.conf import settings
                    stripe.api_key = settings.STRIPE_SECRET_KEY
                    try:
                        stripe.Customer.delete(user.stripe_customer_id)
                    except stripe.error.InvalidRequestError:
                        # Customer may not exist in Stripe, ignore
                        pass
                    user.stripe_customer_id = None
                    user.save(update_fields=["stripe_customer_id"])
            except Exception as e:
                logger.error(
                    f"Error removing Stripe customer for user {user.email}: {str(e)}", exc_info=True)

            # Soft-delete user subscriptions and related history
            user_subscriptions = UserSubscription.objects.filter(
                user=user, is_deleted=False, status="active")

            # Soft-delete subscription history
            SubscriptionHistory.objects.filter(user_subscription__in=user_subscriptions, is_deleted=False).update(
                is_deleted=True, deleted_at=timezone.now())

            # Soft-delete payment transactions
            PaymentTransaction.objects.filter(user_subscription__in=user_subscriptions, is_deleted=False).update(
                is_deleted=True, deleted_at=timezone.now())

            # Soft-delete user subscriptions
            user_subscriptions.update(
                is_deleted=True,
                cancelled_date=timezone.now(),
                status="cancelled",
                deleted_at=timezone.now(),
                cancelled_by=deleted_by if deleted_by else None
            )
            # Clean up user session tracks. These are hard-deleted as they are transient data.
            SessionTrack.objects.filter(user=user).delete()

            # 5. Soft-delete the user itself
            user.organizations.clear()
            user.is_deleted = True
            user.deleted_at = timezone.now()
            user.is_active = False
            user.self_deleted = (deleted_by is None or deleted_by == user)
            user.save()

            removal_date = timezone.now().strftime("%Y-%m-%d")
            
            org_owner = org.get_organization_owner()
            from accounts.tasks import send_user_removed_from_organization_email_task
            send_user_removed_from_organization_email_task.delay(
                user.get_decrypted_email(),
                user.first_name,
                org_owner.get_full_name() if org_owner else "--",
                org.get_decrypted_name(),
                deleted_by.get_full_name() if deleted_by else "--",
                removal_date.isoformat() if hasattr(removal_date, "isoformat") else str(removal_date),
                user.role
            )
    except Exception as e:
        logger.error(f"Error during soft delete for {email}: {str(e)}", exc_info=True)
        clear_deletion_in_progress(email)
        raise SoftDeleteError(f"Soft delete failed for {email}: {str(e)}")
    finally:
        clear_deletion_in_progress(email)
