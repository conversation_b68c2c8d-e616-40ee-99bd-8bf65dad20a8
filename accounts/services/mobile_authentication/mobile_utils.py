import random
import redis

from esign.settings import REDIS_URL


# Initialize the Redis client
redis_client = redis.from_url(REDIS_URL, decode_responses=True)
# from django.contrib.auth.hashers import check_password, make_password

# from accounts.models.users import User

# from accounts.services.mobile_authentication.mobile_authentication import (
#     JWTTokenManager)


def generate_otp():
    return random.randint(100000, 999999)

def blacklist_token(token, expiry_time):
    """
    Blacklist a token by storing it in Redis with an expiration time.
    """
    redis_client.set(f"blacklist:{token}", "true", ex=expiry_time)

def is_token_blacklisted(token):
    """
    Check if a token is blacklisted by querying Redis.
    """
    return redis_client.exists(f"blacklist:{token}") == 1
# def mobile_save_user_details(personal_number):
#     # jwt_manager = JWTTokenManager()

#     hash_password = make_password(personal_number)
#     existing_user = User.objects.filter(private_number=hash_password).first()

#     if existing_user:
#         return existing_user
#         # access_token = jwt_manager.generate_token(existing_user.reference_id)
#         # refresh_token = jwt_manager.generate_token(
#         #     existing_user.id, token_type="refresh"
#         # )

#         # return {
#         #     "access_token": access_token,
#         #     "refresh_token": refresh_token,
#         # }

#     for user in User.objects.all():
#         if check_password(personal_number, user.private_number):
#             print("private number exist.")
#             return user
#             # access_token = jwt_manager.generate_token(user.reference_id)
#             # refresh_token = jwt_manager.generate_token(
#             #     user.reference_id, token_type="refresh"
#             # )

#             # return {
#             #     "access_token": access_token,
#             #     "refresh_token": refresh_token,
#             # }

#     new_user = User.objects.create(private_number=hash_password)
#     print("Created new user.")
#     return new_user

#     # Generate JWT tokens for the new user
#     # access_token = jwt_manager.generate_token(new_user.reference_id)
#     # refresh_token = jwt_manager.generate_token(
#     #     new_user.reference_id, token_type="refresh"
#     # )

#     # return {
#     #     "access_token": access_token,
#     #     "refresh_token": refresh_token,
#     # }
