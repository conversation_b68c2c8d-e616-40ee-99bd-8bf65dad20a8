import logging
import os

from django.conf import settings
from django.utils.translation import gettext as _
from twilio.base.exceptions import TwilioRestException  # type: ignore
from twilio.rest import Client  # type: ignore

logger = logging.getLogger("app")


def send_otp_via_sms(receiver_name=None, phone_number=None, otp=None):
    client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
    logger.info(f"Attempting to send OTP to {phone_number}.")
    try:
        message = client.messages.create(
            from_="Skrivly",
            body=f"Hi, your One-Time Password (OTP) is {otp}. Please use this to complete your verification. Thank you!",
            to=phone_number,
        )
        logger.info(
            f"OTP sent successfully to {phone_number}. Message SID: {message.sid}"
        )
        return {
            "status": "success",
            "message_sid": message.sid,
            "message": _("OTP sent successfully!"),
        }

    except TwilioRestException as e:
        logger.error(f"Failed to send OTP to {phone_number}. Error: {str(e)}")
        return {
            "status": "failed",
            "message": _("Failed to send OTP"),
            "error_details": str(e),
        }
    

def send_info_via_sms(receiver_name=None, sender_name=None, email=None, phone_number=None, message=None):
    client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
    logger.info(f"Attempting to send OTP to {phone_number}.")
    try:
        message = client.messages.create(
            from_="Skrivly",
            body=f"Hi, {receiver_name} we have sent a document on your {email} address, Kindly check your inbox, and if you don't find it there, please also check your spam folder. From {sender_name}",
            to=phone_number,
        )
        logger.info(
            f"OTP sent successfully to {phone_number}. Message SID: {message.sid}"
        )
        return {
            "status": "success",
            "message_sid": message.sid,
            "message": _("OTP sent successfully!"),
        }

    except TwilioRestException as e:
        logger.error(f"Failed to send OTP to {phone_number}. Error: {str(e)}")
        return {
            "status": "failed",
            "message": _("Failed to send OTP"),
            "error_details": str(e),
        }




def send_notfication_via_sms(phone_number, message):
    client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)

    logger.info(f"Attempting to send OTP to {phone_number}.")
    try:
        message = client.messages.create(
            from_="Skrivly",
            body=f"{message}",
            to=phone_number,
        )
        logger.info(
            f"OTP sent successfully to {phone_number}. Message SID: {message.sid}"
        )
        return {
            "status": "success",
            "message_sid": message.sid,
            "message": _("OTP sent successfully!"),
        }

    except TwilioRestException as e:
        logger.error(f"Failed to send OTP to {phone_number}. Error: {str(e)}")
        return {
            "status": "failed",
            "message": _("Failed to send OTP"),
            "error_details": str(e),
        }


def send_verfiy_link_via_sms(
    receiver_name=None,
    phone=None,
    reference_signer_id=None,
    org_doc_reference_id=None,
    user_id=None,
    document=None,
):
    client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
    dynamic_url = (
        f"{os.getenv('FE_BASE_URL')}/"
        + f"sign_document/{reference_signer_id}/{user_id}/{document}/{org_doc_reference_id}"
    )
    logger.info(f"Attempting to send preview link to {phone}.")
    try:
        message = client.messages.create(
            from_="Skrivly",
            body=f"Hi,you can view your document by clicking the following link: {dynamic_url}. Please review it at your earliest convenience. Thank you!",
            to=phone,
        )
        logger.info(f"OTP sent successfully to {phone}. Message SID: {message.sid}")

        return {
            "status": "success",
            "message_sid": message.sid,
            "message": _("OTP sent successfully!"),
        }

    except TwilioRestException as e:
        logger.error(f"Failed to send OTP to {phone}. Error: {str(e)}")
        return {
            "status": "failed",
            "message": _("Failed to send OTP"),
            "error_details": str(e),
        }


def send_reminder_link_via_sms(
    receiver_name=None,
    phone=None,
    reference_signer_id=None,
    org_doc_reference_id=None,
    user_id=None,
    document=None,
):
    client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
    dynamic_url = (
        f"{os.getenv('FE_BASE_URL')}/"
        + f"sign_document/{reference_signer_id}/{user_id}/{document}/{org_doc_reference_id}"
    )
    logger.info(f"Attempting to send preview link to {phone}.")
    try:
        message = client.messages.create(
            from_="Skrivly",
            body=f"Hi {receiver_name}, This is a Reminder, please sign your document.Tap here to sign: {dynamic_url}",
            to=phone,
        )
        logger.info(f"OTP sent successfully to {phone}. Message SID: {message.sid}")

        return {
            "status": "success",
            "message_sid": message.sid,
            "message": _("OTP sent successfully!"),
        }

    except TwilioRestException as e:
        logger.error(f"Failed to send OTP to {phone}. Error: {str(e)}")
        return {
            "status": "failed",
            "message": _("Failed to send OTP"),
            "error_details": str(e),
        }
    

def send_document_completed_via_sms(receiver_name=None, phone_number=None, reference_signer_id=None, user_id=None, document=None, org_doc_reference_id=None, status=None):
    # if isinstance(document, dict):
    document_reference_id = document.get("reference_id")
    if status == "approved_owner":  
        dynamic_url = (
        f"{os.getenv('FE_BASE_URL')}/"
        + f"sign_document/{reference_signer_id}/{user_id}/{document_reference_id}/{org_doc_reference_id}?is_owner=true"
        )      
    else:
        dynamic_url = (
        f"{os.getenv('FE_BASE_URL')}/"
        + f"sign_document/{reference_signer_id}/{user_id}/{document_reference_id}/{org_doc_reference_id}?is_owner=false"
        )

    client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)

    logger.info(f"Attempting to send preview link to {phone_number}.")
    try:
        # You can view the overview here: {dynamic_url}. Thank you for using our service!
        message = client.messages.create(
            from_="Skrivly",
            body=f"Hi {receiver_name}, your document has been successfully signed and completed. ",
            to=phone_number,
        )
        # logger.info(f"OTP sent successfully to {phone}. Message SID: {message.sid}")

        return {
            "status": "success",
            "message_sid": message.sid,
            "message": _("Document signed successfully!"),
        }


    except TwilioRestException as e:
        return {
            "status": "failed",
            "message": _("Failed to send Document"),
            "error_details": str(e),
        }
