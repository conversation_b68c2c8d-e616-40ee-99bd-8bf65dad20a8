import logging
from datetime import timed<PERSON><PERSON>
from django.utils import timezone
from django.db.models import Q
from cryptography.fernet import Fernet
from django.conf import settings

from accounts.models.users import UserContactHistory, User
from accounts.services.jwt_authentication.authentication import fernet_decrypt, fernet_encrypt, generate_hmac_token


logger = logging.getLogger('app')

cipher_suite = Fernet(settings.ENCRYPTION_KEY)


class ContactUpdateService:
    def __init__(self, user):
        if not user or not isinstance(user, User):
            raise ValueError("A valid user object is required")
        self.user = user

    def can_update_contact(self, contact_type):
        """
        Check if user can update their contact information
        Returns True if no update in last 30 days, False otherwise
        """
        try:
            thirty_days_ago = timezone.now() - timedelta(days=30)

            last_update = UserContactHistory.objects.filter(
                user=self.user,
                contact_type=contact_type,
                changed_at__gte=thirty_days_ago
            ).first()

            # Return True if there's no update in last 30 days (last_update is None)
            # Return False if there is an update (last_update exists)
            return last_update is None

        except Exception as e:
            logger.error(
                f"Error checking contact update eligibility - User: {self.user.id} - Error: {str(e)}")
            # Log the full exception details for debugging
            logger.exception("Full exception details:")
            raise

    def update_contact(self, contact_type, new_value, changed_by=None):
        """
        Update user's contact information and track the change
        """
        try:
            if not self.can_update_contact(contact_type):
                raise ValueError(
                    f"You can only update your {contact_type} once every 30 days")

            # Get current value based on contact type
            if contact_type == 'email':
                current_value = self.user.get_decrypted_email()
                # Update user's email
                # self.user.email = new_value
                self.user.email = cipher_suite.encrypt(str(new_value).encode()).decode()
                self.user.private_email = generate_hmac_token(new_value)
            elif contact_type == 'phone':
                current_value = self.user.get_decrypted_phone_number()
                # Update user's phone
                self.user.phone_number = cipher_suite.encrypt(
                    str(new_value).encode()).decode()
                self.user.private_phone = generate_hmac_token(new_value)
            else:
                raise ValueError(f"Invalid contact type: {contact_type}")

            # Deactivate all previous active records for this contact type
            UserContactHistory.objects.filter(
                user=self.user,
                contact_type=contact_type,
                is_active=True
            ).update(is_active=False)

            # Create new history record
            UserContactHistory.objects.create(
                user=self.user,
                contact_type=contact_type,
                old_value=current_value,
                new_value=new_value,
                changed_by=changed_by,
                is_active=True
            )

            # Save user changes
            self.user.save()

            return {
                'old_value': current_value,
                'new_value': new_value,
                'changed_at': timezone.now(),
                'is_active': True
            }
        except Exception as e:
            logger.error(
                f"Error updating contact - User: {self.user.id} - Error: {str(e)}")
            raise

    def get_contact_history(self, contact_type=None):
        """
        Get user's contact update history
        """
        query = Q(user=self.user)
        if contact_type:
            query &= Q(contact_type=contact_type)

        return UserContactHistory.objects.filter(query).order_by('-changed_at')

    def get_active_contact(self, contact_type):
        """
        Get the currently active contact information for the specified type
        """
        try:
            active_record = UserContactHistory.objects.get(
                user=self.user,
                contact_type=contact_type,
                is_active=True
            )
            return active_record.new_value
        except UserContactHistory.DoesNotExist:
            return None
