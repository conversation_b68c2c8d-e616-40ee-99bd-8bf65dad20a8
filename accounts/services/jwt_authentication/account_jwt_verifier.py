import logging

import jwt  # type: ignore
from django.conf import settings
from django.contrib.auth import get_user_model
from django.http import HttpRequest
from django.utils.translation import gettext as _
from rest_framework import authentication, exceptions

from accounts.services.jwt_authentication.authentication import fernet_decrypt

User = get_user_model()

logger = logging.getLogger(__name__)


class AccountJWTAccessTokenAuthentication(authentication.BaseAuthentication):
    def __init__(self, *args, **kwargs):
        self.secret_key = settings.SECRET_KEY  # Secret key to decode JWT
        self.algorithm = "HS256"  # Algorithm used to encode JWT
        logger.info("AccountJWTAccessTokenAuthentication initialized.")

    def authenticate(self, request: HttpRequest):
        # authentication_token = request.query_params.get("token")
        authentication_token = request.META.get("HTTP_AUTHORIZATION")
        if not authentication_token:
            logger.warning("Authorization token missing in request.")
            raise exceptions.AuthenticationFailed(_("Authorization token missing"))

        if not authentication_token.startswith("Bearer "):
            logger.warning("Invalid authorization header format.")
            raise exceptions.AuthenticationFailed(_("Invalid authorization header"))

        # Get the actual token
        token = authentication_token.split(" ")[1]
        logger.info(f"Received token: {token[:10]}...")

        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            logger.info(f"Decoded JWT payload: {payload}")
            reference_id = fernet_decrypt(payload["reference_id"].encode())
            logger.info(f"Decrypted reference ID: {reference_id}")

            # Retrieve the user by reference ID
            try:
                user = User.objects.get(reference_id=reference_id)
                logger.info(f"User found: {user.username}")
            except User.DoesNotExist:
                logger.error(f"User with reference ID {reference_id} not found.")
                raise exceptions.AuthenticationFailed(_("User not found"))

            logger.info(f"Authentication successful for user: {user.username}")
            # Return the user and token for successful authentication
            return (user, token)

        except jwt.ExpiredSignatureError:
            logger.error("JWT token has expired.")
            raise exceptions.AuthenticationFailed(_("Token has expired"))
        except jwt.InvalidTokenError:
            logger.error("JWT token is invalid.")
            raise exceptions.AuthenticationFailed(_("Invalid token"))
