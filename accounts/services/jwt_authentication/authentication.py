# import secrets
import base64
import hashlib
import hmac
import logging
from datetime import datetime, timedelta, timezone

import jwt  # type : ignore
from cryptography.fernet import Fernet
from django.conf import settings
from django.utils.translation import gettext as _
from rest_framework.exceptions import ValidationError

from esign.utils.custom_response import api_response

logger = logging.getLogger("app")

import os

from dotenv import load_dotenv

load_dotenv()

JWT_EXPIRY_SECONDS = int(os.getenv("JWT_EXPIRY_SECONDS", 3600000))

# Encryption setup
cipher_suite = Fernet(settings.ENCRYPTION_KEY)
SECRET_KEY = settings.HMAC_SECRET_KEY


def fernet_encrypt(data):
    # here data sendi string to byte forrmat
    logger.info(f"Encrypting data: {data}")
    return cipher_suite.encrypt(data.encode("utf-8"))


def fernet_decrypt(ciphertext):
    # here data from byte to string then decrypting
    # logger.info(f"Decrypting data: {ciphertext[:10]}...")
    
    try:
        return cipher_suite.decrypt(ciphertext).decode("utf-8")
    except jwt.ExpiredSignatureError:
            logger.error("JWT token has expired.")
            return api_response(message=_("Refresh token expired."))
    except jwt.InvalidTokenError:
            print("Invalid error ")
            logger.error("Invalid JWT token.")
            return api_response(action="validation_error")

    except Exception as e:
        logger.error(f"Decryption error: {str(e)}")
        return None


class JWTTokenManager:
    """
    A class to handle JWT token generation and validation.
    """

    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = "HS256"
        logger.info("JWTTokenManager initialized.")

    def generate_token(self, user_id, token_type="access", exp_minutes=JWT_EXPIRY_SECONDS):
        """
        Generate a JWT token.

        :param user_id: ID of the user for whom the token is generated.
        :param token_type: Type of the token ('access' or 'refresh').
        :param exp_minutes: Expiration time in minutes for the access token.
        :return: Encoded JWT token.
        """
        try:
           
           
            
            if token_type == "access":
                expiration = datetime.now(tz=timezone.utc) + timedelta(minutes=exp_minutes)
                logger.info(f"Access token will expire in {exp_minutes} minutes.")
            elif token_type == "refresh":
                expiration = datetime.now(tz=timezone.utc) + timedelta(days=7)
                logger.info("Refresh token will expire in 7 days.")
            else:
                logger.error(f"Invalid token type: {token_type}")
                raise ValueError("Invalid token type")

            payload = {
                "reference_id": fernet_encrypt(user_id).decode(),
                "exp": expiration,
                "type": token_type,
            }
            logger.info(f"Generated {token_type} token for user {user_id}.")

            return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        except Exception as e:
            logger.error(f"Token generation failed: {str(e)}")
            raise ValidationError(f"Error generating token: {str(e)}")

    def decode_token(self, token):
        """
        Decode a JWT token to retrieve the payload.

        :param token: JWT token to decode.
        :return: Decoded payload.
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            reference_id = fernet_decrypt(payload["reference_id"].encode())
            logger.info(
                f"Decoded token payload: {payload}, reference_id: {reference_id}"
            )
            return reference_id

        except jwt.ExpiredSignatureError:
            logger.error("JWT token has expired.")
            return api_response(message=_("Refresh token expired."))
        except jwt.InvalidTokenError:
            logger.error("Invalid JWT token.")
            return api_response(action="validation_error")
        except Exception as e:
            logger.error(f"Token decoding failed: {str(e)}")
            return api_response(
                action="invalid_token",
                message=_("Error decoding token"),

            )


"""
# Example usage
secret_key = "your_secret_key"
user_id = "user123"

# Generate the token
token = generate_hmac_token(user_id, secret_key)
print("Generated Token:", token)

# Verify the token
is_valid = verify_hmac_token(token, user_id, secret_key)
print("Is token valid:", is_valid)

# Generate a random 32-character secret key
# def generate_secret_key():
#     return secrets.token_hex(32)  # 32-character hex string (128 bits)

"""


def generate_hmac_token(user_id, secret_key=SECRET_KEY):
    logger.info(f"Generating HMAC token for user_id: {user_id}")
    signature = hmac.new(secret_key.encode(), user_id.encode(), hashlib.sha256).digest()
    token = base64.urlsafe_b64encode(signature).decode()
    logger.info(f"Generated HMAC token: {token[:10]}...")
    return token


def verify_hmac_token(token, user_id, secret_key=SECRET_KEY):
    try:
        logger.info(f"Verifying HMAC token for user_id: {user_id}")
        # Recalculate the signature using the provided user_id and SECRET_KEY
        recalculated_signature = hmac.new(
            secret_key.encode(), user_id.encode(), hashlib.sha256
        ).digest()

        # Encode the recalculated signature in base64 (to match the token format)
        recalculated_token = base64.urlsafe_b64encode(recalculated_signature).decode()
        if hmac.compare_digest(token, recalculated_token):
            print("Token is valid")
            logger.info("HMAC token is valid.")
            return True
        else:
            print("Invalid token")
            logger.warning("HMAC token is invalid.")
            return False

    except Exception as e:
        logger.error(f"Token verification failed: {e}")
        print(f"Token verification failed: {e}")
        return False
