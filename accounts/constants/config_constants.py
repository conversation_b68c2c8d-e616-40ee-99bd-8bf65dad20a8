import logging
from django.conf import settings

logger = logging.getLogger('app')

try:
    WS_BASE_URL = settings.WS_BASE_URL
    QR_WEBSOCKET_URL = f"{WS_BASE_URL}/qr"
    
    logger.info(
        "BankID URLs configured",
        extra={
            'ws_base_url': WS_BASE_URL,
            'qr_websocket_url': QR_WEBSOCKET_URL
        }
    )
except AttributeError as e:
    logger.error(
        "Missing required BankID URL configuration",
        extra={'error': str(e)},
        exc_info=True
    )
    raise
