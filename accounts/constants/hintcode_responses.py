BANKID_RESPONSES = [
    {
        "short_name": "RFA1",
        "status": "pending",
        "hintCode": ["outstandingTransaction", "noClient"],
        "message": "Start your BankID app.",
    },
    {
        "short_name": "RFA2",
        "status": "failed",
        "hintCode": [],
        "message": "The BankID app is not installed. Please contact your bank.",
        "sweden_message": "BankID-appen är inte installerad. Kontakta din bank.",
    },
    {
        "short_name": "RFA3",
        "status": "failed",
        "hintCode": [],
        "errorCode": "cancelled",
        "message": "Action cancelled. Please try again.",
        "sweden_message": "Åtgärden avbröts. Försök igen.",
    },
    {
        "short_name": "RFA4",
        "status": "failed",
        "hintCode": [],
        "errorCode": "alreadyInProgress",
        "message": (
            "An identification or signing for this personal number is already started. "
            "Please try again."
        ),
        "sweden_message": (
            "En identifiering eller underskrift för detta personnummer har redan "
            "påbörjats. Försök igen."
        ),
    },
    {
        "short_name": "RFA5",
        "status": "failed",
        "hintCode": [],
        "errorCode": ["requestTimeout", "maintenance", "internalError"],
        "message": "Internal error. Please try again.",
        "sweden_message": "Internt fel. Försök igen.",
    },
    {
        "short_name": "RFA6",
        "status": "failed",
        "hintCode": ["userCancel"],
        "message": "Action cancelled.",
        "sweden_message": "Åtgärden avbröts.",
    },
    {
        "short_name": "RFA8",
        "status": "failed",
        "hintCode": ["expiredTransaction"],
        "message": (
            "The BankID app is not responding. Please check that it’s started and that "
            "you have internet access. If you don’t have a valid BankID you can get "
            "one from your bank. Try again."
        ),
        "sweden_message": (
            "BankID-appen svarar inte. Kontrollera att den är startad och att du har "
            "internetåtkomst. Om du inte har ett giltigt BankID, skaffa ett från din "
            "bank. Försök igen."
        ),
    },
    {
        "short_name": "RFA9",
        "status": "pending",
        "hintCode": ["userSign"],
        "message": "Enter your security code in BankID app and select Identify/Sign.",
        "sweden_message": (
            "Ange din säkerhetskod i BankID-appen och välj Identifiera/Signera."
        ),
    },
    {
        "short_name": "RFA13",
        "status": "pending",
        "hintCode": ["outstandingTransaction"],
        "message": "Trying to start your BankID app.",
        "sweden_message": "Försöker starta din BankID-app.",
    },
    {
        "short_name": "RFA14 (A)",
        "status": "pending",
        "hintCode": ["started"],
        "message": (
            "Searching for BankID, it may take a little while … If a few seconds have "
            "passed and still no BankID has been found, you probably don’t have a "
            "BankID which can be used for this identification/signing on this "
            "computer. If you have a BankID card, please insert it into your card "
            "reader. If you don’t have a BankID you can get one from your bank. If you "
            "have a BankID on another device you can start the BankID app on that "
            "device."
        ),
        "sweden_message": (
            "Söker efter BankID, det kan ta en liten stund … Om några sekunder har gått"
            " och inget BankID har hittats, har du förmodligen inget BankID som kan "
            "användas för denna identifiering/underskrift på denna dator. Om du har ett"
            " BankID-kort, sätt in det i kortläsaren. Om du inte har ett BankID kan du "
            "skaffa ett från din bank. Om du har ett BankID på en annan enhet kan du "
            "starta BankID-appen på den enheten."
        ),
    },
    {
        "short_name": "RFA14 (B)",
        "status": "pending",
        "hintCode": ["started"],
        "message": (
            "Searching for BankID, it may take a little while … If a few seconds have "
            "passed and still no BankID has been found, you probably don’t have a "
            "BankID which can be used for this identification/signing on this device. "
            "If you don’t have a BankID you can get one from your bank. If you have a "
            "BankID on another device you can start the BankID app on that device."
        ),
        "sweden_message": (
            "Söker efter BankID, det kan ta en liten stund … Om några sekunder har gått"
            " och inget BankID har hittats, har du förmodligen inget BankID som kan "
            "användas för denna identifiering/underskrift på denna enhet. Om du inte "
            "har ett BankID kan du skaffa ett från din bank. Om du har ett BankID på en"
            " annan enhet kan du starta BankID-appen på den enheten."
        ),
    },
    {
        "short_name": "RFA15 (A)",
        "status": "pending",
        "hintCode": ["started"],
        "message": (
            "Searching for BankID:s, it may take a little while … If a few seconds have"
            " passed and still no BankID has been found, you probably don’t have a "
            "BankID which can be used for this identification/signing on this computer."
            " If you have a BankID card, please insert it into your card reader. If you"
            "don’t have a BankID you can get one from your bank."
        ),
        "sweden_message": (
            "Söker efter BankID:s, det kan ta en liten stund … Om några sekunder har "
            "gått och inget BankID har hittats, har du förmodligen inget BankID som kan"
            " användas för denna identifiering/underskrift på denna dator. Om du har"
            " ett BankID-kort, sätt in det i kortläsaren. Om du inte har ett BankID "
            "kan du skaffa ett från din bank."
        ),
    },
    {
        "short_name": "RFA15 (B)",
        "status": "pending",
        "hintCode": ["started"],
        "message": (
            "Searching for BankID, it may take a little while … If a few seconds have "
            "passed and still no BankID has been found, you probably don’t have a "
            "BankID which can be used for this identification/signing on this device. "
            "If you don’t have a BankID you can get one from your bank."
        ),
        "sweden_message": (
            "Söker efter BankID, det kan ta en liten stund … Om några sekunder har gått"
            " och inget BankID har hittats, har du förmodligen inget BankID som kan "
            "användas för denna identifiering/underskrift på denna enhet. Om du inte "
            "har ett BankID kan du skaffa ett från din bank."
        ),
    },
    {
        "short_name": "RFA16",
        "status": "failed",
        "hintCode": ["certificateErr"],
        "message": (
            "The BankID you are trying to use is blocked or too old. Please use another"
            " BankID or get a new one from your bank."
        ),
        "sweden_message": (
            "BankID:t du försöker använda är blockerat eller för gammalt. Använd ett "
            "annat BankID eller skaffa ett nytt från din bank."
        ),
    },
    {
        "short_name": "RFA17 (A)",
        "status": "failed",
        "hintCode": ["startFailed"],
        "message": (
            "The BankID app couldn’t be found on your computer or mobile device. "
            "Please install it and get a BankID from your bank. Install the app from "
            "your app store or https://install.bankid.com."
        ),
    },
    {
        "short_name": "RPA17 (B)",
        "status": "failed",
        "hintCode": ["startFailed"],
        "message": (
            "Failed to scan the QR code. Start the BankID app and scan the QR code. If "
            "you don’t have a valid BankID, get one from your bank."
        ),
        "sweden_message": (
            "Misslyckades med att skanna QR-koden. Starta BankID-appen och skanna "
            "QR-koden. Om du inte har ett giltigt BankID, skaffa ett från din bank."
        ),
    },
    {
        "short_name": "RFA18",
        "status": "pending",
        "hintCode": [],
        "message": "Start the BankID app.",
        "sweden_message": "Starta BankID-appen.",
    },
    {
        "short_name": "RFA19",
        "status": "pending",
        "hintCode": ["noQr"],
        "message": (
            "The BankID app is not started. Start your BankID app or scan the QR code."
        ),
        "sweden_message": (
            "BankID-appen är inte startad. Starta din BankID-app eller skanna QR-koden."
        ),
    },
    {
        "short_name": "RFA20",
        "status": "pending",
        "hintCode": [],
        "message": "Do you want to use BankID on this device or another device?",
        "sweden_message": "Vill du använda BankID på denna enhet eller en annan enhet?",
    },
    {
        "short_name": "RFA21 (A)",
        "status": "pending",
        "hintCode": ["okUserSign"],
        "message": "Enter your security code in the BankID app.",
        "sweden_message": "Ange din säkerhetskod i BankID-appen.",
    },
    {
        "short_name": "RFA21 (B)",
        "status": "failed",
        "hintCode": [],
        "errorCode": "expiredTransaction",
        "message": (
            "The BankID app didn’t respond in time. Please start the app and try again."
        ),
        "sweden_message": (
            "BankID-appen svarade inte i tid. Starta appen och försök igen."
        ),
    },
    {
        "short_name": "RFA22",
        "status": "failed",
        "hintCode": [],
        "errorCode": "certErr",
        "message": (
            "The certificate used for BankID is not valid. Please contact your bank."
        ),
        "sweden_message": (
            "Certifikatet som används för BankID är inte giltigt. Kontakta din bank."
        ),
    },
    {
        "short_name": "RFA23",
        "status": "failed",
        "hintCode": [],
        "errorCode": "internalError",
        "message": (
            "There was an internal error. Please try again later or contact support."
        ),
        "sweden_message": (
            "Ett internt fel uppstod. Försök igen senare eller kontakta support."
        ),
    },
]
