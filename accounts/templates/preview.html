{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Preview</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            box-sizing: border-box;
        }

        .container {
            width: 85%; /* 85% width for the document preview */
            height: 85%; /* 85% height for the document preview */
            display: flex;
            position: relative; /* Make container relative for button positioning */
        }

        .document-preview {
            width: 100%; /* Full width for the document preview */
            height: 100%; /* Full height for the document preview */
            display: flex;
            justify-content: center;
            align-items: center;
        }

        iframe {
            width: 100%;
            height: 100%;
            border: none;
            overflow: auto; /* Makes iframe scrollable */
        }

        .button-container {
            display: flex;
            flex-direction: column; /* Stack buttons vertically */
            justify-content: flex-start; /* Align to the start */
            align-items: flex-start; /* Align buttons to the left */
            margin-right: 20px; /* Space between buttons and document preview */
            width: 150px; /* Fixed width for the button container */
        }

        button {
            padding: 10px 20px;
            font-size: 16px;
            margin-bottom: 10px; /* Space between buttons */
            cursor: pointer;
            border: none;
            border-radius: 5px;
            transition: background-color 0.3s;
            width: 100%; /* Make buttons full width */
        }

        .agree {
            background-color: #4CAF50; /* Green */
            color: white;
        }

        .agree:hover {
            background-color: #45a049; /* Darker green on hover */
        }

        .disagree {
            background-color: #f44336; /* Red */
            color: white;
        }

        .disagree:hover {
            background-color: #e53935; /* Darker red on hover */
        }

        .message {
            margin-top: 20px; /* Space above message */
            text-align: center; /* Center the message text */
            width: 100%; /* Make message full width */
        }

        @media (max-width: 768px) {
            body {
                height: auto; /* Allow height to adjust in smaller screens */
                padding: 20px; /* Add padding for smaller screens */
            }

            .container {
                flex-direction: column; /* Stack elements vertically on smaller screens */
                height: auto; /* Allow height to adjust in smaller screens */
                width: 100%; /* Full width for smaller screens */
            }

            .button-container {
                width: 100%; /* Full width for button container on small screens */
                margin: 0; /* Remove margin on small screens */
            }

            iframe {
                height: 60vh; /* Responsive height for iframe */
            }
        }
    </style>
</head>
<body>
    <h1>Document Preview</h1>

    <div class="container">
        <div class="button-container">
            {% if status == "pending" %}
                <button type="button" class="agree" onclick="handleAction('agree')">Agree</button>
            {% endif %}
            <!-- <button type="button" class="disagree" onclick="handleAction('disagree')">Disagree</button> -->
        </div>

        <div class="document-preview">
            <!-- Display the document file -->
            <iframe src="data:application/pdf;base64,{{ document_preview }}"  scrolling="yes"></iframe>
        </div>
    </div>

    {% if message %}
        <div class="message">{{ message }}</div>
    {% endif %}

    <script>
        function getCSRFToken() {
            let csrfToken = null;
            const cookies = document.cookie.split(';');

            for (let cookie of cookies) {
                const trimmedCookie = cookie.trim();
                if (trimmedCookie.startsWith('csrftoken=')) {
                    csrfToken = trimmedCookie.substring('csrftoken='.length, trimmedCookie.length);
                    break;
                }
            }
            return csrfToken;
        }

        const csrfToken = getCSRFToken();


        const reference_signer_id = "{{ reference_signer_id|escapejs }}";
        const reference_id = "{{ reference_id|escapejs }}"
        function handleAction(action) {
            // console.log("reference_signer_id ID:", reference_signer_id);
            // console.log("reference_id ID:", reference_id);
            const language = location.pathname.split("/")[1] || "en";
            var myHeaders = new Headers();
            myHeaders.append("Content-Type", "application/json");
            myHeaders.append("Cookie", document.cookie);
            myHeaders.append("X-CSRFToken", csrfToken)
            let url =`${location.origin}/${language}/api/v1/user-documents/details/${reference_id}/signers/${reference_signer_id}/`
            var raw = JSON.stringify({
                "status":"approved"
            });

            var requestOptions = {
                method: 'PUT',
                headers: myHeaders,
                body: raw,
            };

            fetch(url, requestOptions)
                .then(response => response.json())
                .then(result => {
                    // console.log(result);
                    document.querySelector('.agree').style.display = 'none';
                })
                .catch(error => console.log('error', error));
                }
    </script>
</body>
</html>
