from django.urls import path, re_path

from accounts.views.email_auth_views import (
    DocumentOtpVerifyView,
    EmailOtpVerifyView,
    PasswordChangeView,
    UserLoginView,
    UserRegistrationView,
    UserUpdateView,
    ForgotPasswordView,
    VerifyPasswordResetOTP,
    ResetPasswordView,
    UserVerificationRegistrationView,
    ContactHistoryView,
    EmailChangeVerifyView,
    LoginOtpVerifyView,
)
from accounts.views.mobile_auth_views import (
    DeleteUserFromTrashView,
    InvitedUsersListView,
    InviteUserAPIView,
    MobileSignup,
    SignupWithMobile,
    OrganizationAllUpdateView,
    OrganizationListCreateView,
    OrganizationRetrieveView,
    OrganizationSetPrimaryView,
    OrganizationTrashView,
    MobileNumberChangeVerifyView,
    OrganizationUpdateView,
    UserActiveStatusView,
    OtpOneTimeAcessView,
    OtpVerifyView,
    UserProfileRetrieveUpdateAPIView,
    UserVerifyView,
    LogoutAllDevicesView,
    LogoutView,
    UserUpdateAPIView,
    MobileSignupOtpVerifyView,
    SendOtpAuthenticatedUser,
    VerifyInviteTokenView,
)
from accounts.views.refresh_token_view import RefreshTokenAPIView
from accounts.views.storage_auth_views import (
    StorageAuthView,
)

from .views.bankid_auth_views import (
    BankIDCallbackView,
    CancelAuthenticationView,
    DocumentPreviewView,
    StartAuthenticationView,
    VerifyAuthenticationView,
)
from accounts.views.test_auth_api import TestLoginAPI
from .views import organisation_accounts_views
from .views.organisation_accounts_views import OrganisationsUsersListView

urlpatterns = [

    path(
        "auth/document-otp-verify/<str:email>/",
        DocumentOtpVerifyView.as_view(),
        name="otp-verify",
    ),
    path("auth/signup/", UserRegistrationView.as_view(), name="signup_user"),
    path("auth/verify-register-user/<str:email>/",
         UserVerificationRegistrationView.as_view(),
         name="verify_registeremail",
         ),
    path(
        "auth/verify-email-otp/<str:email>/",
        EmailOtpVerifyView.as_view(),
        name="verify_email",
    ),
    path("auth/singup-final/", UserUpdateView.as_view(), name="signup_user_final"),
    path("auth/login/", UserLoginView.as_view(), name="login_user"),
    path("auth/start/", StartAuthenticationView.as_view(), name="start_authentication"),
    path(
        "auth/verify/",
        VerifyAuthenticationView.as_view(),
        name="verify_authentication",
    ),
    path(
        "auth/bankid-callback/",
        BankIDCallbackView.as_view(),
        name="bankid_callback",
    ),
    path(
        "auth/cancel/", CancelAuthenticationView.as_view(), name="cancel_authentication"
    ),
    # path("auth/send_mail", SendMailToUser.as_view(), name="send_mail"),
    re_path(
        r"^auth/preview_document/(?P<pk>[^/]+)/$",
        DocumentPreviewView.as_view(),
        name="preview_document",
    ),
    path("auth/mobile_authentication/", MobileSignup.as_view(), name="mobile_signup"),
    path("auth/mobile_register_signup/",
         SignupWithMobile.as_view(), name="mobile_register_signup"),
    path(
        "auth/send-otp-authenticated/",
        SendOtpAuthenticatedUser.as_view(),
        name="send_otp_authenticated",
    ),
    path(
        "auth/otp_verify/<str:phone_number>/",
        OtpVerifyView.as_view(),
        name="otp-verify",
    ),
    path(
        "auth/otp-verify-single/<str:phone_number>/",
        OtpOneTimeAcessView.as_view(),
        name="otp-verify-single",
    ),
    path(
        "auth/profile-details/",
        UserProfileRetrieveUpdateAPIView.as_view(),
        name="profile-details",
    ),
    path(
        "auth/user-update/",
        UserUpdateAPIView.as_view(),
        name="user-update",
    ),
    path(
        "auth/user-status/",
        UserActiveStatusView.as_view(),
        name="user-status",
    ),
    path("auth/token/refresh/", RefreshTokenAPIView.as_view(), name="token_refresh"),
    path(
        "auth/organisation-actions/",
        OrganizationListCreateView.as_view(),
        name="organisation",
    ),
    path(
        "auth/organisation-set-default/",
        OrganizationSetPrimaryView.as_view(),
        name="organisation-set-default",
    ),
    path(
        "auth/organisation-activate/",
        OrganizationUpdateView.as_view(),
        name="organisation-activate",
    ),
    path(
        "auth/organisation-trash/",
        OrganizationTrashView.as_view(),
        name="organisation-trash",
    ),
    path(
        "auth/invite-user/",
        InviteUserAPIView.as_view(),
        name="invite-user",
    ),
    # PATCH to this endpoint allows org_superadmin to update invited user profile (first_name, last_name, email, role, phone_number)
    path(
        "auth/verify-user/",
        UserVerifyView.as_view(),
        name="verify-user",
    ),
    path('auth/verify-invite-token/',
         VerifyInviteTokenView.as_view(), name='verify-invite-token'),
    path(
        "auth/invite-user-list/",
        InvitedUsersListView.as_view(),
        name="invite-user-list",
    ), path(
        "auth/organisations-user-list/",
        OrganisationsUsersListView.as_view(),
        name="invite-user-list",
    ),
    path(
        "auth/user-delete/",
        DeleteUserFromTrashView.as_view(),
        name="user-delete",
    ),
    path(
        "auth/organization-update/<str:reference_id>/",
        OrganizationAllUpdateView.as_view(),
        name="organization-update",
    ),
    path(
        "auth/organizations/<str:reference_id>/",
        OrganizationRetrieveView.as_view(),
        name="organization-detail",
    ),
    path("auth/password-reset/",
         PasswordChangeView.as_view(),
         name="password-reset"),

    path("auth/logout/",
         LogoutView.as_view(),
         name="logout"),

    path("auth/logout-all/",
         LogoutAllDevicesView.as_view(),
         name="logout-all"),

    path(
        "auth/organisations-user-list/",
        OrganisationsUsersListView.as_view(),
        name="invite-user-list",
    ),


    path('auth/forgot-password/', ForgotPasswordView.as_view(), name='forgot-password'),
    path('auth/password-reset-otp/', VerifyPasswordResetOTP.as_view(),
         name='verify-password-reset-otp'),
    path('auth/reset-password/', ResetPasswordView.as_view(), name='reset-password'),
    path('test-login/', TestLoginAPI.as_view(), name='test-login'),

    path(
        "organisation/deleted-users/",
        organisation_accounts_views.OrganisationDeletedUsersListView.as_view(),
        name="organisation-deleted-users",
    ),
    path(
        "auth/account-storage/",
        StorageAuthView.as_view(),
        name="account-storage"
    ),
    path('contact-history/', ContactHistoryView.as_view(), name='contact-history'),

    # Self Email|Phone Verify & Change
    path('mobile/change/verify/', MobileNumberChangeVerifyView.as_view(),
         name='mobile-change-verify'),
    path('email/change/verify/', EmailChangeVerifyView.as_view(), name='email-change-verify'),

    path(
        "auth/mobile-signup-verify/<str:phone_number>/",
        MobileSignupOtpVerifyView.as_view(),
        name="mobile-signup-verify",
    ),
    path("auth/login-otp-verify/", LoginOtpVerifyView.as_view(), name="login_otp_verify"),
]
