import os
import smtplib
import logging
from documents.models import UserDocument
from django.utils.translation import gettext as _
from django.conf import settings
from cryptography.fernet import Fernet
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from dotenv import load_dotenv
from email.mime.application import MIMEApplication
from django.utils import translation
from django.template.loader import render_to_string

from celery import shared_task
from accounts.models.users import User
from accounts.services.soft_delete_service import soft_delete_user_and_related

load_dotenv()
# import uuid


# from django.db import transaction


load_dotenv()
# Set up logger


cipher_suite = Fernet(settings.ENCRYPTION_KEY)

# from .services.stripe_service import StripeService


logger = logging.getLogger("app")


def load_html_template(template_path):
    """Load the HTML template from the file"""
    try:
        template_file = os.path.join(
            settings.BASE_DIR, 'documents', 'templates', template_path)
        if not os.path.exists(template_file):
            logger.error(f"Template file not found at: {template_file}")
            return None

        with open(template_file, "r", encoding="utf-8") as f:
            html_template = f.read()
            return html_template
    except FileNotFoundError:
        logger.error(f"HTML template file not found: {template_path}")
        return None
    except Exception as e:
        logger.error(f"Error reading HTML template file: {str(e)}")
        return None


def send_common_email(sender, receiver, subject, html_content, image_info=None, attachments=None):
    """
    Common function to handle email sending with HTML content and image attachments.

    Args:
        sender: Email sender address
        receiver: Email recipient address
        subject: Email subject
        html_content: HTML content of the email
        image_info: List of image filenames to attach (default: None)

    Returns:
        str: Success/Error message
    """
    if image_info is None:
        image_info = ["logo.png", "facebook2x.png",
                      "instagram2x.png", "linkedin2x.png", "twitter2x.png"]

    msg = MIMEMultipart("alternative")
    msg["From"] = sender  # Use the provided sender email
    msg["To"] = receiver
    msg["Subject"] = subject

    # Attach HTML content
    part2 = MIMEText(html_content, "html")
    msg.attach(part2)

    # Attach images
    try:
        for image in image_info:
            file_path = os.path.join('documents', 'images', image)
            with open(file_path, 'rb') as f:
                img = MIMEImage(f.read())
                img.add_header('Content-ID', '<{name}>'.format(name=image))
                img.add_header('Content-Disposition', 'inline', filename=image)
                msg.attach(img)
    except FileNotFoundError:
        logger.error(f"Error: Image file not found at {file_path}")
    except Exception as e:
        logger.error(f"Error attaching images: {str(e)}")

    # Attach files (e.g., PDF certificate)
    if attachments:
        for attachment in attachments:
            filename = attachment["filename"]
            part = MIMEApplication(attachment["content"], Name=filename)
            part["Content-Disposition"] = f'attachment; filename="{filename}"'
            msg.attach(part)

    # Send email
    smtpObj = None
    try:
        logger.info(f"Sending email to {receiver} with subject '{subject}'")
        smtpObj = smtplib.SMTP("smtp.gmail.com", 587)
        smtpObj.starttls()

        # Get email credentials
        email_user = os.getenv("EMAIL_HOST_USER")
        email_password = os.getenv("EMAIL_HOST_PASSWORD")

        if not email_user or not email_password:
            error_msg = "Email credentials not configured properly"
            logger.error(error_msg)
            return error_msg

        smtpObj.login(email_user, email_password)

        # Send email using the same sender email consistently
        smtpObj.sendmail(sender, receiver, msg.as_string())
        logger.info(f"Email sent successfully to {receiver}")
        return "Email sent successfully"
    except smtplib.SMTPAuthenticationError as e:
        error_msg = f"SMTP Authentication Error: {str(e)}. Please check your email credentials and ensure you're using an App Password if 2FA is enabled."
        logger.error(error_msg)
        return error_msg
    except smtplib.SMTPException as e:
        error_msg = f"SMTP Error: unable to send email. Error message: {str(e)}"
        logger.error(error_msg)
        return error_msg
    except Exception as e:
        error_msg = f"Unexpected error while sending email: {str(e)}"
        logger.error(error_msg)
        return error_msg
    finally:
        if smtpObj:
            try:
                smtpObj.quit()
            except:
                pass


def send_document_delete_email(sender, receiver, subject, document, receiver_name):
    document_obj = UserDocument.objects.filter(reference_id=document).first()
    document_title = document_obj.get_decrypted_title()
    document_deleted_by_user = document_obj.deleted_by.get_full_name()
    deleted_at = document_obj.deleted_at.strftime(
        "%Y-%m-%d %H:%M") if document_obj.deleted_at else ""

    template_path = os.path.join('documents', 'email', 'document_expiry.html')
    template_vars = {
        "subject": subject,
        "receiver_name": receiver_name,
        "document_title": document_title,
        "deleted_by": document_deleted_by_user,
        "deleted_at": deleted_at
    }

    html_formatted = render_to_string(template_path, template_vars)

    image_info = ["logo.png", "document-expiry.gif", "facebook2x.png",
                  "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


def send_invite_email(sender, receiver, subject, dynamic_url):
    template_path = os.path.join('documents', 'email', 'invite_user.html')
    html_template = load_html_template(template_path)
    template_vars = {
        "receiver": receiver,
        "dynamic_url": dynamic_url,
    }
    html_formatted = html_template.format(**template_vars)

    image_info = ["logo.png", "join.gif", "facebook2x.png",
                  "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


@shared_task
def send_welcome_user_email(receiver_name, receiver, subject):
    sender = os.getenv("EMAIL_HOST_USER")
    template_path = os.path.join('documents', 'email', 'welcome_email.html')
    
    template_vars = {
        "receiver": receiver_name,
    }
    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["logo.png", "welcome.gif", "join.gif", "facebook2x.png", "instagram2x.png", "linkedin2x.png",
                  "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


def send_welcome_email(sender, receiver_name, receiver, subject):
    template_path = os.path.join('documents', 'email', 'welcome_email.html')
    
    template_vars = {
        "receiver": receiver_name,
    }
    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["logo.png", "welcome.gif", "join.gif", "facebook2x.png",
                  "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


@shared_task
def send_trial_subscription_created_email(receiver, receiver_name, subject):
    sender = os.getenv("EMAIL_HOST_USER")
    template_path = os.path.join('documents', 'email', 'trial_subscription.html')
    
    template_vars = {
        "receiver_name": receiver_name,
        "support_email": "<EMAIL>",
        "start_url": f"{os.getenv('FE_BASE_URL')}/dashboard"
    }
    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["logo.png", "welcome.gif", "facebook2x.png",
                  "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


def send_premium_subscription_created_email(sender, receiver, receiver_name, subject):
    template_path = os.path.join('documents', 'email', 'premium_subscription.html')

    template_vars = {
        "receiver_name": receiver_name,
        "dashboard_url": f"{os.getenv('FE_BASE_URL')}/dashboard",
        "support_email": "<EMAIL>",
    }
    
    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["logo.png", "facebook2x.png",
                  "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


def send_basic_subscription_created_email(sender, receiver, receiver_name, subject):
    template_path = os.path.join('documents', 'email', 'basic_subscription.html')
    html_template = load_html_template(template_path)
    template_vars = {
        "customer_name": receiver_name,
        "plan_name": "Basic Plan",
        "support_email": "<EMAIL>",
        "subject": subject,
    }
    html_formatted = render_to_string(html_template, context=template_vars)

    image_info = ["logo.png", "welcome.gif", "facebook2x.png",
                  "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


def send_enterprise_subscription_created_email(sender, receiver, receiver_name, subject):
    template_path = os.path.join('documents', 'email', 'enterprise.html')
    # html_template = load_html_template(template_path)
    template_vars = {
        "customer_name": receiver_name,
        "support_email": "<EMAIL>",
    }
    # html_formatted = html_template.format(**template_vars)
    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["logo.png", "welcome.gif", "facebook2x.png",
                  "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


def send_reminder_email(sender, receiver, subject, message, dynamic_url, language="en", receiver_name=None, document=None):
    document_obj = UserDocument.objects.filter(reference_id=document).first()
    document_title = document_obj.get_decrypted_title()
    document_user = document_obj.owner.first_name + " " + document_obj.owner.last_name

    template_path = os.path.join('documents', 'email', 'document_sign_reminder.html')
    # html_template = load_html_template(template_path)
    template_vars = {
        "receiver_name": receiver_name,
        "document_title": document_title,
        "document_user": document_user,
        "document_link": dynamic_url,
        "document_subject": _('Pending Document Signing Request'),
        "document_message": _('This is a reminder that you have a pending document that requires your signature. Please review and sign it as soon as possible to avoid any delays.'),
        "document_greeting": _('Dear'),
        "document_details": _('Document Details'),
        "document_name": _('Document Name'),
        "document_button_info": _('Click the button below to review and sign the document securely:'),
        "document_requested_by": _('Requested By'),
        "document_review_sign": _('Review and Sign Document'),
    }
    # html_formatted = html_template.format(**template_vars)
    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["logo.png", "signature-request.gif", "facebook2x.png",
                  "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


def resend_document_email(sender, receiver, subject, document, dynamic_url, receiver_name=None):
    document_obj = UserDocument.objects.filter(reference_id=document).first()
    document_title = document_obj.get_decrypted_title()
    document_user = document_obj.owner.first_name + " " + document_obj.owner.last_name

    template_path = os.path.join('documents', 'email', 'resend_document.html')
    
    template_vars = {
        "receiver_name": receiver_name,
        "document_title": document_title,
        "document_user": document_user,
        "document_link": dynamic_url
    }
    
    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["logo.png", "document-success.gif", "facebook2x.png",
                  "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


def send_approval_email(sender, receiver, subject, document, dynamic_url, receiver_name=None, attachments=None):
    document_obj = UserDocument.objects.filter(reference_id=document).first()
    document_title = document_obj.get_decrypted_title()
    document_user = document_obj.owner.first_name + " " + document_obj.owner.last_name

    template_path = os.path.join('documents', 'email', 'document_sign_confirmed.html')

    template_vars = {
        "receiver_name": receiver_name,
        "document_title": document_title,
        "document_user": document_user,
        "document_link": dynamic_url
    }

    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["logo.png", "document-success.gif", "facebook2x.png",
                  "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info, attachments=attachments)


@shared_task
def send_password_reset_otp_email(receiver, subject, otp, user_name=None):
    sender = os.getenv("EMAIL_HOST_USER")
    template_path = os.path.join('documents', 'email', 'verification_otp.html')
    
    
    template_vars = {
        "receiver": receiver if not user_name else user_name,
        "otp": otp,
    }
    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["password.gif", "logo.png", "signature-request.gif", "facebook2x.png", "instagram2x.png",
                  "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


@shared_task
def send_blocked_account_info_email(receiver, subject):
    sender = os.getenv("EMAIL_HOST_USER")
    template_path = os.path.join('documents', 'email', 'blocked_account.html')

    context = {
        "button_url": f"{os.getenv('FE_BASE_URL')}/forgot_password"
    }
    html_template = render_to_string(template_path, context)

    image_info = ["password.gif", "logo.png", "signature-request.gif", "facebook2x.png", "instagram2x.png",
                  "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_template, image_info)


@shared_task
def send_background_verification_email(receiver, subject, otp):
    sender = os.getenv("EMAIL_HOST_USER")
    template_path = os.path.join('documents', 'email', 'verification_otp.html')
    
    template_vars = {
        "receiver": receiver,
        "otp": otp,
    }
    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["password.gif", "logo.png", "signature-request.gif", "facebook2x.png", "instagram2x.png",
                  "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


@shared_task
def send_register_user_email(receiver, subject, otp):   
    sender = os.getenv("EMAIL_HOST_USER")
    template_path = os.path.join('documents', 'email', 'verification_otp.html')
    
    template_vars = {
        "receiver": "",
        "otp": otp,
    }
    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["password.gif", "logo.png", "signature-request.gif",
                  "facebook2x.png", "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


def send_verification_email(sender, receiver, subject, otp):
    template_path = os.path.join('documents', 'email', 'verification_otp.html')
    
    template_vars = {
        "receiver": receiver,
        "otp": otp,
    }
    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["password.gif", "logo.png", "signature-request.gif",
                  "facebook2x.png", "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


def send_dynamic_email(sender, receiver, subject, dynamic_url, message=None, document=None, receiver_name=None):
    document_obj = UserDocument.objects.filter(reference_id=document).first()
    document_title = document_obj.get_decrypted_title()
    document_user = document_obj.owner.first_name + " " + document_obj.owner.last_name

    template_path = os.path.join('documents', 'email', 'document_sign_email.html')
    
    message_block = ""
    if message:
        message_block = "User Message"
        # message_block = """
        # <td style="font-size: 14px; border: 1px solid #b0b0b0; padding: 10px 20px; font-weight: 600; color: #000;">
        #     User message
        # </td>
        # """

    expiry_msg = ""
    display_status = "none"

    if document_obj and document_obj.expiration_date:
        expiry_msg = f"the document link will be valid until the date {document_obj.expiration_date.strftime('%Y-%m-%d')}"
        display_status = "block"

    if document_obj and getattr(document_obj, "agreement_start_date", None):
        agreement_start_date_value = document_obj.agreement_start_date.strftime(
            "%Y-%m-%d")
    else:
        agreement_start_date_value = "--"

    if document_obj and getattr(document_obj, "agreement_end_date", None):
        agreement_end_date_value = document_obj.agreement_end_date.strftime("%Y-%m-%d")
    else:
        agreement_end_date_value = "--"

    # message_details = ""
    # if message:
        # message_details = f"""<td style="font-size: 14px;text-align: end; padding: 10px 20px;border: 1px solid #b0b0b0;font-weight: 500;color: #666;">{message}</td>"""

    template_vars = {
        "document_subject": _('Document Signing Request'),
        "document_greeting": _('Dear'),
        "receiver_name": receiver_name,
        "document_message": _("You've been requested to review and sign a document via skrivly. Skrivly ensures your signature is secure and legally compliant under the eIDAS Regulation."),
        "message": message if message else "",
        "document_details": _('Document Details'),
        "document_name": _('Document Name'),
        "document_title": document_title,
        "document_requested_by": _('Requested By'),
        "document_user": document_user,
        "user_message_block": message_block if message else "",
        "user_message_details": message if message else "",
        "user_message_display_status": "none" if not message else "contents",
        "agreement_start_date": "Agreement Start Date",
        "agreement_start_date_value": agreement_start_date_value,
        "agreement_end_date": "Agreement End Date",
        "agreement_end_date_value": agreement_end_date_value,
        "document_link": dynamic_url,
        "document_button_info": _('Click the button below to review and sign the document securely:'),
        "document_review_sign": _('Review and Sign Document'),
        "document_expiry_info": _(expiry_msg),
        "display_status": display_status,
    }
    
    html_formatted = render_to_string(template_path, context=template_vars)

    image_info = ["logo.png", "signature-request.gif", "facebook2x.png",
                  "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


def send_trial_expiry_email(sender, receiver, receiver_name, subject, start_url):
    template_path = os.path.join('documents', 'email', 'trial_expiry.html')
    # html_template = load_html_template(template_path)
    template_vars = {
        "receiver_name": receiver_name,
        "start_url": start_url,
        # "support_email": "<EMAIL>"
    }
    html_formatted = render_to_string(template_path, context=template_vars)
    # html_formatted = html_template.format(**template_vars)

    image_info = ["logo.png", "subscription-expiry.gif", "facebook2x.png",
                  "instagram2x.png", "linkedin2x.png", "twitter2x.png"]
    return send_common_email(sender, receiver, subject, html_formatted, image_info)


def send_notification_email(receiver, message, subject="New Event"):
    html = f"""<html>
  <head>
    <style>
      body {{
        font-family: Arial, sans-serif;
        background-color: #f4f4f4;
        margin: 0;
        padding: 0;
        color: #333;
      }}
      .container {{
        width: 80%;
        margin: 20px auto;
        background-color: #ffffff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }}
      .content {{
        font-size: 16px;
        line-height: 1.6;
        color: #555;
      }}
      .footer {{
        margin-top: 30px;
        font-size: 14px;
        color: #777;
      }}
      .footer p {{
        margin: 5px 0;
      }}
      .footer .company-name {{
        font-weight: bold;
        color: #2c3e50;
      }}
    </style>
  </head>
  <body>
    <div class="container">
      <p class="content">Hello,</p>
      <p class="content">{message}</p>
      <br>
      <div class="footer">
        <p class="company-name">Skrivly</p>
      </div>
    </div>
  </body>
</html>"""

    return send_common_email(os.getenv("EMAIL_HOST_USER"), receiver, subject, html)


@shared_task
def send_background_email(
        email,
        receiver_name=None,
        message=None,
        reference_signer_id=None,
        document=None,
        language="en",
        role=None,
        organisation_id=None,
        org_doc_reference_id=None,
        otp=None,
        user_id=None,
        status=None,
        owner_user_id=None,
        payment_link=None,
        verification_token=None,
        verification_uid=None,
        pdf_attachment=None,
        signer_name=None,
        email_subject=None
):
    print(email, document)
    logger.info(
        f"Sending email for document {document} to {email} in {language} language"
    )
    response = None
    # Activate the requested language

    translation.activate(language)
    language = (
        translation.get_language() if translation.get_language() == "sv" else "en"
    )
    if role and organisation_id:
        response = send_invite_email(
            sender=os.getenv("EMAIL_HOST_USER"),
            receiver=email,
            subject="Invite User",
            dynamic_url=(
                f"{os.getenv('FE_BASE_URL')}/create_password/"
                f"?organization_id={organisation_id}&user_id={user_id}&owner_user_id={owner_user_id}&verification_token={verification_token}&verification_uid={verification_uid}"
            ),
        )
    elif otp and status == "email_change_current_otp":
        # receiver, subject, otp, is_current_email=True, user_name=None
        response = send_email_change_verification_email(
            receiver=email,
            subject="Email Change Verification",
            otp=otp,
            is_current_email=True,
            user_name=receiver_name
        )
    elif otp and status == "email_change_new_otp":
        # receiver, subject, otp, is_current_email=True, user_name=None
        response = send_email_change_verification_email(
            receiver=email,
            subject="Email Change Verification",
            otp=otp,
            is_current_email=False,
            user_name=receiver_name
        )
    elif otp and status == "otp_reset":
        response = send_verification_email(
            sender=os.getenv("EMAIL_HOST_USER"),
            receiver=email,
            subject="Password Reset OTP",
            otp=otp,
        )
    elif otp and status == "verify_email":
        response = send_verification_email(
            sender=os.getenv("EMAIL_HOST_USER"),
            receiver=email,
            subject="Verify Email",
            otp=otp,
        )
    elif otp and status == "register_user":
        response = send_verification_email(
            sender=os.getenv("EMAIL_HOST_USER"),
            receiver=email,
            subject="Register User",
            otp=otp,
        )
    elif status == "welcome_email":
        response = send_welcome_email(
            sender=os.getenv("EMAIL_HOST_USER"),
            receiver_name=receiver_name if receiver_name else None,
            receiver=email,
            subject="Your Journey with Skrivly Begins",
        )
    elif otp:
        response = send_verification_email(
            sender=os.getenv("EMAIL_HOST_USER"),
            receiver=email,
            subject="Document Signer",
            otp=otp,
        )
    elif status == "approved" or status == "approved_owner":
        document_reference_id = document.get("reference_id")
        if status == "approved_owner":
            subject_title = f"Your document has been successfully signed by {signer_name}"
            dynamic_url = (
                f"{os.getenv('FE_BASE_URL')}/"
                + f"sign_document/{reference_signer_id}/{user_id}/{document_reference_id}/{org_doc_reference_id}?is_owner=true"
            )
        else:
            subject_title = f"Document Signed"
            dynamic_url = (
                f"{os.getenv('FE_BASE_URL')}/"
                + f"sign_document/{reference_signer_id}/{user_id}/{document_reference_id}/{org_doc_reference_id}?is_owner=false"
            )

        response = send_approval_email(
            sender=os.getenv("EMAIL_HOST_USER"),
            receiver=email,
            receiver_name=receiver_name,
            subject=subject_title,
            document=document_reference_id,
            dynamic_url=dynamic_url,
            attachments=[pdf_attachment] if pdf_attachment else None,
        )
    elif status == "resend_signed":
        document_reference_id = document.get("reference_id")
        dynamic_url = (
            f"{os.getenv('FE_BASE_URL')}/"
            + f"sign_document/{reference_signer_id}/{user_id}/{document_reference_id}/{org_doc_reference_id}?is_owner=false"
        )
        response = resend_document_email(
            sender=os.getenv("EMAIL_HOST_USER"),
            receiver=email,
            receiver_name=receiver_name,
            subject="Document Resend Signed",
            document=document_reference_id,
            dynamic_url=dynamic_url,
        )
    elif status == "Terminate" or status == "Withdrawn":
        response = send_document_delete_email(
            sender=os.getenv("EMAIL_HOST_USER"),
            receiver=email,
            subject=f"Document {status}",
            document=document,
            receiver_name=receiver_name
        )
    elif status == "reminder":
        response = send_reminder_email(
            sender=os.getenv("EMAIL_HOST_USER"),
            receiver=email,
            subject="Reminder",
            message=message,
            dynamic_url=(
                # f"{os.getenv('LOCAL_URL')}/{language}/api/v1/accounts/auth/"
                # + f"preview_document/{str(document)}/"
                    f"{os.getenv('FE_BASE_URL')}/"
                    + f"sign_document/{reference_signer_id}/{user_id}/{document}/{org_doc_reference_id}"
            ),
            language=language,
            document=document,
            receiver_name=receiver_name,
        )
    elif status == "verify":
        response = send_verification_email(
            sender=os.getenv("EMAIL_HOST_USER"),
            receiver=email,
            subject="Document Verify",
            otp=otp,
        )
    else:
        # Use custom subject if provided, otherwise use default
        subject = email_subject if email_subject else "Document Awaiting Your Signature | Skrivly"
        
        response = send_dynamic_email(
            sender=os.getenv("EMAIL_HOST_USER"),
            receiver=email,
            receiver_name=receiver_name,
            subject=subject,
            message=message,
            document=document,
            dynamic_url=(
                    f"{os.getenv('FE_BASE_URL')}/"
                    + f"sign_document/{reference_signer_id}/{user_id}/{document}/{org_doc_reference_id}"
            ),
        )

    translation.deactivate()
    return email, response


def send_email_change_verification_email(receiver, subject, otp, is_current_email=True, user_name=None):
    """
    Send email change verification OTP to either current or new email address.

    Args:
        receiver (str): Email address to send to
        subject (str): Email subject
        otp (str): OTP code
        is_current_email (bool): Whether this is for current email (True) or new email (False)
        user_name (str, optional): User's full name for current email template
    """
    try:
        sender = os.getenv("EMAIL_HOST_USER")
        if not sender:
            logger.error("EMAIL_HOST_USER not configured")
            return "Email configuration error"

        # Choose template based on whether it's current or new email
        template_name = 'email_change_otp.html' if is_current_email else 'new_email_otp.html'
        template_path = os.path.join('documents', 'email', template_name)

        # if not os.path.exists(template_path):
        #     return f"Failed to find the template path with path: {template_path}"

        # Prepare template variables
        template_vars = {
            "otp": otp,
            # Use email username if no name provided
            "user_name": user_name if user_name else receiver.split('@')[0]
        }

        if not is_current_email:
            template_vars = {
                "otp": otp
            }


        html_formatted = render_to_string(template_path, context=template_vars)

        # Common images for both templates
        image_info = ["logo.png", "instagram2x.png", "linkedin2x.png"]

        # Send the email
        result = send_common_email(sender, receiver, subject,
                                   html_formatted, image_info)

        if "success" not in result.lower():
            logger.error(f"Failed to send email: {result}")
            return result

        logger.info(f"Successfully sent email change verification to {receiver}")
        return "Email sent successfully"

    except Exception as e:
        error_msg = f"Unexpected error in send_email_change_verification_email: {str(e)}"
        logger.error(error_msg)
        return error_msg


@shared_task
def send_subscription_confirmation_email_task(user_email, user_name, plan_name, start_date, plan_type, amount_paid):
    """
    Send subscription confirmation email to user.

    Args:
        user_email (str): User's email address
        user_name (str): User's full name
        plan_name (str): Name of the subscription plan
        start_date (str): Start date of the subscription
        plan_type (str): Type of the subscription plan (monthly/yearly)
        amount_paid (str): Amount paid for the subscription
    """
    try:
        sender = os.getenv("EMAIL_HOST_USER")
        if not sender:
            logger.error("EMAIL_HOST_USER not configured")
            return "Email configuration error"

        template_path = os.path.join('documents', 'email', 'new_plan_confirmation.html')
        # html_template = load_html_template(template_path)
        # if not os.path.exists(template_path):
        #     return f"Failed to load email template with path: {template_path}"

        # Prepare template variables
        template_vars = {
            "user_name": user_name,
            "plan_name": plan_name,
            "start_date": start_date,
            # Convert 'monthly' to 'Monthly', 'yearly' to 'Yearly'
            "billing_cycle": plan_type.capitalize(),
            "amount_paid": amount_paid,
            "login_url": f"{os.getenv('FE_BASE_URL')}/login"
        }

        html_formatted = render_to_string(template_path, context=template_vars)
        # Common images for the template
        image_info = ["logo.png", "instagram2x.png", "linkedin2x.png"]

        # Send the email
        subject = f"Welcome to Skrivly - Your {plan_name} is Active!"
        result = send_common_email(sender, user_email, subject,
                                   html_formatted, image_info)

        if "success" not in result.lower():
            logger.error(f"Failed to send email: {result}")
            return result

        logger.info(
            f"Successfully sent subscription confirmation email to {user_email}")
        return "Email sent successfully"

    except Exception as e:
        error_msg = f"Unexpected error in send_subscription_confirmation_email_task: {str(e)}"
        logger.error(error_msg)
        return error_msg


@shared_task
def send_plan_upgrade_confirmation_email_task(user_email, user_first_name, old_plan_name, new_plan_name, upgrade_date, plan_type, amount_charged):
    """
    Send plan upgrade confirmation email to user.

    Args:
        user_email (str): User's email address
        user_first_name (str): User's first name
        old_plan_name (str): Name of the previous plan
        new_plan_name (str): Name of the new plan
        upgrade_date (str): Date of the upgrade
        plan_type (str): Type of the subscription plan (monthly/yearly)
        amount_charged (str): Amount charged for the upgrade
    """
    try:
        sender = os.getenv("EMAIL_HOST_USER")
        if not sender:
            logger.error("EMAIL_HOST_USER not configured")
            return "Email configuration error"

        template_path = os.path.join(
            'documents', 'email', 'plan_upgrade_confirmation.html')
        html_template = load_html_template(template_path)
        if not html_template:
            return "Failed to load email template"

        # Prepare template variables
        template_vars = {
            "user_first_name": user_first_name,
            "old_plan_name": old_plan_name,
            "new_plan_name": new_plan_name,
            "upgrade_date": upgrade_date,
            # Convert 'monthly' to 'Monthly', 'yearly' to 'Yearly'
            "billing_cycle": plan_type.capitalize(),
            "amount_charged": amount_charged,
            "login_url": f"{os.getenv('FE_BASE_URL')}/login"
        }

        try:
            # Manually replace placeholders
            html_formatted = html_template
            for key, value in template_vars.items():
                placeholder = "{" + str(key) + "}"
                html_formatted = html_formatted.replace(placeholder, str(value))

        except Exception as e:
            logger.error(f"Failed to format email template: {str(e)}")
            return f"Template formatting error: {str(e)}"

        # Common images for the template
        image_info = ["logo.png", "instagram2x.png", "linkedin2x.png"]

        # Send the email
        subject = "Your Skrivly Plan Has Been Upgraded Successfully!"
        result = send_common_email(sender, user_email, subject,
                                   html_formatted, image_info)

        if "success" not in result.lower():
            logger.error(f"Failed to send email: {result}")
            return result

        logger.info(
            f"Successfully sent plan upgrade confirmation email to {user_email}")
        return "Email sent successfully"

    except Exception as e:
        error_msg = f"Unexpected error in send_plan_upgrade_confirmation_email_task: {str(e)}"
        logger.error(error_msg)
        return error_msg


@shared_task
def send_plan_downgrade_confirmation_email_task(user_email, user_first_name, old_plan_name, new_plan_name, upgrade_date, plan_type, amount_charged):
    """
    Email users after they downgrade their subscription plan.

    Args:
        user_email (str): The email address of the user
        user_first_name (str): The first name of the user
        old_plan_name (str): The name of the previous plan
        new_plan_name (str): The name of the new plan
        upgrade_date (str): The date when the downgrade takes effect
        plan_type (str): The billing cycle of the plan
        amount_charged (float): The new subscription amount
    """
    try:
        if not os.getenv("EMAIL_HOST_USER"):
            logger.error("Email configuration is missing")
            return "Email configuration is missing"

        sender = os.getenv("EMAIL_HOST_USER")

        # Load the HTML template
        template_path = os.path.join(
            'documents', 'email', 'plan_downgrade_confirmation.html')
        html_template = load_html_template(template_path)
        if not html_template:
            return "Failed to load email template"

        # Prepare template variables
        template_vars = {
            "user_first_name": user_first_name,
            "old_plan_name": old_plan_name,
            "new_plan_name": new_plan_name,
            "effective_date": upgrade_date,
            "billing_cycle": plan_type.capitalize(),
            "new_amount": amount_charged,
            "login_url": f"{os.getenv('FE_BASE_URL')}/login",
        }

        try:
            # Manually replace placeholders
            html_formatted = html_template
            for key, value in template_vars.items():
                placeholder = "{" + str(key) + "}"
                html_formatted = html_formatted.replace(placeholder, str(value))

        except Exception as e:
            logger.error(f"Failed to format email template: {str(e)}")
            return f"Template formatting error: {str(e)}"

        # Common images for the template
        image_info = ["logo.png", "instagram2x.png", "linkedin2x.png"]

        # Send the email
        subject = "Your Skrivly Plan Has Been Downgraded"
        result = send_common_email(sender, user_email, subject,
                                   html_formatted, image_info)

        if "success" not in result.lower():
            logger.error(f"Failed to send email: {result}")
            return result

        logger.info(f"Plan downgrade confirmation email sent to {user_email}")
        return f"Plan downgrade confirmation email sent to {user_email}"

    except Exception as e:
        logger.error(f"Error sending plan downgrade confirmation email: {str(e)}")
        return f"Error sending plan downgrade confirmation email: {str(e)}"


@shared_task
def send_payment_success_confirmation_email_task(user_email, user_first_name, plan_name, payment_date, billing_cycle, amount_paid, invoice_pdf_url):
    """
    Email users after successful payment for their subscription.

    Args:
        user_email (str): The email address of the user
        user_first_name (str): The first name of the user
        plan_name (str): The name of the subscription plan
        payment_date (str): The date of the payment
        billing_cycle (str): The billing cycle of the plan
        amount_paid (float): The amount paid
    """
    try:
        if not os.getenv("EMAIL_HOST_USER"):
            logger.error("Email configuration is missing")
            return "Email configuration is missing"

        sender = os.getenv("EMAIL_HOST_USER")

        # Load the HTML template
        template_path = os.path.join(
            'documents', 'email', 'payment_success_confirmation.html')
        html_template = load_html_template(template_path)
        if not html_template:
            return "Failed to load email template"

        # Prepare template variables
        template_vars = {
            "user_first_name": user_first_name,
            "plan_name": plan_name,
            "payment_date": payment_date,
            "billing_cycle": billing_cycle,
            "amount_paid": amount_paid,
            "login_url": f"{os.getenv('FE_BASE_URL')}/login",
            "invoice_pdf_url": invoice_pdf_url,
        }

        try:
            # Manually replace placeholders
            html_formatted = html_template
            for key, value in template_vars.items():
                placeholder = "{" + str(key) + "}"
                html_formatted = html_formatted.replace(placeholder, str(value))

        except Exception as e:
            logger.error(f"Failed to format email template: {str(e)}")
            return f"Template formatting error: {str(e)}"

        # Common images for the template
        image_info = ["logo.png", "instagram2x.png", "linkedin2x.png"]

        # Send the email
        subject = "Payment Received - Thank You for Choosing Skrivly!"
        result = send_common_email(sender, user_email, subject,
                                   html_formatted, image_info)

        if "success" not in result.lower():
            logger.error(f"Failed to send email: {result}")
            return result

        logger.info(f"Payment success confirmation email sent to {user_email}")
        return f"Payment success confirmation email sent to {user_email}"

    except Exception as e:
        logger.error(f"Error sending payment success confirmation email: {str(e)}")
        return f"Error sending payment success confirmation email: {str(e)}"


@shared_task
def send_plan_cancellation_confirmation_email_task(user_email, user_first_name, plan_name, cancellation_date, amount, last_payment_date, access_end_date,):
    """
    Email users after their subscription has been cancelled.

    Args:
        user_email (str): The email address of the user.
        user_first_name (str): The first name of the user.
        plan_name (str): The name of the cancelled plan.
        cancellation_date (str): The date the subscription was cancelled.
        amount (float): The amount of the last payment.
        last_payment_date (str): The date of the last payment.
        access_end_date (str): The date when access to features ends.
    """
    try:
        if not os.getenv("EMAIL_HOST_USER"):
            logger.error("Email configuration is missing")
            return "Email configuration is missing"

        sender = os.getenv("EMAIL_HOST_USER")
        subject = "Your Skrivly Subscription Has Been Cancelled"

        # Load the HTML template
        template_path = os.path.join(
            'documents', 'email', 'plan_cancellation_confirmation.html'
        )
        html_template = load_html_template(template_path)
        if not html_template:
            return "Failed to load email template"

        # Prepare template variables
        template_vars = {
            "user_first_name": user_first_name,
            "plan_name": plan_name,
            "cancellation_date": cancellation_date,
            "amount": amount,
            "last_payment_date": last_payment_date,
            "access_end_date": access_end_date,
            "login_url": f"{os.getenv('FE_BASE_URL')}/login",
        }

        try:
            # Manually replace placeholders
            html_formatted = html_template
            for key, value in template_vars.items():
                placeholder = "{" + str(key) + "}"
                html_formatted = html_formatted.replace(placeholder, str(value))

        except Exception as e:
            logger.error(f"Failed to format email template: {str(e)}")
            return f"Template formatting error: {str(e)}"

        # Common images for the template
        image_info = ["logo.png", "instagram2x.png", "linkedin2x.png"]

        # Send the email
        result = send_common_email(sender, user_email, subject,
                                   html_formatted, image_info)

        if "success" not in result.lower():
            logger.error(f"Failed to send email: {result}")
            return result

        logger.info(f"Plan cancellation confirmation email sent to {user_email}")
        return f"Plan cancellation confirmation email sent to {user_email}"

    except Exception as e:
        logger.error(f"Error sending plan cancellation confirmation email: {str(e)}")
        return f"Error sending plan cancellation confirmation email: {str(e)}"


@shared_task
def send_new_user_added_confirmation_email_task(new_user_email, new_user_first_name, account_owner_organization_name, user_role, added_by, date_added, organisation_id, user_id, owner_user_id, verification_token, verification_uid):
    """
    Send a confirmation email to a new user who has been added to a Skrivly account.

    Args:
        new_user_email (str): The email address of the newly added user.
        new_user_first_name (str): The first name of the newly added user.
        account_owner_organization_name (str): The name of the account owner or organization.
        user_role (str): The role assigned to the new user.
        added_by (str): The name of the person who added the user.
        date_added (str): The date the user was added.
        auto_generated_password (str): The auto-generated temporary password for the new user.
    """
    try:
        if not os.getenv("EMAIL_HOST_USER"):
            logger.error("Email configuration is missing")
            return "Email configuration is missing"

        sender = os.getenv("EMAIL_HOST_USER")
        subject = "You've Been Added to a Skrivly Account"

        template_path = os.path.join(
            'documents', 'email', 'new_user_added_confirmation.html'
        )

        dynamic_url = (
            f"{os.getenv('FE_BASE_URL')}/create_password/"
            f"?organization_id={organisation_id}&user_id={user_id}&owner_user_id={owner_user_id}&verification_token={verification_token}&verification_uid={verification_uid}"
        )

        template_vars = {
            "new_user_first_name": new_user_first_name,
            "account_owner_organization_name": account_owner_organization_name,
            "user_role": user_role,
            "added_by": added_by,
            "date_added": date_added,
            "user_email": new_user_email,
            "dynamic_url": dynamic_url,
        }

        html_formatted = render_to_string(template_path, context=template_vars)
        image_info = ["logo.png", "instagram2x.png", "linkedin2x.png"]

        result = send_common_email(sender, new_user_email,
                                   subject, html_formatted, image_info)

        if "success" not in result.lower():
            logger.error(f"Failed to send email: {result}")
            return result

        logger.info(f"New user added confirmation email sent to {new_user_email}")
        return f"New user added confirmation email sent to {new_user_email}"

    except Exception as e:
        logger.error(f"Error sending new user added confirmation email: {str(e)}")
        return f"Error sending new user added confirmation email: {str(e)}"


@shared_task
def send_user_removed_from_organization_email_task(user_email, user_first_name, organization_or_account_owner_name, organization_name, name_of_admin_or_account_owner, removal_date, user_role):
    """
    Send an email to a user after they have been removed from an organization.

    Args:
        user_email (str): The email address of the removed user.
        user_first_name (str): The first name of the removed user.
        organization_or_account_owner_name (str): The name of the organization or account owner.
        organization_name (str): The name of the organization from which the user was removed.
        name_of_admin_or_account_owner (str): The name of the admin or account owner who performed the removal.
        removal_date (str): The date of the user's removal.
        user_role (str): The previous role of the removed user in the organization.
    """
    try:
        if not os.getenv("EMAIL_HOST_USER"):
            logger.error("Email configuration is missing")
            return "Email configuration is missing"

        sender = os.getenv("EMAIL_HOST_USER")
        subject = "You've Been Removed from a Skrivly Account"

        template_path = os.path.join(
            'documents', 'email', 'user_removed_from_organization.html'
        )
        html_template = load_html_template(template_path)
        if not html_template:
            return "Failed to load email template"

        template_vars = {
            "user_first_name": user_first_name,
            "organization_or_account_owner_name": organization_or_account_owner_name,
            "organization_name": organization_name,
            "name_of_admin_or_account_owner": name_of_admin_or_account_owner,
            "removal_date": removal_date,
            "user_role": user_role,
        }

        try:
            html_formatted = html_template
            for key, value in template_vars.items():
                placeholder = "{" + str(key) + "}"
                html_formatted = html_formatted.replace(placeholder, str(value))
        except Exception as e:
            logger.error(f"Failed to format email template: {str(e)}")
            return f"Template formatting error: {str(e)}"

        image_info = ["logo.png", "instagram2x.png", "linkedin2x.png"]

        result = send_common_email(sender, user_email, subject,
                                   html_formatted, image_info)

        if "success" not in result.lower():
            logger.error(f"Failed to send email: {result}")
            return result

        logger.info(f"User removed from organization email sent to {user_email}")
        return f"User removed from organization email sent to {user_email}"

    except Exception as e:
        logger.error(f"Error sending user removed from organization email: {str(e)}")
        return f"Error sending user removed from organization email: {str(e)}"


@shared_task
def send_admin_account_deleted_email_task(user_email, user_first_name, account_role, name_of_user_admin, deletion_date):
    """
    Send an email to an admin user after their account has been permanently deleted.

    Args:
        user_email (str): The email address of the deleted user.
        user_first_name (str): The first name of the deleted user.
        account_role (str): The role of the deleted account (e.g., "org_superadmin").
        name_of_user_admin (str): The name of the admin who performed the deletion.
        deletion_date (str): The date of the account's deletion.
    """
    try:
        if not os.getenv("EMAIL_HOST_USER"):
            logger.error("Email configuration is missing")
            return "Email configuration is missing"

        sender = os.getenv("EMAIL_HOST_USER")
        subject = "Your Skrivly Account Has Been Deleted"

        template_path = os.path.join(
            'documents', 'email', 'admin_account_deleted.html'
        )
        html_template = load_html_template(template_path)
        if not html_template:
            return "Failed to load email template"

        template_vars = {
            "user_first_name": user_first_name,
            "user_email": user_email,
            "account_role": account_role,
            "name_of_user_admin": name_of_user_admin,
            "deletion_date": deletion_date,
        }

        html_formatted = render_to_string(template_path, context=template_vars)

        image_info = ["logo.png", "instagram2x.png", "linkedin2x.png"]

        result = send_common_email(sender, user_email, subject,
                                   html_formatted, image_info)

        if "success" not in result.lower():
            logger.error(f"Failed to send email: {result}")
            return result

        logger.info(f"Admin account deleted email sent to {user_email}")
        return f"Admin account deleted email sent to {user_email}"

    except Exception as e:
        logger.error(f"Error sending admin account deleted email: {str(e)}")
        return f"Error sending admin account deleted email: {str(e)}"


@shared_task
def send_user_profile_updated_email_task(user_email, user_first_name, updated_by, update_date, changed_fields, ip_address, device_browser, location):
    """
    Send an email to a user after their profile has been updated.

    Args:
        user_email (str): The email address of the user whose profile was updated.
        user_first_name (str): The first name of the user whose profile was updated.
        updated_by (str): The name of the user who performed the update.
        update_date (str): The date of the profile update.
        changed_fields (dict): Dictionary of fields that were changed with old and new values.
        ip_address (str): The IP address from which the update was made.
        device_browser (str): The device/browser used for the update.
        location (str): The approximate location from where the update was made.
    """
    try:
        if not os.getenv("EMAIL_HOST_USER"):
            logger.error("Email configuration is missing")
            return "Email configuration is missing"

        sender = os.getenv("EMAIL_HOST_USER")
        subject = "Your Skrivly Profile Has Been Updated"

        template_path = os.path.join(
            'documents', 'email', 'user_profile_updated.html'
        )
        
        # Use Django's render_to_string for proper template rendering
        from django.template.loader import render_to_string
        
        template_vars = {
            "user_first_name": user_first_name,
            "updated_by": updated_by,
            "update_date": update_date,
            "changed_fields": changed_fields,
            "ip_address": ip_address,
            "device_browser": device_browser,
            "location": location,
        }

        try:
            html_formatted = render_to_string(template_path, context=template_vars)
        except Exception as e:
            logger.error(f"Failed to render email template: {str(e)}")
            return f"Template rendering error: {str(e)}"

        image_info = ["logo.png", "instagram2x.png", "linkedin2x.png"]

        result = send_common_email(sender, user_email, subject,
                                   html_formatted, image_info)

        if "success" not in result.lower():
            logger.error(f"Failed to send email: {result}")
            return result

        logger.info(f"User profile updated email sent to {user_email}")
        return f"User profile updated email sent to {user_email}"

    except Exception as e:
        logger.error(f"Error sending user profile updated email: {str(e)}")
        return f"Error sending user profile updated email: {str(e)}"


@shared_task
def soft_delete_user_task(email, deleted_by_id=None):
    deleted_by = User.objects.filter(
        id=deleted_by_id).first() if deleted_by_id else None
    soft_delete_user_and_related(email, deleted_by=deleted_by)
