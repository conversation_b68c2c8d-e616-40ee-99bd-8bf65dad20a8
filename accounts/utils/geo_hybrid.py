#!/usr/bin/env python3
"""
Hybrid Geo Location System
Uses both local databases and API services as fallback
"""

import os
import logging
import requests
from typing import Dict, Any, Optional
from django.conf import settings

logger = logging.getLogger(__name__)

# Configuration
USE_API_FALLBACK = getattr(settings, 'USE_GEO_API_FALLBACK', True)
GEO_API_URL = getattr(settings, 'GEO_API_URL', 'http://ip-api.com/json/{ip}')
GEO_API_TIMEOUT = getattr(settings, 'GEO_API_TIMEOUT', 5)

# Database paths
GEOIP_DB_PATH = os.path.join(settings.BASE_DIR, "geoip", "GeoLite2-City.mmdb")
GEOIP_ASN_DB_PATH = os.path.join(settings.BASE_DIR, "geoip", "GeoLite2-ASN.mmdb")


def get_location_from_database(ip_address: str) -> Optional[Dict[str, Any]]:
    """
    Get location from local GeoLite2-City.mmdb database.
    """
    if not os.path.exists(GEOIP_DB_PATH):
        logger.warning(f"City database not found at {GEOIP_DB_PATH}")
        return None
    
    try:
        import geoip2.database
        
        with geoip2.database.Reader(GEOIP_DB_PATH) as reader:
            response = reader.city(ip_address)
            
            return {
                'city': response.city.name,
                'state': response.subdivisions.most_specific.name if response.subdivisions.most_specific else None,
                'country': response.country.name,
                'country_code': response.country.iso_code,
                'postal_code': response.postal.code,
                'latitude': response.location.latitude,
                'longitude': response.location.longitude,
                'timezone': response.location.time_zone,
                'source': 'local_database'
            }
            
    except Exception as e:
        logger.error(f"Error reading from local database: {str(e)}")
        return None


def get_location_from_api(ip_address: str) -> Optional[Dict[str, Any]]:
    """
    Get location from API service (fallback method).
    """
    if not USE_API_FALLBACK:
        return None
    
    try:
        url = GEO_API_URL.format(ip=ip_address)
        response = requests.get(url, timeout=GEO_API_TIMEOUT)
        response.raise_for_status()
        
        data = response.json()
        
        # Handle different API response formats
        if 'status' in data and data['status'] == 'success':
            # ip-api.com format
            return {
                'city': data.get('city'),
                'state': data.get('regionName'),
                'country': data.get('country'),
                'country_code': data.get('countryCode'),
                'postal_code': data.get('zip'),
                'latitude': data.get('lat'),
                'longitude': data.get('lon'),
                'timezone': data.get('timezone'),
                'isp': data.get('isp'),
                'source': 'api_service'
            }
        else:
            # Other API formats
            return {
                'city': data.get('city') or data.get('locality'),
                'state': data.get('region') or data.get('state'),
                'country': data.get('country') or data.get('country_name'),
                'country_code': data.get('country_code'),
                'postal_code': data.get('postal_code') or data.get('zip'),
                'latitude': data.get('latitude') or data.get('lat'),
                'longitude': data.get('longitude') or data.get('lon'),
                'timezone': data.get('timezone'),
                'source': 'api_service'
            }
            
    except Exception as e:
        logger.error(f"Error getting location from API: {str(e)}")
        return None


def get_location_from_ip(ip_address: str) -> str:
    """
    Get location using hybrid approach: local database first, then API fallback.
    """
    # Try local database first
    location_data = get_location_from_database(ip_address)
    
    if location_data:
        # Format location string
        parts = []
        if location_data.get('city'):
            parts.append(location_data['city'])
        if location_data.get('state'):
            parts.append(location_data['state'])
        if location_data.get('country'):
            parts.append(location_data['country'])
        
        if parts:
            return ", ".join(parts)
    
    # Fallback to API if enabled
    if USE_API_FALLBACK:
        location_data = get_location_from_api(ip_address)
        if location_data:
            parts = []
            if location_data.get('city'):
                parts.append(location_data['city'])
            if location_data.get('state'):
                parts.append(location_data['state'])
            if location_data.get('country'):
                parts.append(location_data['country'])
            
            if parts:
                return ", ".join(parts) + " (API)"
    
    return "Unknown"


def get_comprehensive_ip_info(ip_address: str) -> Dict[str, Any]:
    """
    Get comprehensive IP information using all available methods.
    """
    result = {
        'ip': ip_address,
        'location': 'Unknown',
        'network_info': 'Unknown',
        'source': 'none',
        'methods_used': []
    }
    
    # Try local database for location
    location_data = get_location_from_database(ip_address)
    if location_data:
        result['location'] = f"{location_data.get('city', '')}, {location_data.get('state', '')}, {location_data.get('country', '')}".strip(', ')
        result['source'] = 'local_database'
        result['methods_used'].append('local_database')
        result['location_details'] = location_data
    
    # Try API fallback for location
    elif USE_API_FALLBACK:
        location_data = get_location_from_api(ip_address)
        if location_data:
            result['location'] = f"{location_data.get('city', '')}, {location_data.get('state', '')}, {location_data.get('country', '')}".strip(', ')
            result['source'] = 'api_service'
            result['methods_used'].append('api_service')
            result['location_details'] = str(location_data)
    
    return result
