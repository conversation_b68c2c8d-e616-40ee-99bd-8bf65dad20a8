import stripe
from django.conf import settings
import os
from dotenv import load_dotenv
load_dotenv()

stripe.api_key = settings.STRIPE_SECRET_KEY


def create_stripe_product_and_price(name, description, unit_amount, currency, interval, lookup_key=None):
    """
    Create a Stripe product and its associated price
    Similar to the example's create_product and create_price functions
    """
    try:
        # Create Stripe Product
        stripe_product = stripe.Product.create(
            name=name,
            description=description,
            metadata={'lookup_key': lookup_key} if lookup_key else {}
        )

        # Create Stripe Price
        price_data = {
            'product': stripe_product.id,
            'unit_amount': unit_amount,
            'currency': currency,
            'recurring': {
                'interval': interval
            }
        }

        if lookup_key:
            price_data['lookup_key'] = lookup_key

        stripe_price = stripe.Price.create(**price_data)
        return stripe_product, stripe_price

    except stripe.error.StripeError as e:
        raise Exception(f"Failed to create Stripe product/price: {str(e)}")


def get_or_create_customer(user):
    """
    Get an existing Stripe customer or create a new one
    """
    if user.stripe_customer_id:
        try:
            # Try to fetch existing customer
            customer = stripe.Customer.retrieve(user.stripe_customer_id)
            return customer
        except stripe.error.InvalidRequestError:
            # Customer doesn't exist in Stripe, create new one
            pass

    # Create new customer
    customer = stripe.Customer.create(
        email=user.get_decrypted_email(),
        name=f"{user.get_full_name()}",
        metadata={'user_id': str(user.id)}
    )

    # Save customer ID to user model
    user.stripe_customer_id = customer.id
    user.save()

    return customer


def create_subscription(user, stripe_price_id, payment_method_id=None):
    """
    Create a Stripe subscription using an existing price ID
    """
    try:
        # Get or create customer
        customer = get_or_create_customer(user)

        # Attach payment method if provided
        if payment_method_id:
            stripe.PaymentMethod.attach(
                payment_method_id,
                customer=customer.id
            )

            # Set as default payment method
            stripe.Customer.modify(
                customer.id,
                invoice_settings={
                    'default_payment_method': payment_method_id
                }
            )

        # Create subscription
        subscription = stripe.Subscription.create(
            customer=customer.id,
            items=[{'price': stripe_price_id}],
            payment_behavior='default_incomplete',
            expand=['latest_invoice.payment_intent']
        )

        return subscription

    except stripe.error.StripeError as e:
        raise Exception(f"Failed to create subscription: {str(e)}")


def get_price_by_lookup_key(lookup_key):
    """
    Retrieve a Stripe price using its lookup key
    """
    try:
        prices = stripe.Price.list(lookup_keys=[lookup_key], active=True)
        return prices.data[0] if prices.data else None
    except stripe.error.StripeError as e:
        raise Exception(f"Failed to retrieve price: {str(e)}")


def get_price(price_details):
    """
    Format price details for Stripe checkout session
    """
    return {
        'price': price_details['price_id'],
        'quantity': price_details.get('quantity', 1)
    }


def create_checkout_session(user, sub_details):
    """
    Create a Stripe checkout session for subscription

    Args:
        user: User object
        sub_details: List of dictionaries containing price and quantity
                    [{
                        'price': 'price_H...',
                        'quantity': 1
                    }]
    """
    try:
        customer = get_or_create_customer(user)

        checkout_session = stripe.checkout.Session.create(
            customer=customer.id,
            payment_method_types=['card'],
            line_items=sub_details,  # Stripe expects this format directly
            mode='subscription',
            success_url=f"{os.getenv('FE_BASE_URL')}/payment/success/?session_id={{CHECKOUT_SESSION_ID}}",
            cancel_url=f"{os.getenv('FE_BASE_URL')}/payment/cancel/",
        )
        return checkout_session
    except Exception as e:
        raise Exception(f"Error creating checkout session: {str(e)}")


def cancel_subscription(subscription_id, cancel_at_period_end=True):
    """
    Cancel a subscription either immediately or at the end of the billing period

    Args:
        subscription_id: The Stripe subscription ID
        cancel_at_period_end: If True, cancel at period end. If False, cancel immediately
    """
    try:
        if cancel_at_period_end:
            # Cancel at period end
            canceled_subscription = stripe.Subscription.modify(
                subscription_id,
                cancel_at_period_end=True
            )
        else:
            # Cancel immediately
            canceled_subscription = stripe.Subscription.delete(subscription_id)

        return canceled_subscription

    except stripe.error.StripeError as e:
        print(f"Stripe error: {str(e)}")
        raise
    except Exception as e:
        print(f"Error canceling subscription: {str(e)}")
        raise
