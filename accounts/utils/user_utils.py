from typing import Optional, Dict, Union
from django.db.models import Q

from accounts.models import User, Organization
from accounts.services.jwt_authentication.authentication import fernet_decrypt
from subscriptions.models import UserSubscription


def get_user_by_identifier(identifier: str) -> Optional[User]:
    """
    Get user by email, reference_id, or user_id
    """
    try:
        # Try to find by reference_id first
        user = User.objects.filter(reference_id=identifier).first()
        if user:
            return user

        # Try to find by id
        try:
            user = User.objects.filter(id=identifier).first()
            if user:
                return user
        except ValueError:
            pass

        # Try to find by email
        user = User.objects.filter(private_email=identifier).first()
        return user

    except Exception as e:
        print("Error fetching user by identifier")

        return None


def get_user_organization(user: User) -> Optional[Organization]:
    """
    Get organization based on user role
    - For org_superadmin: returns active primary organization
    - For other roles: returns their associated organization
    """
    try:
        if not user:
            return None

        if user.role == "org_superadmin":
            return user.organizations.filter(
                is_primary=True,
                is_active=True
            ).first()
        else:
            return user.organizations.filter(is_active=True).first()

    except Exception as e:
        print("Error fetching user by identifier")
        return None


def get_user_details(identifier: str) -> Dict[str, Union[str, bool, dict]]:
    """
    Get complete user details including organization info
    """
    try:
        user = get_user_by_identifier(identifier)
        if not user:
            return {}

        organization = get_user_organization(user)

        user_details = {
            "user_details": {
                "reference_id": user.reference_id,
                "id": user.id,
                "first_name": user.first_name if user.first_name else None,
                "username": user.username if user.username else None,
                "last_name": user.last_name if user.last_name else None,
                "email": fernet_decrypt(user.email) if user.email else None,
                "phone_number": fernet_decrypt(user.phone_number) if user.phone_number else None,
                "role": user.role,
                "is_active": user.is_active,
            }
        }

        if organization:
            user_details["organisation_details"] = {
                "id": organization.id if organization.id else None,
                "name": organization.name if organization.name else None,
                "number": organization.number if organization.number else None,
                "reference_id": organization.reference_id if organization.reference_id else None,
                "is_primary": organization.is_primary if organization.is_primary else None,
                "created_by": organization.created_by if organization.created_by else None,
                "is_active": organization.is_active
            }

        return user_details

    except Exception as e:
        print("Error fetching user by identifier")
        return {}


def get_user_organisation_subscription(reference_id: str):
    """
    Get organization owner details based on user reference ID.

    Args:
        reference_id (str): Reference ID of the user

    Returns:
        dict: Dictionary containing organization owner details and organization info
        None: If no user or organization found
    """
    try:
        # Get the user based on reference ID
        user = User.objects.filter(reference_id=reference_id).first()
        if not user:
            return None

        # Get the user's organization
        organization = get_user_organization(user)
        if not organization:
            return None

        # Get the organization owner (original creator)
        org_owner = organization.get_organization_owner()
        if not org_owner:
            return None
        owner_subscription = UserSubscription.objects.filter(
            user=org_owner, status='active').first()
        # Return owner and organization details
        return owner_subscription
    except Exception as e:
        print(f"Error getting organization subscription details: {str(e)}")
        return None
