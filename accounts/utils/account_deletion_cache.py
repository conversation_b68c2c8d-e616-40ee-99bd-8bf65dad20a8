# accounts/utils/account_deletion_cache.py

from django.conf import settings
from django.core.cache import cache

DELETION_FLAG_TIMEOUT = getattr(
    settings, "DELETION_FLAG_TIMEOUT", 7200)  # fallback to 1 hour


def set_deletion_in_progress(email):
    """Set a cache flag indicating deletion is in progress for this email."""
    cache.set(f"deletion_in_progress:{email}", True, timeout=DELETION_FLAG_TIMEOUT)


def clear_deletion_in_progress(email):
    """Remove the deletion-in-progress flag for this email."""
    cache.delete(f"deletion_in_progress:{email}")


def is_deletion_in_progress(email):
    """Check if deletion is in progress for this email."""
    return cache.get(f"deletion_in_progress:{email}") is True
