from unittest.mock import patch

from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase


class CancelAuthenticationTest(APITestCase):
    def setUp(self):
        self.url = reverse("cancel_authentication")
        self.valid_data = {
            "verification_order_id": "131daac9-16c6-4618-beb0-365768f37288"
        }
        self.invalid_data = {"verification_order_id": ""}

    @patch(
        "accounts.services.bankid_authentication.bankid_client_service."
        "BankIDClientService.cancel_authentication"
    )
    def test_cancel_authentication_success(self, mock_cancel_auth):
        # Mocking the response from the cancel_authentication
        mock_cancel_auth.return_value = {}

        response = self.client.post(self.url, self.valid_data, format="json")

        # Asserting the correct behavior
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {})

    @patch(
        "accounts.services.bankid_authentication.bankid_client_service."
        "BankIDClientService.cancel_authentication"
    )
    def test_cancel_authentication_invalid_data(self, mock_cancel_auth):
        response = self.client.post(self.url, self.invalid_data, format="json")

        # No need to mock `cancel_authentication` since the data is invalid
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("verification_order_id", response.data)
