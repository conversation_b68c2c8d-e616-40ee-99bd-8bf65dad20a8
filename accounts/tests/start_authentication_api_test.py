import io
import uuid
from unittest.mock import MagicMock, patch

from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from accounts.serializers.bankid_auth_serializers import AuthSerializer


class TestStartAuthenticationView(APITestCase):
    def setUp(self):
        self.url = reverse("start_authentication")
        self.valid_data = {"end_user_ip": "***************"}
        self.invalid_data = {"end_user_ip": ""}
        self.without_json = {}

    @patch("accounts.services.bankid_authentication.bankid_client")
    @patch("accounts.services.bankid_authentication.bankid_utils.generate_qr_code")
    def test_qr_code_generation_success(self, mock_generate_qr_code, mock_start_auth):
        # Mock the return value of start_authentication
        mock_start_auth.return_value = {
            "orderRef": "131daac9-16c6-4618-beb0-365768f37288",
            "autoStartToken": "7c40b5c9-fa74-49cf-b98c-bfe651f9a7c6",
            "qrStartToken": "67df3917-fa0d-44e5-b327-edcc928297f8",
            "qrStartSecret": "d28db9a7-4cde-429e-a983-359be676944c",
        }

        # Mock the QR code generation
        mock_img = MagicMock()
        buffer = io.BytesIO()
        mock_img.save = MagicMock()
        buffer.write(b"fake image data")
        buffer.seek(0)
        mock_img.save.return_value = None
        mock_generate_qr_code.return_value = mock_img

        response = self.client.post(self.url, self.valid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("qr_code", response.data)
        self.assertIn("verification_order_id", response.data)
        self.assertIn("websocket_url", response.data)

        self.assertTrue(
            uuid.UUID(response.data.get("verification_order_id"), version=4)
        )
        self.assertTrue(
            response.data.get("websocket_url").startswith("ws://127.0.0.1:8000/ws/qr/")
        )
        self.assertTrue(
            response.data.get("websocket_url").endswith(
                response.data["verification_order_id"] + "/"
            )
        )
        self.assertTrue(response.data["qr_code"].startswith("data:image/png;base64"))

    @patch("accounts.services.bankid_authentication.bankid_client")
    def test_in_device_flow_success(self, mock_bankid_client):
        # Mock the sign_document function
        mock_bankid_client.sign_document.return_value = {
            "orderRef": "131daac9-16c6-4618-beb0-365768f37288",
            "autoStartToken": "7c40b5c9-fa74-49cf-b98c-bfe651f9a7c6",
            "qrStartToken": "67df3917-fa0d-44e5-b327-edcc928297f8",
            "qrStartSecret": "d28db9a7-4cde-429e-a983-359be676944c",
        }

        # Test data for in-device flow
        test_data = {
            "end_user_ip": "***************",
            "document_content": "test-doc-123",
            "flow_type": "in_device",
            "device_type": "mobile",
            "return_url": "https://example.com/callback"
        }

        response = self.client.post(self.url, test_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("verification_order_id", response.data)
        self.assertIn("autostart_url", response.data)
        self.assertIn("flow_type", response.data)
        
        # Check flow type
        self.assertEqual(response.data["flow_type"], "in_device")
        
        # Check autostart URL format for mobile
        autostart_url = response.data["autostart_url"]
        self.assertTrue(autostart_url.startswith("https://app.bankid.com/"))
        self.assertIn("autostarttoken=", autostart_url)
        self.assertIn("redirect=", autostart_url)

    @patch("accounts.services.bankid_authentication.bankid_client")
    def test_in_device_flow_desktop_success(self, mock_bankid_client):
        # Mock the sign_document function
        mock_bankid_client.sign_document.return_value = {
            "orderRef": "131daac9-16c6-4618-beb0-365768f37288",
            "autoStartToken": "7c40b5c9-fa74-49cf-b98c-bfe651f9a7c6",
            "qrStartToken": "67df3917-fa0d-44e5-b327-edcc928297f8",
            "qrStartSecret": "d28db9a7-4cde-429e-a983-359be676944c",
        }

        # Test data for in-device flow on desktop
        test_data = {
            "end_user_ip": "***************",
            "document_content": "test-doc-123",
            "flow_type": "in_device",
            "device_type": "desktop",
            "return_url": "https://example.com/callback"
        }

        response = self.client.post(self.url, test_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("autostart_url", response.data)
        
        # Check autostart URL format for desktop
        autostart_url = response.data["autostart_url"]
        self.assertTrue(autostart_url.startswith("bankid:///"))
        self.assertIn("autostarttoken=", autostart_url)
        self.assertIn("redirect=", autostart_url)

    @patch("accounts.services.bankid_authentication.bankid_client")
    def test_qr_flow_with_document_success(self, mock_bankid_client):
        # Mock the sign_document function
        mock_bankid_client.sign_document.return_value = {
            "orderRef": "131daac9-16c6-4618-beb0-365768f37288",
            "autoStartToken": "7c40b5c9-fa74-49cf-b98c-bfe651f9a7c6",
            "qrStartToken": "67df3917-fa0d-44e5-b327-edcc928297f8",
            "qrStartSecret": "d28db9a7-4cde-429e-a983-359be676944c",
        }

        # Test data for QR flow with document
        test_data = {
            "end_user_ip": "***************",
            "document_content": "test-doc-123",
            "flow_type": "qr",
            "device_type": "desktop"
        }

        response = self.client.post(self.url, test_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("verification_order_id", response.data)
        self.assertIn("websocket_url", response.data)
        self.assertIn("flow_type", response.data)
        
        # Check flow type
        self.assertEqual(response.data["flow_type"], "qr")
        
        # Check websocket URL format
        websocket_url = response.data["websocket_url"]
        self.assertTrue(websocket_url.startswith("ws://"))
        self.assertIn("/ws/qr/", websocket_url)

    @patch("accounts.services.bankid_authentication.bankid_client")
    def test_in_device_flow_missing_autostart_token(self, mock_bankid_client):
        # Mock the sign_document function to return response without autoStartToken
        mock_bankid_client.sign_document.return_value = {
            "orderRef": "131daac9-16c6-4618-beb0-365768f37288",
            "qrStartToken": "67df3917-fa0d-44e5-b327-edcc928297f8",
            "qrStartSecret": "d28db9a7-4cde-429e-a983-359be676944c",
        }

        # Test data for in-device flow
        test_data = {
            "end_user_ip": "***************",
            "document_content": "test-doc-123",
            "flow_type": "in_device",
            "device_type": "mobile",
            "return_url": "https://example.com/callback"
        }

        response = self.client.post(self.url, test_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("AutoStartToken not found in response", str(response.data))

    def test_device_type_detection_mobile(self):
        # Test mobile device detection
        test_data = {
            "end_user_ip": "***************",
            "device_type": "mobile"
        }

        response = self.client.post(
            self.url, 
            test_data, 
            format="json",
            HTTP_USER_AGENT="Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15"
        )

        # Should not fail due to device type detection
        self.assertNotEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_device_type_detection_desktop(self):
        # Test desktop device detection
        test_data = {
            "end_user_ip": "***************",
            "device_type": "desktop"
        }

        response = self.client.post(
            self.url, 
            test_data, 
            format="json",
            HTTP_USER_AGENT="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        )

        # Should not fail due to device type detection
        self.assertNotEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @patch("accounts.services.bankid_authentication.bankid_client")
    def test_invalid_data_in_serializer(self, mock_start_auth):
        # Simulate invalid serializer data
        response = self.client.post(self.url, self.invalid_data, format="json")

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("end_user_ip", response.data)

    def test_with_no_data(self):
        response = self.client.post(self.url, self.without_json, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.data, {"end_user_ip": ["This field is required."]})

    def test_authuserserializer_validate_data(self):
        validate_data = {"end_user_ip": "127.0.0.1"}
        serializer = AuthSerializer(
            data=validate_data
        )  # Need to provide data for validation
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data["end_user_ip"], "127.0.0.1")

    def test_authuserserializer_in_validate_data(self):
        invalid_data = {"end_user_ip": "127...1"}
        serializer = AuthSerializer(
            data=invalid_data
        )  # Need to provide data for validation
        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            serializer.errors, {"end_user_ip": ["Enter a valid IPv4 or IPv6 address."]}
        )

    def test_authuserserializer_flow_type_validation(self):
        # Test valid flow types
        valid_data = {
            "end_user_ip": "127.0.0.1",
            "flow_type": "qr"
        }
        serializer = AuthSerializer(data=valid_data)
        self.assertTrue(serializer.is_valid())

        valid_data["flow_type"] = "in_device"
        serializer = AuthSerializer(data=valid_data)
        self.assertTrue(serializer.is_valid())

        # Test invalid flow type
        invalid_data = {
            "end_user_ip": "127.0.0.1",
            "flow_type": "invalid"
        }
        serializer = AuthSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn("flow_type", serializer.errors)

    def test_authuserserializer_device_type_validation(self):
        # Test valid device types
        valid_data = {
            "end_user_ip": "127.0.0.1",
            "device_type": "mobile"
        }
        serializer = AuthSerializer(data=valid_data)
        self.assertTrue(serializer.is_valid())

        valid_data["device_type"] = "desktop"
        serializer = AuthSerializer(data=valid_data)
        self.assertTrue(serializer.is_valid())

        # Test invalid device type
        invalid_data = {
            "end_user_ip": "127.0.0.1",
            "device_type": "invalid"
        }
        serializer = AuthSerializer(data=invalid_data)
        self.assertFalse(serializer.is_valid())
        self.assertIn("device_type", serializer.errors)
