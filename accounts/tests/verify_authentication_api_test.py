from unittest.mock import patch

from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from accounts.models import User


class VerifyAuthenticationViewTest(APITestCase):
    fixtures = ["accounts/tests/fixtures/user_data_fixture.json"]

    def setUp(self):
        self.url = reverse("verify_authentication", kwargs={"language": "en"})
        self.valid_payload = {
            "verification_order_id": "131daac9-16c6-4618-beb0-365768f37288"
        }
        self.invalid_payload = {"verification_order_id": ""}
        self.patcher = patch(
            "accounts.services.bankid_authentication.bankid_client_service."
            "BankIDClientService.verify_authentication"
        )
        self.mock_verify_authentication = self.patcher.start()

    def tearDown(self):
        self.patcher.stop()

    def test_post_with_valid_data_and_new_user(self):
        """Test case for valid data where the user does not already exist."""
        response_data = {
            "status": "complete",
            "completionData": {
                "user": {
                    "personalNumber": "************",
                    "name": "<PERSON>",
                    "givenName": "<PERSON>",
                    "surname": "<PERSON><PERSON>",
                },
                "device": {"ipAddress": "***********"},
                "bankIdIssueDate": "2020-02-01",
                "signature": "<base64-encoded data>",
                "ocspResponse": "<base64-encoded data>",
            },
        }
        self.mock_verify_authentication.return_value = response_data

        response = self.client.post(self.url, self.valid_payload, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(User.objects.filter(signature="<base64-encoded data>").exists())

    def test_post_with_invalid_data(self):
        """Test case for invalid data (missing verification_order_id)."""
        response = self.client.post(self.url, self.invalid_payload, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_post_with_existing_user(self):
        """Test case where the user already exists in the database."""
        response_data = {
            "status": "complete",
            "completionData": {
                "user": {
                    "personalNumber": "************",
                    "name": "Karl Karlsson",
                    "givenName": "Karl",
                    "surname": "Karlsson",
                },
                "device": {"ipAddress": "***********"},
                "bankIdIssueDate": "2020-02-01",
                "signature": "<base64-encoded data>",
                "ocspResponse": "<base64-encoded data>",
            },
        }
        self.mock_verify_authentication.return_value = response_data

        response = self.client.post(self.url, self.valid_payload, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(User.objects.count(), 1)  # Ensure no new user is created

    def test_post_with_incomplete_status(self):
        """Test case for a response that indicates the status is not complete."""
        response_data = {"status": "pending", "hintCode": "outstandingTransaction"}
        self.mock_verify_authentication.return_value = response_data

        response = self.client.post(self.url, self.valid_payload, format="json")
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertIn("status", response.data)
