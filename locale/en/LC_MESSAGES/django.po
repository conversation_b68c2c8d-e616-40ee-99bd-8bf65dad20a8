# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-02 04:53+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: accounts/middleware/internationalization.py:23
msgid "Unsupported language code."
msgstr ""

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Väntande begäran om dokumentsignering"
msgstr "Pending Document Signing Request"

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Granska och signera dokument"
msgstr "Review and Sign Document"

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Klicka på knappen nedan för att granska och signera dokumentet på ett säkert sätt:"
msgstr "Click the button below to review and sign the document securely:"

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Begärd av"
msgstr "Requested By"

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Dokumentnamn"
msgstr "Document Name"

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Dokumentdetaljer"
msgstr "Document Details"

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Begäran om dokumentsignering"
msgstr "Document Signing Request"


#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Kära"
msgstr "Dear"

#: accounts/services/bankid_authentication/bankid_utils.py

msgid "Du har ett nytt dokument som väntar på din underskrift. Vänligen granska och underteckna dokumentet så snart som möjligt."
msgstr "You’ve been requested to review and sign a document via skrivly. Skrivly ensures your signature is secure and legally compliant under the eIDAS Regulation."


#: accounts/serializers/email_auth_serializers.py:24
msgid "Both email and password are required."
msgstr ""


#: accounts/serializers/email_auth_serializers.py:32
msgid "Invalid credentials. Please try again."
msgstr ""

#: accounts/serializers/email_auth_serializers.py:60
msgid "Passwords must match"
msgstr ""

#: accounts/serializers/email_auth_serializers.py:64
msgid "A user with this email address already exists."
msgstr ""

#: accounts/services/jwt_authentication/account_jwt_verifier.py:27
#: documents/utils/jwt_verifier.py:30
msgid "Authorization token missing"
msgstr ""

#: accounts/services/jwt_authentication/account_jwt_verifier.py:31
#: documents/utils/jwt_verifier.py:34
msgid "Invalid authorization header"
msgstr ""

#: accounts/services/jwt_authentication/account_jwt_verifier.py:49
#: documents/utils/jwt_verifier.py:52 documents/utils/jwt_verifier.py:114
#: documents/utils/jwt_verifier.py:173 documents/utils/jwt_verifier.py:188
msgid "User not found"
msgstr ""

#: accounts/services/jwt_authentication/account_jwt_verifier.py:57
#: documents/utils/jwt_verifier.py:59 documents/utils/jwt_verifier.py:110
#: documents/utils/jwt_verifier.py:183
msgid "Token has expired"
msgstr ""

#: accounts/services/jwt_authentication/account_jwt_verifier.py:60
#: documents/utils/jwt_verifier.py:62 documents/utils/jwt_verifier.py:107
#: documents/utils/jwt_verifier.py:112 documents/utils/jwt_verifier.py:186
msgid "Invalid token"
msgstr ""

#: accounts/services/jwt_authentication/authentication.py:88
#: accounts/views/refresh_token_view.py:57
msgid "Refresh token expired."
msgstr ""

#: accounts/services/mobile_authentication/mobile_clients.py:24
msgid "OTP sent successfully!"
msgstr ""

#: accounts/services/mobile_authentication/mobile_clients.py:31
msgid "Failed to send OTP"
msgstr ""

#: accounts/views/bankid_auth_views.py:73
msgid "Invalid response for QR code creation"
msgstr ""

#: accounts/views/bankid_auth_views.py:118
msgid "Authentication failed."
msgstr ""

#: accounts/views/bankid_auth_views.py:192
msgid "Unable to cancel Order reference Id."
msgstr ""

#: accounts/views/bankid_auth_views.py:198
msgid "Cancellation of the order reference ID was successful."
msgstr ""

#: accounts/views/email_auth_views.py:23
msgid "User registered successfully!"
msgstr ""

#: accounts/views/email_auth_views.py:48
msgid "Login successful."
msgstr ""

#: accounts/views/mobile_auth_views.py:65
msgid "OTP sent and user created"
msgstr ""

#: accounts/views/mobile_auth_views.py:73
#, fuzzy
#| msgid "OTP uppdaterad och skickad igen"
msgid "OTP updated and resent"
msgstr "OTP updated and resent"

#: accounts/views/mobile_auth_views.py:113
#: accounts/views/mobile_auth_views.py:176
msgid "The OTP has expired. Please request a new one and try again."
msgstr ""

#: accounts/views/mobile_auth_views.py:125
#: accounts/views/mobile_auth_views.py:188
msgid "This OTP has already been used. Please request a new one."
msgstr ""

#: accounts/views/refresh_token_view.py:25
msgid "Refresh token required."
msgstr ""

#: accounts/views/refresh_token_view.py:37
#, fuzzy
#| msgid "Ogiltig token-typ."
msgid "Invalid token type."
msgstr "Invalid token type."

#: accounts/views/refresh_token_view.py:41
msgid "User ID not found in token."
msgstr ""

#: accounts/views/subscription_auth_view.py:116
#: documents/views/document_views.py:540 documents/views/document_views.py:823
#: documents/views/document_views.py:873 documents/views/document_views.py:913
#, fuzzy
#| msgid "Databasens integritetsfel."
msgid "Database integrity error."
msgstr "Database integrity error."

#: documents/utils/jwt_verifier.py:75 documents/utils/jwt_verifier.py:147
msgid "Authorization token missing or invalid"
msgstr ""

#: documents/utils/jwt_verifier.py:117
msgid "Invalid last_login format in token"
msgstr ""

#: documents/utils/jwt_verifier.py:156
msgid "This is not a one-time access token"
msgstr ""

#: documents/utils/jwt_verifier.py:161
msgid "Token is blacklisted and cannot be used again"
msgstr ""

#: documents/utils/jwt_verifier.py:192
msgid "Invalid login format in token"
msgstr ""

#: documents/views/document_views.py:294
msgid "Document not found."
msgstr ""

#: documents/views/document_views.py:308
msgid "Signer not found."
msgstr ""

#: documents/views/document_views.py:486
#, fuzzy
#| msgid "Dokument hittades inte."
msgid "Document file is required."
msgstr "Document not found."

#: documents/views/document_views.py:704
msgid "Document successfully deleted."
msgstr ""

#: documents/views/document_views.py:791
msgid "This email address is already assigned to this document"
msgstr ""

msgid "Hej,"
msgstr "Hello,"

msgid "Vi har skapat ett nytt dokument för dig att granska."
msgstr "We have generated a new document for you to review."

msgid "Klicka på länken nedan för att förhandsgranska dokumentet:"
msgstr "Click the link below to preview the document:"

msgid "Förhandsgranska dokument"
msgstr "Preview Document"

msgid ""
"Om du inte kan komma åt länken, kopiera och klistra in följande URL i din "
"webbläsare:"
msgstr ""
"If you cannot access the link, copy and paste the following URL into your "
"browser:"

msgid "Med vänliga hälsningar,"
msgstr "Best regards,"

msgid "Aviox Technologies Pvt Ltd"
msgstr "Aviox Technologies Pvt Ltd"

msgid "E-signera dokument"
msgstr "Esign Document"

msgid "Internt serverfel"
msgstr "Internal Server Error"

msgid "Lagringsinformation hämtades framgångsrikt"
msgstr "Storage information retrieved successfully"

#~ msgid "misslyckades"
#~ msgstr "failed"

#~ msgid "Dokument för underskrift"
#~ msgstr "Esign Document"

#~ msgid "Starta din BankID-app."
#~ msgstr "Start your BankID app."

#~ msgid "BankID-appen är inte installerad. Vänligen kontakta din bank."
#~ msgstr "The BankID app is not installed. Please contact your bank."

#~ msgid "Åtgärden avbröts. Försök igen."
#~ msgstr "Action cancelled. Please try again."

#~ msgid ""
#~ "En identifiering eller signering för detta personnummer har redan "
#~ "påbörjats. Försök igen."
#~ msgstr ""
#~ "An identification or signing for this personal number is already started. "
#~ "Please try again."

#~ msgid "Internt fel. Försök igen."
#~ msgstr "Internal error. Please try again."

#~ msgid "Åtgärden avbröts."
#~ msgstr "Action cancelled."

#~ msgid ""
#~ "BankID-appen svarar inte. Kontrollera att den är igång och att du har "
#~ "internetåtkomst. Om du inte har ett giltigt BankID kan du få ett från din "
#~ "bank. Försök igen."
#~ msgstr ""
#~ "The BankID app is not responding. Please check that it’s started and that "
#~ "you have internet access. If you don’t have a valid BankID you can get "
#~ "one from your bank. Try again."

#~ msgid "Ange din säkerhetskod i BankID-appen och välj Identifiera/Signera."
#~ msgstr "Enter your security code in BankID app and select Identify/Sign."

#~ msgid "Försöker starta din BankID-app."
#~ msgstr "Trying to start your BankID app."

#~ msgid ""
#~ "Söker efter BankID, det kan ta lite tid... Om några sekunder har gått och "
#~ "inget BankID har hittats, har du förmodligen inte ett BankID som kan "
#~ "användas för denna identifiering/signering på den här datorn. Om du har "
#~ "ett BankID-kort, sätt in det i din kortläsare. Om du inte har ett BankID "
#~ "kan du få ett från din bank. Om du har ett BankID på en annan enhet kan "
#~ "du starta BankID-appen på den enheten."
#~ msgstr ""
#~ "Searching for BankID, it may take a little while … If a few seconds have "
#~ "passed and still no BankID has been found, you probably don’t have a "
#~ "BankID which can be used for this identification/signing on this "
#~ "computer. If you have a BankID card, please insert it into your card "
#~ "reader. If you don’t have a BankID you can get one from your bank. If you "
#~ "have a BankID on another device you can start the BankID app on that "
#~ "device."

#~ msgid ""
#~ "BankID:et du försöker använda är blockerat eller för gammalt. Vänligen "
#~ "använd ett annat BankID eller skaffa ett nytt från din bank."
#~ msgstr ""
#~ "The BankID you are trying to use is blocked or too old. Please use "
#~ "another BankID or get a new one from your bank."

#~ msgid ""
#~ "BankID-appen kunde inte hittas på din dator eller mobila enhet. "
#~ "Installera det och skaffa ett BankID från din bank. Installera appen från "
#~ "din appbutik eller https://install.bankid.com."
#~ msgstr ""
#~ "The BankID app couldn’t be found on your computer or mobile device. "
#~ "Please install it and get a BankID from your bank. Install the app from "
#~ "your app store or https://install.bankid.com."

#~ msgid ""
#~ "Kunde inte skanna QR-koden. Starta BankID-appen och skanna QR-koden. Om "
#~ "du inte har ett giltigt BankID, skaffa ett från din bank."
#~ msgstr ""
#~ "Failed to scan the QR code. Start the BankID app and scan the QR code. If "
#~ "you don’t have a valid BankID, get one from your bank."

#~ msgid "Starta BankID-appen."
#~ msgstr "Start the BankID app."

#~ msgid ""
#~ "BankID-appen är inte startad. Starta din BankID-app eller skanna QR-koden."
#~ msgstr ""
#~ "The BankID app is not started. Start your BankID app or scan the QR code."

#~ msgid "Vill du använda BankID på den här enheten eller en annan enhet?"
#~ msgstr "Do you want to use BankID on this device or another device?"

#~ msgid "Ange din säkerhetskod i BankID-appen."
#~ msgstr "Enter your security code in the BankID app."

#~ msgid "BankID-appen svarade inte i tid. Starta appen och försök igen."
#~ msgstr ""
#~ "The BankID app didn’t respond in time. Please start the app and try again."

#~ msgid ""
#~ "Certifikatet som används för BankID är inte giltigt. Kontakta din bank."
#~ msgstr ""
#~ "The certificate used for BankID is not valid. Please contact your bank."

#~ msgid "Ett internt fel uppstod. Försök igen senare eller kontakta support."
#~ msgstr ""
#~ "There was an internal error. Please try again later or contact support."

#~ msgid "Data skapades framgångsrikt"
#~ msgstr "Data created successfully"

#~ msgid "Misslyckades med att skapa data"
#~ msgstr "Failed to create data"

#~ msgid "Data uppdaterades framgångsrikt"
#~ msgstr "Data updated successfully"

#~ msgid "Misslyckades med att uppdatera data"
#~ msgstr "Failed to update data"

#~ msgid "Data raderades framgångsrikt"
#~ msgstr "Data deleted successfully"

#~ msgid "Misslyckades med att radera data"
#~ msgstr "Failed to delete data"

#~ msgid "Data hämtades framgångsrikt"
#~ msgstr "Data retrieved successfully"

#~ msgid "Data hittades inte"
#~ msgstr "Data not found"

#~ msgid "Obehörig åtkomst"
#~ msgstr "Unauthorized access"

#~ msgid "Otillåten åtkomst"
#~ msgstr "Forbidden access"

#~ msgid "Valideringsfel"
#~ msgstr "Validation error"

#~ msgid "Okänd åtgärd"
#~ msgstr "Unknown action"

#~ msgid "framgång"
#~ msgstr "success"

#~ msgid "komplett"
#~ msgstr "complete"

#~ msgid "väntande"
#~ msgstr "pending"

#~ msgid "Kan inte avbryta orderreferens-ID."
#~ msgstr "Unable to cancel Order reference Id."

#~ msgid "Avbokningen av orderreferens-ID lyckades."
#~ msgstr "Cancellation of the order reference ID was successful."

#~ msgid "Autentiseringen misslyckades."
#~ msgstr "Authentication failed."

#~ msgid "Ogiltigt svar för skapande av QR-kod"
#~ msgstr "Invalid response for QR code creation"

#~ msgid "Något gick fel!!"
#~ msgstr "Something went Wrong!!"

#~ msgid "Ej stöd för språkets kod."
#~ msgstr "Unsupported language code."

#~ msgid "Data hämtades framgångsrikt."
#~ msgstr "Data fetched successfully."

#~ msgid "Ogiltigt UUID."
#~ msgstr "Invalid UUID."

#~ msgid "Ogiltigt datumformat. Använd ÅÅÅÅ-MM-DD."
#~ msgstr "Invalid date format. Use YYYY-MM-DD."

#~ msgid "Undertecknare hittades inte."
#~ msgstr "Signer not found."

#~ msgid "Dokumentet har tagits bort framgångsrikt."
#~ msgstr "Document successfully deleted."

#~ msgid "Denna e-postadress är redan tilldelad detta dokument"
#~ msgstr "This email address is already assigned to this document"

#~ msgid "Användar- och dokumentfält är obligatoriska."
#~ msgstr "User and Document fields are required."

#~ msgid "Det gick inte att skicka OTP"
#~ msgstr "Failed to send OTP"

#~ msgid "OTP skickad och användare skapad"
#~ msgstr "OTP sent and user created"

#~ msgid "OTP:n har gått ut. Vänligen begär en ny och försök igen."
#~ msgstr "The OTP has expired. Please request a new one and try again."

#~ msgid "Denna OTP har redan använts. Vänligen begär en ny."
#~ msgstr "This OTP has already been used. Please request a new one."

#~ msgid "Återställningstoken har löpt ut."
#~ msgstr "Refresh token expired."

#~ msgid "Användar-ID kunde inte hittas i token."
#~ msgstr "User ID not found in token."

#~ msgid "Återställningstoken krävs."
#~ msgstr "Refresh token required."
