# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-02 04:53+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: accounts/middleware/internationalization.py:23
msgid "Unsupported language code."
msgstr "Ej stöd för språkets kod."

#: accounts/serializers/email_auth_serializers.py:24
msgid "Both email and password are required."
msgstr ""

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "This is a reminder that you have a pending document that requires your signature. Please review and sign it as soon as possible to avoid any delays"
msgstr "Detta är en påminnelse om att du har ett väntande dokument som kräver din underskrift. Vänligen granska och underteckna den så snart som möjligt för att undvika förseningar"

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Pending Document Signing Request"
msgstr "Väntande begäran om dokumentsignering"

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Review and Sign Document"
msgstr "Granska och signera dokument"

#: accounts/services/bankid_authentication/bankid_utils.py

msgid "Click the button below to review and sign the document securely:"
msgstr "Klicka på knappen nedan för att granska och signera dokumentet på ett säkert sätt:"


#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Requested By"
msgstr "Begärd av"

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Document Name"
msgstr "Dokumentnamn"

#: accounts/services/bankid_authentication/bankid_utils.py

msgid "Document Details"
msgstr "Dokumentdetaljer"

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Document Signing Request"
msgstr "Begäran om dokumentsignering"

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "Dear"
msgstr "Kära"

#: accounts/services/bankid_authentication/bankid_utils.py
msgid "You’ve been requested to review and sign a document via skrivly. Skrivly ensures your signature is secure and legally compliant under the eIDAS Regulation."
msgstr "Du har ett nytt dokument som väntar på din underskrift. Vänligen granska och underteckna dokumentet så snart som möjligt."

#: accounts/serializers/email_auth_serializers.py:32
#, fuzzy
#| msgid "Internal error. Please try again."
msgid "Invalid credentials. Please try again."
msgstr "Internt fel. Försök igen."

#: accounts/serializers/email_auth_serializers.py:60
msgid "Passwords must match"
msgstr ""

#: accounts/serializers/email_auth_serializers.py:64
msgid "A user with this email address already exists."
msgstr ""

#: accounts/services/jwt_authentication/account_jwt_verifier.py:27
#: documents/utils/jwt_verifier.py:30
msgid "Authorization token missing"
msgstr "Authorization token saknas"

#: accounts/services/jwt_authentication/account_jwt_verifier.py:31
#: documents/utils/jwt_verifier.py:34
msgid "Invalid authorization header"
msgstr "Ogiltig auktoriseringshuvud"

#: accounts/services/jwt_authentication/account_jwt_verifier.py:49
#: documents/utils/jwt_verifier.py:52 documents/utils/jwt_verifier.py:114
#: documents/utils/jwt_verifier.py:173 documents/utils/jwt_verifier.py:188
msgid "User not found"
msgstr "Användare hittades inte"

#: accounts/services/jwt_authentication/account_jwt_verifier.py:57
#: documents/utils/jwt_verifier.py:59 documents/utils/jwt_verifier.py:110
#: documents/utils/jwt_verifier.py:183
msgid "Token has expired"
msgstr "Token har löpt ut"

#: accounts/services/jwt_authentication/account_jwt_verifier.py:60
#: documents/utils/jwt_verifier.py:62 documents/utils/jwt_verifier.py:107
#: documents/utils/jwt_verifier.py:112 documents/utils/jwt_verifier.py:186
msgid "Invalid token"
msgstr "Ogiltig token"

#: accounts/services/jwt_authentication/authentication.py:88
#: accounts/views/refresh_token_view.py:57
msgid "Refresh token expired."
msgstr "Återställningstoken har löpt ut."

#: accounts/services/mobile_authentication/mobile_clients.py:24
#, fuzzy
#| msgid "Data created successfully"
msgid "OTP sent successfully!"
msgstr "Data skapades framgångsrikt"

#: accounts/services/mobile_authentication/mobile_clients.py:31
msgid "Failed to send OTP"
msgstr "Det gick inte att skicka OTP"

#: accounts/views/bankid_auth_views.py:73
msgid "Invalid response for QR code creation"
msgstr "Ogiltigt svar för skapande av QR-kod"

#: accounts/views/bankid_auth_views.py:118
msgid "Authentication failed."
msgstr "Autentiseringen misslyckades."

#: accounts/views/bankid_auth_views.py:192
msgid "Unable to cancel Order reference Id."
msgstr "Kan inte avbryta orderreferens-ID."

#: accounts/views/bankid_auth_views.py:198
msgid "Cancellation of the order reference ID was successful."
msgstr "Avbokningen av orderreferens-ID lyckades."

#: accounts/views/email_auth_views.py:23
#, fuzzy
#| msgid "Data retrieved successfully"
msgid "User registered successfully!"
msgstr "Data hämtades framgångsrikt"

#: accounts/views/email_auth_views.py:48
msgid "Login successful."
msgstr ""

#: accounts/views/mobile_auth_views.py:65
msgid "OTP sent and user created"
msgstr "OTP skickad och användare skapad"

#: accounts/views/mobile_auth_views.py:73
msgid "OTP updated and resent"
msgstr "OTP uppdaterad och skickad igen"

#: accounts/views/mobile_auth_views.py:113
#: accounts/views/mobile_auth_views.py:176
msgid "The OTP has expired. Please request a new one and try again."
msgstr "OTP:n har gått ut. Vänligen begär en ny och försök igen."

#: accounts/views/mobile_auth_views.py:125
#: accounts/views/mobile_auth_views.py:188
msgid "This OTP has already been used. Please request a new one."
msgstr "Denna OTP har redan använts. Vänligen begär en ny."

#: accounts/views/refresh_token_view.py:25
msgid "Refresh token required."
msgstr "Återställningstoken krävs."

#: accounts/views/refresh_token_view.py:37
msgid "Invalid token type."
msgstr "Ogiltig token-typ."

#: accounts/views/refresh_token_view.py:41
msgid "User ID not found in token."
msgstr "Användar-ID kunde inte hittas i token."

#: accounts/views/subscription_auth_view.py:116
#: documents/views/document_views.py:540 documents/views/document_views.py:823
#: documents/views/document_views.py:873 documents/views/document_views.py:913
msgid "Database integrity error."
msgstr "Databasens integritetsfel."

#: documents/utils/jwt_verifier.py:75 documents/utils/jwt_verifier.py:147
#, fuzzy
#| msgid "Authorization token missing"
msgid "Authorization token missing or invalid"
msgstr "Authorization token saknas"

#: documents/utils/jwt_verifier.py:117
msgid "Invalid last_login format in token"
msgstr ""

#: documents/utils/jwt_verifier.py:156
msgid "This is not a one-time access token"
msgstr ""

#: documents/utils/jwt_verifier.py:161
msgid "Token is blacklisted and cannot be used again"
msgstr ""

#: documents/utils/jwt_verifier.py:192
#, fuzzy
#| msgid "Invalid token"
msgid "Invalid login format in token"
msgstr "Ogiltig token"

#: documents/views/document_views.py:294
msgid "Document not found."
msgstr "Dokument hittades inte."

#: documents/views/document_views.py:308
msgid "Signer not found."
msgstr "Undertecknare hittades inte."

#: documents/views/document_views.py:486
#, fuzzy
#| msgid "User and Document fields are required."
msgid "Document file is required."
msgstr "Användar- och dokumentfält är obligatoriska."

#: documents/views/document_views.py:704
msgid "Document successfully deleted."
msgstr "Dokumentet har tagits bort framgångsrikt."

#: documents/views/document_views.py:791
msgid "This email address is already assigned to this document"
msgstr "Denna e-postadress är redan tilldelad detta dokument"

msgid "Hello,"
msgstr "Hej,"

msgid "We have generated a new document for you to review."
msgstr "Vi har skapat ett nytt dokument för dig att granska."

msgid "Click the link below to preview the document:"
msgstr "Klicka på länken nedan för att förhandsgranska dokumentet:"

msgid "Preview Document"
msgstr "Förhandsgranska dokument"

msgid ""
"If you cannot access the link, copy and paste the following URL into your "
"browser:"
msgstr ""
"Om du inte kan komma åt länken, kopiera och klistra in följande URL i din "
"webbläsare:"

msgid "Best regards,"
msgstr "Med vänliga hälsningar,"

msgid "Aviox Technologies Pvt Ltd"
msgstr "Aviox Technologies Pvt Ltd"

msgid "Esign Document"
msgstr "E-signera dokument"

msgid "Internal Server Error"
msgstr "Internt serverfel"

msgid "Storage information retrieved successfully"
msgstr "Lagringsinformation hämtades framgångsrikt"

#~ msgid "failed"
#~ msgstr "misslyckades"

#~ msgid "Start your BankID app."
#~ msgstr "Starta din BankID-app."

#~ msgid "The BankID app is not installed. Please contact your bank."
#~ msgstr "BankID-appen är inte installerad. Vänligen kontakta din bank."

#~ msgid "Action cancelled. Please try again."
#~ msgstr "Åtgärden avbröts. Försök igen."

#~ msgid ""
#~ "An identification or signing for this personal number is already started. "
#~ "Please try again."
#~ msgstr ""
#~ "En identifiering eller signering för detta personnummer har redan "
#~ "påbörjats. Försök igen."

#~ msgid "Action cancelled."
#~ msgstr "Åtgärden avbröts."

#~ msgid ""
#~ "The BankID app is not responding. Please check that it’s started and that "
#~ "you have internet access. If you don’t have a valid BankID you can get "
#~ "one from your bank. Try again."
#~ msgstr ""
#~ "BankID-appen svarar inte. Kontrollera att den är igång och att du har "
#~ "internetåtkomst. Om du inte har ett giltigt BankID kan du få ett från din "
#~ "bank. Försök igen."

#~ msgid "Enter your security code in BankID app and select Identify/Sign."
#~ msgstr "Ange din säkerhetskod i BankID-appen och välj Identifiera/Signera."

#~ msgid "Trying to start your BankID app."
#~ msgstr "Försöker starta din BankID-app."

#~ msgid ""
#~ "Searching for BankID, it may take a little while … If a few seconds have "
#~ "passed and still no BankID has been found, you probably don’t have a "
#~ "BankID which can be used for this identification/signing on this "
#~ "computer. If you have a BankID card, please insert it into your card "
#~ "reader. If you don’t have a BankID you can get one from your bank. If you "
#~ "have a BankID on another device you can start the BankID app on that "
#~ "device."
#~ msgstr ""
#~ "Söker efter BankID, det kan ta lite tid... Om några sekunder har gått och "
#~ "inget BankID har hittats, har du förmodligen inte ett BankID som kan "
#~ "användas för denna identifiering/signering på den här datorn. Om du har "
#~ "ett BankID-kort, sätt in det i din kortläsare. Om du inte har ett BankID "
#~ "kan du få ett från din bank. Om du har ett BankID på en annan enhet kan "
#~ "du starta BankID-appen på den enheten."

#~ msgid ""
#~ "The BankID you are trying to use is blocked or too old. Please use "
#~ "another BankID or get a new one from your bank."
#~ msgstr ""
#~ "BankID:et du försöker använda är blockerat eller för gammalt. Vänligen "
#~ "använd ett annat BankID eller skaffa ett nytt från din bank."

#~ msgid ""
#~ "The BankID app couldn’t be found on your computer or mobile device. "
#~ "Please install it and get a BankID from your bank. Install the app from "
#~ "your app store or https://install.bankid.com."
#~ msgstr ""
#~ "BankID-appen kunde inte hittas på din dator eller mobila enhet. "
#~ "Installera det och skaffa ett BankID från din bank. Installera appen från "
#~ "din appbutik eller https://install.bankid.com."

#~ msgid ""
#~ "Failed to scan the QR code. Start the BankID app and scan the QR code. If "
#~ "you don’t have a valid BankID, get one from your bank."
#~ msgstr ""
#~ "Kunde inte skanna QR-koden. Starta BankID-appen och skanna QR-koden. Om "
#~ "du inte har ett giltigt BankID, skaffa ett från din bank."

#~ msgid "Start the BankID app."
#~ msgstr "Starta BankID-appen."

#~ msgid ""
#~ "The BankID app is not started. Start your BankID app or scan the QR code."
#~ msgstr ""
#~ "BankID-appen är inte startad. Starta din BankID-app eller skanna QR-koden."

#~ msgid "Do you want to use BankID on this device or another device?"
#~ msgstr "Vill du använda BankID på den här enheten eller en annan enhet?"

#~ msgid "Enter your security code in the BankID app."
#~ msgstr "Ange din säkerhetskod i BankID-appen."

#~ msgid ""
#~ "The BankID app didn’t respond in time. Please start the app and try again."
#~ msgstr "BankID-appen svarade inte i tid. Starta appen och försök igen."

#~ msgid ""
#~ "The certificate used for BankID is not valid. Please contact your bank."
#~ msgstr ""
#~ "Certifikatet som används för BankID är inte giltigt. Kontakta din bank."

#~ msgid ""
#~ "There was an internal error. Please try again later or contact support."
#~ msgstr "Ett internt fel uppstod. Försök igen senare eller kontakta support."

#~ msgid "Failed to create data"
#~ msgstr "Misslyckades med att skapa data"

#~ msgid "Data updated successfully"
#~ msgstr "Data uppdaterades framgångsrikt"

#~ msgid "Failed to update data"
#~ msgstr "Misslyckades med att uppdatera data"

#~ msgid "Data deleted successfully"
#~ msgstr "Data raderades framgångsrikt"

#~ msgid "Failed to delete data"
#~ msgstr "Misslyckades med att radera data"

#~ msgid "Data not found"
#~ msgstr "Data hittades inte"

#~ msgid "Unauthorized access"
#~ msgstr "Obehörig åtkomst"

#~ msgid "Forbidden access"
#~ msgstr "Otillåten åtkomst"

#~ msgid "Internal server error"
#~ msgstr "Internt serverfel"

#~ msgid "Validation error"
#~ msgstr "Valideringsfel"

#~ msgid "Unknown action"
#~ msgstr "Okänd åtgärd"

#~ msgid "success"
#~ msgstr "framgång"

#~ msgid "complete"
#~ msgstr "komplett"

#~ msgid "pending"
#~ msgstr "väntande"

#~ msgid "Something went Wrong!!"
#~ msgstr "Något gick fel!!"

#~ msgid "Data fetched successfully"
#~ msgstr "Data hämtades framgångsrikt."

#~ msgid "Invalid UUID."
#~ msgstr "Ogiltigt UUID."

#~ msgid "Invalid date format. Use YYYY-MM-DD."
#~ msgstr "Ogiltigt datumformat. Använd ÅÅÅÅ-MM-DD."

#~ msgid "Data retrieved successfully."
#~ msgstr "Data hämtades framgångsrikt."
